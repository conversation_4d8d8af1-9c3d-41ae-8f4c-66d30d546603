@extends('super_admin.layouts.app')

@section('title', 'Announcements Management')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Announcements Management</h1>
                    <p class="text-muted mb-0">Manage system announcements and maintenance notifications</p>
                </div>
                <div>
                    <a href="{{ route('super.announcements.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create Announcement
                    </a>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ $stats['total'] }}</h4>
                                    <p class="mb-0">Total Announcements</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-bullhorn fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ $stats['active'] }}</h4>
                                    <p class="mb-0">Active</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ $stats['scheduled'] }}</h4>
                                    <p class="mb-0">Scheduled</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ $stats['expired'] }}</h4>
                                    <p class="mb-0">Expired</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-history fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="{{ route('super.announcements.index') }}">
                        <div class="row">
                            <div class="col-md-2">
                                <label for="type" class="form-label">Type</label>
                                <select name="type" id="type" class="form-control">
                                    <option value="">All Types</option>
                                    @foreach(\App\Models\Announcement::getTypeOptions() as $value => $label)
                                        <option value="{{ $value }}" {{ request('type') === $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="priority" class="form-label">Priority</label>
                                <select name="priority" id="priority" class="form-control">
                                    <option value="">All Priorities</option>
                                    @foreach(\App\Models\Announcement::getPriorityOptions() as $value => $label)
                                        <option value="{{ $value }}" {{ request('priority') === $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="target_audience" class="form-label">Audience</label>
                                <select name="target_audience" id="target_audience" class="form-control">
                                    <option value="">All Audiences</option>
                                    @foreach(\App\Models\Announcement::getTargetAudienceOptions() as $value => $label)
                                        <option value="{{ $value }}" {{ request('target_audience') === $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="status" class="form-label">Status</label>
                                <select name="status" id="status" class="form-control">
                                    <option value="">All Statuses</option>
                                    <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                                    <option value="scheduled" {{ request('status') === 'scheduled' ? 'selected' : '' }}>Scheduled</option>
                                    <option value="expired" {{ request('status') === 'expired' ? 'selected' : '' }}>Expired</option>
                                    <option value="draft" {{ request('status') === 'draft' ? 'selected' : '' }}>Draft</option>
                                    <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" name="search" id="search" class="form-control" 
                                       placeholder="Search announcements..." value="{{ request('search') }}">
                            </div>
                            <div class="col-md-1">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Announcements Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        System Announcements
                    </h5>
                </div>
                <div class="card-body">
                    @if($announcements->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Title</th>
                                        <th>Type</th>
                                        <th>Priority</th>
                                        <th>Audience</th>
                                        <th>Status</th>
                                        <th>Schedule</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($announcements as $announcement)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="{{ $announcement->icon }} me-2 text-{{ $announcement->type === 'danger' ? 'danger' : ($announcement->type === 'warning' ? 'warning' : ($announcement->type === 'success' ? 'success' : 'info')) }}"></i>
                                                <div>
                                                    <a href="{{ route('super.announcements.show', $announcement) }}" 
                                                       class="text-decoration-none fw-bold">
                                                        {{ $announcement->title }}
                                                    </a>
                                                    <div class="text-muted small">{{ Str::limit($announcement->content, 60) }}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>{!! $announcement->type_badge !!}</td>
                                        <td>{!! $announcement->priority_badge !!}</td>
                                        <td>
                                            <span class="badge bg-secondary">
                                                {{ \App\Models\Announcement::getTargetAudienceOptions()[$announcement->target_audience] }}
                                            </span>
                                        </td>
                                        <td>{!! $announcement->status_badge !!}</td>
                                        <td>
                                            @if($announcement->starts_at || $announcement->ends_at)
                                                <div class="small">
                                                    @if($announcement->starts_at)
                                                        <div><strong>Start:</strong> {{ $announcement->starts_at->format('M d, Y H:i') }}</div>
                                                    @endif
                                                    @if($announcement->ends_at)
                                                        <div><strong>End:</strong> {{ $announcement->ends_at->format('M d, Y H:i') }}</div>
                                                    @endif
                                                </div>
                                            @else
                                                <span class="text-muted">No schedule</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div>{{ $announcement->created_at->format('M d, Y') }}</div>
                                            <small class="text-muted">{{ $announcement->created_at->diffForHumans() }}</small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('super.announcements.show', $announcement) }}" 
                                                   class="btn btn-outline-primary btn-sm" title="View">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('super.announcements.edit', $announcement) }}" 
                                                   class="btn btn-outline-secondary btn-sm" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                @if(!$announcement->published_at || $announcement->published_at > now())
                                                    <form method="POST" action="{{ route('super.announcements.publish', $announcement) }}" class="d-inline">
                                                        @csrf
                                                        <button type="submit" class="btn btn-outline-success btn-sm" title="Publish">
                                                            <i class="fas fa-globe"></i>
                                                        </button>
                                                    </form>
                                                @elseif($announcement->is_active)
                                                    <form method="POST" action="{{ route('super.announcements.unpublish', $announcement) }}" class="d-inline">
                                                        @csrf
                                                        <button type="submit" class="btn btn-outline-warning btn-sm" title="Unpublish">
                                                            <i class="fas fa-eye-slash"></i>
                                                        </button>
                                                    </form>
                                                @endif
                                                <form method="POST" action="{{ route('super.announcements.destroy', $announcement) }}" 
                                                      class="d-inline" onsubmit="return confirm('Are you sure you want to delete this announcement?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-outline-danger btn-sm" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        @if($announcements->hasPages())
                            <div class="d-flex justify-content-center mt-4">
                                {{ $announcements->links() }}
                            </div>
                        @endif
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-bullhorn fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">No Announcements Found</h4>
                            <p class="text-muted">
                                @if(request()->hasAny(['type', 'priority', 'target_audience', 'status', 'search']))
                                    No announcements match your current filters.
                                @else
                                    Create your first announcement to communicate with users.
                                @endif
                            </p>
                            <div class="mt-4">
                                @if(request()->hasAny(['type', 'priority', 'target_audience', 'status', 'search']))
                                    <a href="{{ route('super.announcements.index') }}" class="btn btn-outline-secondary me-2">
                                        <i class="fas fa-times"></i> Clear Filters
                                    </a>
                                @endif
                                <a href="{{ route('super.announcements.create') }}" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Create First Announcement
                                </a>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mt-4">
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-tools fa-2x text-warning mb-3"></i>
                            <h5>Maintenance Notice</h5>
                            <p class="text-muted">Schedule maintenance announcements</p>
                            <a href="{{ route('super.announcements.create') }}?type=maintenance" class="btn btn-warning">
                                Create Maintenance Notice
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-info-circle fa-2x text-info mb-3"></i>
                            <h5>System Update</h5>
                            <p class="text-muted">Inform users about new features</p>
                            <a href="{{ route('super.announcements.create') }}?type=info" class="btn btn-info">
                                Create Update Notice
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-exclamation-triangle fa-2x text-danger mb-3"></i>
                            <h5>Important Alert</h5>
                            <p class="text-muted">Send urgent notifications</p>
                            <a href="{{ route('super.announcements.create') }}?type=danger&priority=urgent" class="btn btn-danger">
                                Create Alert
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
