@props(['settings', 'menuItems'])

<aside class="bg-gray-800 text-white w-64 min-h-screen flex-shrink-0 fixed left-0 md:relative md:w-64">
    <div class="h-screen flex flex-col">
        <!-- App Name -->
        <div class="p-4 border-b border-gray-700 flex-shrink-0">
            <div class="flex items-center">
                <span class="text-xl font-bold">{{ $settings->app_name }}</span>
            </div>
            @if($settings->app_slogan)
                <p class="text-sm text-gray-400 mt-1">{{ $settings->app_slogan }}</p>
            @endif
            @if($settings->organization_name)
                <p class="text-sm text-white mt-2 font-semibold">{{ $settings->organization_name }}</p>
            @endif

            <!-- Current Branch Display -->
            @if(auth()->check() && auth()->user()->branch_id)
                <div class="mt-2 py-1 px-2 bg-gray-700 rounded-md flex items-center">
                    <i class="fas fa-building text-xs mr-1 text-blue-300"></i>
                    <p class="text-xs font-medium">
                        <span class="text-gray-400">Branch:</span>
                        <span class="text-blue-300">{{ auth()->user() && auth()->user()->branch ? auth()->user()->branch->name : 'None' }}</span>
                    </p>
                </div>
            @elseif(auth()->check())
                <div class="mt-2 py-1 px-2 bg-gray-700 rounded-md flex items-center">
                    <i class="fas fa-exclamation-circle text-xs mr-1 text-yellow-300"></i>
                    <p class="text-xs font-medium text-yellow-300">No branch selected</p>
                </div>
            @endif
        </div>

        <!-- Navigation Menu (Scrollable) -->
        <nav class="flex-1 overflow-y-auto">
            <ul class="space-y-2 p-2">
                @foreach($menuItems as $item)
                    @if(isset($item['route']))
                    <li>
                        <a href="{{ route($item['route']) }}"
                           @if(isset($item['onclick'])) onclick="{{ $item['onclick'] }}" @endif
                           class="flex items-center px-4 py-2.5 text-sm hover:bg-gray-700 rounded-lg transition-colors duration-200
                                  {{ $item['active'] ? 'bg-gray-700' : '' }}">
                            @if(isset($item['icon']))
                                <i class="fas fa-{{ $item['icon'] }} w-5 h-5 mr-3"></i>
                            @endif
                            <span class="flex-1 whitespace-nowrap">{{ $item['label'] }}</span>
                            @if(isset($item['badge']))
                                <span class="px-2 py-1 text-xs rounded-full {{ $item['badge_color'] ?? ($item['active'] ? 'bg-blue-500' : 'bg-gray-600') }}">
                                    {{ $item['badge'] }}
                                </span>
                            @endif
                        </a>
                    </li>
                    @endif
                @endforeach
            </ul>
        </nav>

        <!-- User Info (Fixed at bottom) -->
        @if(auth()->check())
        <div class="flex-shrink-0 border-t border-gray-700">
            <div class="p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-user-circle text-2xl"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium">{{ auth()->user()->name }}</p>
                        <p class="text-xs text-gray-400">{{ auth()->user()->roles ? auth()->user()->roles->pluck('name')->join(', ') : 'No roles' }}</p>
                    </div>
                </div>

                <!-- Profile Link -->
                <a href="{{ route('profile.show') }}" class="mt-3 flex items-center px-4 py-2 text-sm rounded-lg hover:bg-gray-700 transition-colors duration-200">
                    <i class="fas fa-id-card w-5 h-5 mr-3"></i>
                    <span>My Profile</span>
                </a>

                <!-- Logout Button -->
                <form method="POST" action="{{ route('logout') }}" class="mt-3">
                    @csrf
                    <button type="submit" class="w-full flex items-center px-4 py-2 text-sm rounded-lg hover:bg-gray-700 transition-colors duration-200">
                        <i class="fas fa-sign-out-alt w-5 h-5 mr-3"></i>
                        <span>Logout</span>
                    </button>
                </form>

                <!-- Developer Info & Version -->
                <div class="mt-4 pt-4 border-t border-gray-700">
                    <div class="text-xs text-gray-400">
                        <p class="mt-1">OderFlow Pro</p>
                        <p>Version 4.0.1</p>
                        <p>Developed by: Kuronicz Tech</p>
                    </div>
                </div>
            </div>
        </div>
        @endif
    </div>
</aside>

@once
@push('scripts')
<script>
function initiateBackup(event) {
    event.preventDefault();

    Swal.fire({
        title: 'Create Database Backup',
        text: 'Are you sure you want to create a database backup?',
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'Yes, create backup',
        cancelButtonText: 'Cancel',
        showLoaderOnConfirm: true,
        preConfirm: () => {
            return fetch('/database/backup', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            })
            .then(response => response.json())
            .then(data => {
                if (!data.success) {
                    throw new Error(data.message || 'Backup failed');
                }
                return data;
            })
            .catch(error => {
                Swal.showValidationMessage(`Request failed: ${error.message}`);
            });
        },
        allowOutsideClick: () => !Swal.isLoading()
    }).then((result) => {
        if (result.isConfirmed) {
            Swal.fire({
                title: 'Success!',
                text: result.value.message,
                icon: 'success',
                timer: 2000,
                showConfirmButton: false
            });
        }
    });
}
</script>
@endpush
@endonce

<style>
    /* Custom Scrollbar Styles */
    .scrollbar-thin::-webkit-scrollbar {
        width: 2px;
    }

    .scrollbar-thin::-webkit-scrollbar-track {
        background-color: rgba(31, 41, 55, 0.8);
    }

    .scrollbar-thin::-webkit-scrollbar-thumb {
        background-color: rgba(156, 163, 175, 0.3);
        border-radius: 1px;
    }

    .scrollbar-thin::-webkit-scrollbar-thumb:hover {
        background-color: rgba(156, 163, 175, 0.5);
    }

    /* Firefox */
    .scrollbar-thin {
        scrollbar-width: thin;
        scrollbar-color: rgba(156, 163, 175, 0.3) rgba(31, 41, 55, 0.8);
    }
</style>
