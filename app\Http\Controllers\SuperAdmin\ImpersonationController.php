<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Organization;
use App\Services\ImpersonationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ImpersonationController extends Controller
{
    protected $impersonationService;

    public function __construct(ImpersonationService $impersonationService)
    {
        $this->middleware('super_admin');
        $this->impersonationService = $impersonationService;
    }

    /**
     * Show user selection for impersonation
     */
    public function index(Request $request)
    {
        $query = User::with(['organization', 'roles'])
            ->where('status', 'active')
            ->whereHas('organization', function($q) {
                $q->where('is_active', true);
            });

        // Filter by organization
        if ($request->has('organization_id') && $request->organization_id) {
            $query->where('organization_id', $request->organization_id);
        }

        // Search by name or email
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $users = $query->orderBy('name')->paginate(20);
        $organizations = Organization::where('is_active', true)->orderBy('name')->get();

        return view('super_admin.impersonation.index', compact('users', 'organizations'));
    }

    /**
     * Start impersonating a user
     */
    public function start(User $user)
    {
        $superAdmin = Auth::guard('super_admin')->user();

        if ($this->impersonationService->startImpersonation($user, $superAdmin)) {
            return redirect()->route('dashboard')
                ->with('success', "You are now impersonating {$user->name}. Remember to stop impersonation when done.");
        }

        return back()->with('error', 'Failed to start impersonation. Please try again.');
    }

    /**
     * Stop impersonation and return to super admin
     */
    public function stop()
    {
        if ($this->impersonationService->stopImpersonation()) {
            return redirect()->route('super.dashboard')
                ->with('success', 'Impersonation stopped successfully.');
        }

        return redirect()->route('super.login')
            ->with('error', 'Failed to stop impersonation. Please log in again.');
    }

    /**
     * Get impersonation status (for AJAX)
     */
    public function status()
    {
        if (!$this->impersonationService->isImpersonating()) {
            return response()->json(['impersonating' => false]);
        }

        $data = $this->impersonationService->getImpersonationData();
        $remainingTime = $this->impersonationService->getRemainingTime();

        return response()->json([
            'impersonating' => true,
            'target_user' => [
                'name' => $data['target_user_name'],
                'email' => $data['target_user_email'],
            ],
            'super_admin' => [
                'name' => $data['super_admin_name'],
                'email' => $data['super_admin_email'],
            ],
            'remaining_time_minutes' => $remainingTime,
            'expires_at' => $data['expires_at'],
        ]);
    }

    /**
     * Show user details before impersonation
     */
    public function show(User $user)
    {
        $user->load(['organization', 'roles', 'branch']);

        // Get recent user activities
        $recentActivities = $user->activities()
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return view('super_admin.impersonation.show', compact('user', 'recentActivities'));
    }
}
