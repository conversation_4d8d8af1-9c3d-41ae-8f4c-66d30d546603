@extends('layouts.app')

@section('title', 'Subscription Plans')

@section('content')
@php
    // Ensure we have default values to prevent errors
    $organization = $organization ?? auth()->user()->organization;
    $currentPlan = $currentPlan ?? $organization->plan ?? null;
    $availablePlans = $availablePlans ?? collect();
    $planComparisons = $planComparisons ?? [];
    $activeSubscription = $activeSubscription ?? $organization->activeSubscription ?? null;
@endphp
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Subscription Plans</h1>
            <p class="text-muted">Manage your subscription and billing</p>
        </div>
    </div>

    <!-- Pending Payments -->
    @if(isset($pendingPayments) && $pendingPayments->count() > 0)
        <div class="card shadow mb-4 border-warning">
            <div class="card-header py-3 bg-warning text-dark">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-clock me-2"></i>Pending Payments
                </h6>
            </div>
            <div class="card-body">
                @foreach($pendingPayments as $payment)
                    <div class="row mb-3 {{ !$loop->last ? 'border-bottom pb-3' : '' }}">
                        <div class="col-md-6">
                            <h6>Payment Details:</h6>
                            <ul class="list-unstyled">
                                <li><strong>Amount:</strong> ${{ number_format($payment->amount, 2) }}</li>
                                <li><strong>Method:</strong> {{ ucfirst(str_replace('_', ' ', $payment->payment_method)) }}</li>
                                <li><strong>Reference:</strong> {{ $payment->payment_reference }}</li>
                                <li><strong>Submitted:</strong> {{ $payment->created_at->format('M d, Y g:i A') }}</li>
                                <li><strong>Status:</strong>
                                    <span class="badge bg-warning">{{ ucfirst($payment->status) }}</span>
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            @if($payment->notes)
                                <h6>Notes:</h6>
                                <div class="bg-light p-3 rounded">
                                    @php
                                        // Try to parse JSON data for plan change details
                                        $notesData = null;
                                        if (str_contains($payment->notes, 'Plan Change Details:')) {
                                            $parts = explode('Plan Change Details:', $payment->notes);
                                            if (count($parts) > 1) {
                                                $jsonPart = trim($parts[1]);
                                                $notesData = json_decode($jsonPart, true);
                                            }
                                        }
                                    @endphp

                                    @if($notesData && is_array($notesData))
                                        {{-- Display formatted plan change details --}}
                                        @if(str_contains($payment->notes, 'Plan Change:'))
                                            <div class="mb-3">
                                                <strong class="text-primary">Plan Change Request</strong>
                                            </div>
                                        @endif

                                        <div class="row">
                                            <div class="col-md-6">
                                                <h6 class="text-info">Change Details</h6>
                                                <ul class="list-unstyled small">
                                                    <li><strong>Type:</strong> {{ ucfirst(str_replace('_', ' ', $notesData['type'] ?? 'N/A')) }}</li>
                                                    <li><strong>Change Type:</strong> {{ ucfirst($notesData['change_type'] ?? 'N/A') }}</li>
                                                    @if(isset($notesData['current_plan_id']))
                                                        <li><strong>From Plan ID:</strong> {{ $notesData['current_plan_id'] ?? 'No Plan' }}</li>
                                                    @endif
                                                    @if(isset($notesData['requested_plan_id']))
                                                        <li><strong>To Plan ID:</strong> {{ $notesData['requested_plan_id'] }}</li>
                                                    @endif
                                                </ul>
                                            </div>

                                            @if(isset($notesData['proration']) && is_array($notesData['proration']))
                                                <div class="col-md-6">
                                                    <h6 class="text-success">Pricing Details</h6>
                                                    <ul class="list-unstyled small">
                                                        <li><strong>Amount:</strong> ${{ number_format($notesData['proration']['net_amount'] ?? 0, 2) }}</li>
                                                        @if(isset($notesData['proration']['current_plan_credit']) && $notesData['proration']['current_plan_credit'] > 0)
                                                            <li><strong>Credit:</strong> ${{ number_format($notesData['proration']['current_plan_credit'], 2) }}</li>
                                                        @endif
                                                        @if(isset($notesData['proration']['new_plan_charge']))
                                                            <li><strong>New Plan Charge:</strong> ${{ number_format($notesData['proration']['new_plan_charge'], 2) }}</li>
                                                        @endif
                                                        @if(isset($notesData['proration']['remaining_days']))
                                                            <li><strong>Remaining Days:</strong> {{ number_format($notesData['proration']['remaining_days'], 0) }} days</li>
                                                        @endif
                                                    </ul>
                                                </div>
                                            @endif
                                        </div>

                                        @if(isset($notesData['proration']['proration_details']))
                                            <div class="mt-3">
                                                <small class="text-muted">
                                                    <strong>Details:</strong> {{ $notesData['proration']['proration_details'] }}
                                                </small>
                                            </div>
                                        @endif
                                    @else
                                        {{-- Display regular notes --}}
                                        <div style="white-space: pre-wrap;" class="text-muted">{{ $payment->notes }}</div>
                                    @endif
                                </div>
                            @endif
                            <div class="mt-3">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Your payment is being reviewed by admin. Your plan will be updated once payment is approved.
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    @endif

    <!-- Current Plan Status -->
    @if($currentPlan)
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Current Plan</h6>
            @if($organization->access_status === 'grace_period')
                <span class="badge bg-warning">Grace Period</span>
            @elseif($organization->access_status === 'trial')
                <span class="badge bg-info">Trial</span>
            @elseif($organization->access_status === 'active')
                <span class="badge bg-success">Active</span>
            @else
                <span class="badge bg-danger">Expired</span>
            @endif
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <h4>{{ $currentPlan->name }}</h4>
                    @if($currentPlan->description)
                        <p class="text-muted">{{ $currentPlan->description }}</p>
                    @endif

                    <!-- Plan Features -->
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <h6>Plan Limits:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-building text-info me-2"></i>
                                    {{ $currentPlan->branch_limit == 999 ? 'Unlimited' : $currentPlan->branch_limit }} Branches
                                </li>
                                <li><i class="fas fa-users text-success me-2"></i>
                                    {{ $currentPlan->user_limit == 999 ? 'Unlimited' : $currentPlan->user_limit }} Users
                                </li>
                                <li><i class="fas fa-shopping-cart text-purple me-2"></i>
                                    {{ $currentPlan->order_limit === null ? 'Unlimited' : $currentPlan->order_limit }} Orders/Month
                                </li>
                                <li><i class="fas fa-database text-warning me-2"></i>
                                    {{ $currentPlan->data_retention_days == 999 ? 'Forever' : $currentPlan->data_retention_days . ' Days' }} Data Retention
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Features:</h6>
                            <div class="d-flex flex-wrap gap-1">
                                @if($currentPlan->thermal_printing)
                                    <span class="badge bg-success">Thermal Printing</span>
                                @endif
                                @if($currentPlan->advanced_reporting)
                                    <span class="badge bg-info">Advanced Reports</span>
                                @endif
                                @if($currentPlan->api_access)
                                    <span class="badge bg-primary">API Access</span>
                                @endif
                                @if($currentPlan->white_label)
                                    <span class="badge bg-secondary">White Label</span>
                                @endif
                                @if($currentPlan->custom_branding)
                                    <span class="badge bg-warning">Custom Branding</span>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-md-end">
                    <div class="h2 text-primary">${{ number_format($currentPlan->price, 2) }}</div>
                    <small class="text-muted">per month</small>

                    @if($activeSubscription)
                        <div class="mt-3">
                            <small class="text-muted">
                                <strong>Current Period:</strong><br>
                                {{ $activeSubscription->start_date->format('M j, Y') }} -
                                {{ $activeSubscription->end_date->format('M j, Y') }}

                                @if($activeSubscription->end_date->isFuture())
                                    <br><span class="text-success">{{ $activeSubscription->end_date->diffForHumans() }}</span>
                                @else
                                    <br><span class="text-danger">Expired {{ $activeSubscription->end_date->diffForHumans() }}</span>
                                @endif
                            </small>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Available Plans -->
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Available Plans</h6>
        </div>
        <div class="card-body">
            <!-- Billing Period Toggle -->
            <x-billing-period-toggle :selectedPeriod="request('billing_period', 'monthly')" />

            @if($availablePlans->count() > 0)
                <div class="row">
                    @foreach($planComparisons as $comparison)
                        @php $plan = $comparison['plan']; @endphp
                        <div class="col-lg-4 mb-4">
                            <div class="card h-100 plan-card {{ $plan->is_featured ? 'border-primary' : '' }}"
                                 data-plan-id="{{ $plan->id }}"
                                 data-monthly-price="{{ $plan->price }}"
                                 data-annual-price="{{ $plan->annual_price }}"
                                 data-annual-discount="{{ $plan->annual_discount_percentage }}">
                                @if($plan->is_featured)
                                    <div class="card-header bg-primary text-white text-center">
                                        <small><i class="fas fa-star me-1"></i>Most Popular</small>
                                    </div>
                                @endif

                                <div class="card-body text-center">
                                    <h5 class="card-title">{{ $plan->name }}</h5>
                                    @if($plan->description)
                                        <p class="card-text text-muted">{{ $plan->description }}</p>
                                    @endif

                                    <div class="h2 text-primary plan-price">${{ number_format($plan->price, 2) }}</div>
                                    <small class="text-muted plan-period">per month</small>

                                    <!-- Annual Savings Display -->
                                    <div class="plan-savings mt-2" style="display: none;">
                                        <!-- Populated by JavaScript -->
                                    </div>

                                    <!-- Plan Limits -->
                                    <hr>
                                    <div class="text-start">
                                        <small>
                                            <strong>Includes:</strong><br>
                                            • {{ $plan->branch_limit == 999 ? 'Unlimited' : $plan->branch_limit }} Branches<br>
                                            • {{ $plan->user_limit == 999 ? 'Unlimited' : $plan->user_limit }} Users<br>
                                            • {{ $plan->order_limit === null ? 'Unlimited' : $plan->order_limit }} Orders/Month<br>
                                            • {{ $plan->data_retention_days == 999 ? 'Forever' : $plan->data_retention_days . ' Days' }} Data Retention
                                        </small>
                                    </div>

                                    <!-- Features -->
                                    @if($plan->thermal_printing || $plan->advanced_reporting || $plan->api_access || $plan->white_label || $plan->custom_branding)
                                        <hr>
                                        <div class="text-start">
                                            <small><strong>Features:</strong><br>
                                                @if($plan->thermal_printing) • Thermal Printing<br> @endif
                                                @if($plan->advanced_reporting) • Advanced Reporting<br> @endif
                                                @if($plan->api_access) • API Access<br> @endif
                                                @if($plan->white_label) • White Label<br> @endif
                                                @if($plan->custom_branding) • Custom Branding<br> @endif
                                            </small>
                                        </div>
                                    @endif

                                    <!-- Proration Info -->
                                    @if(isset($comparison['proration']))
                                        <hr>
                                        <div class="alert {{ $comparison['proration']['net_amount'] > 0 ? 'alert-info' : ($comparison['proration']['net_amount'] < 0 ? 'alert-success' : 'alert-secondary') }}">
                                            <small>
                                                @if($comparison['proration']['type'] === 'new_subscription')
                                                    <strong>New Subscription:</strong> ${{ number_format($plan->price, 2) }}/month
                                                @elseif($comparison['proration']['net_amount'] > 0)
                                                    <strong>Upgrade Cost:</strong> ${{ number_format($comparison['proration']['net_amount'], 2) }}
                                                    @if(isset($comparison['proration']['remaining_days']))
                                                        <br><small class="text-muted">Prorated for {{ $comparison['proration']['remaining_days'] }} remaining days</small>
                                                    @endif
                                                @elseif($comparison['proration']['net_amount'] < 0)
                                                    <strong>Downgrade Credit:</strong> ${{ number_format(abs($comparison['proration']['net_amount']), 2) }}
                                                    @if(isset($comparison['proration']['remaining_days']))
                                                        <br><small class="text-muted">Credit for {{ $comparison['proration']['remaining_days'] }} remaining days</small>
                                                    @endif
                                                @else
                                                    <strong>No Additional Cost</strong>
                                                    <br><small class="text-muted">Same price tier - feature changes only</small>
                                                @endif
                                                @if(isset($comparison['proration']['proration_details']))
                                                    <br><em>{{ $comparison['proration']['proration_details'] }}</em>
                                                @endif
                                            </small>
                                        </div>
                                    @endif
                                </div>

                                <div class="card-footer">
                                    @if($comparison['is_upgrade'])
                                        <a href="#" class="btn btn-primary w-100 plan-select-btn" data-plan-id="{{ $plan->id }}">
                                            <i class="fas fa-arrow-up me-2"></i>Upgrade to {{ $plan->name }}
                                        </a>
                                    @elseif($comparison['is_downgrade'])
                                        <a href="#" class="btn btn-warning w-100 plan-select-btn" data-plan-id="{{ $plan->id }}">
                                            <i class="fas fa-arrow-down me-2"></i>Downgrade to {{ $plan->name }}
                                        </a>
                                    @else
                                        <a href="#" class="btn btn-outline-primary w-100 plan-select-btn" data-plan-id="{{ $plan->id }}">
                                            <i class="fas fa-exchange-alt me-2"></i>Switch to {{ $plan->name }}
                                        </a>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-4">
                    <i class="fas fa-layer-group fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No other plans available</h5>
                    <p class="text-muted">You're already on the best plan for your needs!</p>
                </div>
            @endif
        </div>
    </div>

    <!-- Subscription Management -->
    @if($activeSubscription)
    <div class="card shadow mt-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Subscription Management</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>Billing Information</h6>
                    <p><strong>Payment Method:</strong>
                        {{ $activeSubscription->payment_method ? ucfirst(str_replace('_', ' ', $activeSubscription->payment_method)) : 'Not set' }}
                    </p>
                    <p><strong>Auto Renew:</strong>
                        {{ $activeSubscription->auto_renew ? 'Enabled' : 'Disabled' }}
                    </p>
                    <p><strong>Amount Paid:</strong>
                        ${{ number_format($activeSubscription->amount_paid ?? $currentPlan->price, 2) }}
                    </p>
                </div>
                <div class="col-md-6">
                    <h6>Actions</h6>
                    <div class="d-grid gap-2">
                        @if($activeSubscription->status === 'active')
                            <button type="button" class="btn btn-outline-warning" data-bs-toggle="modal" data-bs-target="#cancelModal">
                                <i class="fas fa-ban me-2"></i>Cancel Subscription
                            </button>
                        @elseif($activeSubscription->status === 'canceled')
                            <form method="POST" action="{{ route('subscription.reactivate') }}">
                                @csrf
                                <button type="submit" class="btn btn-success w-100"
                                        onclick="return confirm('Reactivate your subscription?')">
                                    <i class="fas fa-play me-2"></i>Reactivate Subscription
                                </button>
                            </form>
                        @endif

                        <button type="button" class="btn btn-outline-info" onclick="alert('Payment management coming soon!')">
                            <i class="fas fa-credit-card me-2"></i>Update Payment Method
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>

<!-- Cancel Subscription Modal -->
@if($activeSubscription && $activeSubscription->status === 'active')
<div class="modal fade" id="cancelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Cancel Subscription</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ route('subscription.cancel') }}">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <strong>Warning:</strong> Canceling your subscription will limit your access to the system.
                        You will have a 7-day grace period to reactivate.
                    </div>

                    <div class="mb-3">
                        <label for="cancellation_reason" class="form-label">Reason for cancellation *</label>
                        <textarea class="form-control" id="cancellation_reason"
                                  name="cancellation_reason" rows="3" required
                                  placeholder="Please tell us why you're canceling..."></textarea>
                    </div>

                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="confirm" name="confirm" value="1" required>
                        <label class="form-check-label" for="confirm">
                            I understand that canceling will limit my access to the system
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Keep Subscription</button>
                    <button type="submit" class="btn btn-warning">Cancel Subscription</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endif

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle plan selection with billing period
    const planSelectButtons = document.querySelectorAll('.plan-select-btn');

    planSelectButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            const planId = this.dataset.planId;
            const billingPeriod = document.querySelector('input[name="billing_period"]:checked').value;

            // Redirect to preview with billing period
            const url = `{{ route('plan-change.preview', ':planId') }}`.replace(':planId', planId);
            window.location.href = url + '?billing_period=' + billingPeriod;
        });
    });
});
</script>

@endsection
