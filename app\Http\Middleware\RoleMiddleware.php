<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class RoleMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string  $role
     * @return mixed
     */
    public function handle(Request $request, Closure $next, ...$roles)
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        try {
            $user = Auth::user();

            // Handle both array and pipe-separated string inputs
            $requiredRoles = count($roles) === 1 && str_contains($roles[0], '|')
                ? explode('|', $roles[0])
                : $roles;

            // Clean and validate roles
            $requiredRoles = array_filter(array_map('trim', $requiredRoles));

            if (empty($requiredRoles)) {
                Log::error('Role middleware called with no roles specified', [
                    'url' => $request->fullUrl(),
                    'user' => $user->email
                ]);
                abort(500, 'Server configuration error');
            }

            if (!$user->hasAnyRole($requiredRoles)) {
                Log::warning('Unauthorized access attempt', [
                    'user' => $user->email,
                    'required_roles' => $requiredRoles,
                    'user_roles' => $user->roles()->pluck('roles.name')->toArray(), // Use explicit column name
                    'ip' => $request->ip(),
                    'url' => $request->fullUrl()
                ]);

                return response()->view('errors.403', [
                    'message' => 'You do not have permission to access this resource.',
                    'required_roles' => $requiredRoles
                ], 403);
            }

            return $next($request);

        } catch (\Exception $e) {
            Log::error('Role middleware error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'url' => $request->fullUrl(),
                'user' => Auth::id()
            ]);

            abort(500, 'An error occurred while checking permissions');
        }
    }
}