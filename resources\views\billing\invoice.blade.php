@extends('layouts.app')

@section('title', 'Invoice - ' . $payment->invoice_number)

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Invoice {{ $payment->invoice_number }}</h1>
                <div>
                    <a href="{{ route('billing.index') }}" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left"></i> Back to Billing
                    </a>
                    <a href="{{ route('billing.invoice.download', $payment) }}" class="btn btn-primary">
                        <i class="fas fa-download"></i> Download PDF
                    </a>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <!-- Invoice Header -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h4 class="text-primary">Sales Management System</h4>
                            <p class="mb-1">Subscription Services</p>
                            <p class="mb-1">Email: <EMAIL></p>
                            <p class="mb-0">Phone: +****************</p>
                        </div>
                        <div class="col-md-6 text-end">
                            <h5>INVOICE</h5>
                            <p class="mb-1"><strong>Invoice #:</strong> {{ $payment->invoice_number }}</p>
                            <p class="mb-1"><strong>Date:</strong> {{ $payment->invoice_generated_at->format('M d, Y') }}</p>
                            <p class="mb-0"><strong>Payment Date:</strong> {{ $payment->payment_date->format('M d, Y') }}</p>
                        </div>
                    </div>

                    <hr>

                    <!-- Bill To -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6>Bill To:</h6>
                            <p class="mb-1"><strong>{{ $payment->organization->name }}</strong></p>
                            @if($payment->organization->email)
                                <p class="mb-1">{{ $payment->organization->email }}</p>
                            @endif
                            @if($payment->organization->phone)
                                <p class="mb-1">{{ $payment->organization->phone }}</p>
                            @endif
                            @if($payment->organization->address)
                                <p class="mb-0">{{ $payment->organization->address }}</p>
                            @endif
                        </div>
                        <div class="col-md-6 text-end">
                            <h6>Subscription Details:</h6>
                            <p class="mb-1"><strong>Plan:</strong> {{ $payment->subscription->plan->name }}</p>
                            <p class="mb-1"><strong>Period:</strong> {{ $payment->subscription->start_date->format('M d, Y') }} - {{ $payment->subscription->end_date->format('M d, Y') }}</p>
                            <p class="mb-0"><strong>Status:</strong>
                                <span class="badge bg-{{ $payment->subscription->status === 'active' ? 'success' : 'warning' }}">
                                    {{ ucfirst($payment->subscription->status) }}
                                </span>
                            </p>
                        </div>
                    </div>

                    <!-- Invoice Items -->
                    <div class="table-responsive mb-4">
                        <table class="table table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th>Description</th>
                                    <th>Period</th>
                                    <th>Payment Method</th>
                                    <th class="text-end">Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <strong>{{ $payment->subscription->plan->name }} Subscription</strong>
                                        <br><small class="text-muted">Payment for subscription services</small>
                                        @if($payment->payment_reference)
                                            <br><small class="text-muted">Reference: {{ $payment->payment_reference }}</small>
                                        @endif
                                    </td>
                                    <td>{{ $payment->payment_date->format('M Y') }}</td>
                                    <td>{{ ucfirst(str_replace('_', ' ', $payment->payment_method)) }}</td>
                                    <td class="text-end">${{ number_format($payment->amount, 2) }}</td>
                                </tr>
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="3" class="text-end">Subtotal:</th>
                                    <th class="text-end">${{ number_format($payment->amount, 2) }}</th>
                                </tr>
                                <tr>
                                    <th colspan="3" class="text-end">Tax (0%):</th>
                                    <th class="text-end">$0.00</th>
                                </tr>
                                <tr class="table-primary">
                                    <th colspan="3" class="text-end">Total:</th>
                                    <th class="text-end">${{ number_format($payment->amount, 2) }}</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <!-- Payment Information -->
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Payment Information:</h6>
                            <p class="mb-1"><strong>Payment Date:</strong> {{ $payment->payment_date->format('M d, Y') }}</p>
                            <p class="mb-1"><strong>Payment Method:</strong> {{ ucfirst(str_replace('_', ' ', $payment->payment_method)) }}</p>
                            @if($payment->payment_reference)
                                <p class="mb-1"><strong>Reference:</strong> {{ $payment->payment_reference }}</p>
                            @endif
                            <p class="mb-0"><strong>Status:</strong>
                                <span class="badge bg-success">Paid</span>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h6>Approval Information:</h6>
                            <p class="mb-1"><strong>Approved By:</strong> {{ $payment->approvedBy->name ?? 'System' }}</p>
                            <p class="mb-1"><strong>Approved Date:</strong> {{ $payment->approved_at->format('M d, Y H:i') }}</p>
                            @if($payment->notes)
                                <div class="mb-0">
                                    <strong>Notes:</strong>
                                    @php
                                        // Try to parse JSON data for plan change details
                                        $notesData = null;
                                        if (str_contains($payment->notes, 'Plan Change Details:')) {
                                            $parts = explode('Plan Change Details:', $payment->notes);
                                            if (count($parts) > 1) {
                                                $jsonPart = trim($parts[1]);
                                                $notesData = json_decode($jsonPart, true);
                                            }
                                        }
                                    @endphp

                                    @if($notesData && is_array($notesData))
                                        {{-- Display formatted plan change details --}}
                                        <div class="mt-2">
                                            @if(str_contains($payment->notes, 'Plan Change:'))
                                                <div class="mb-2">
                                                    <em class="text-primary">Plan Change Request</em>
                                                </div>
                                            @endif

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <small>
                                                        <strong>Change Details:</strong><br>
                                                        • Type: {{ ucfirst(str_replace('_', ' ', $notesData['type'] ?? 'N/A')) }}<br>
                                                        • Change Type: {{ ucfirst($notesData['change_type'] ?? 'N/A') }}<br>
                                                        @if(isset($notesData['current_plan_id']))
                                                            • From Plan ID: {{ $notesData['current_plan_id'] ?? 'No Plan' }}<br>
                                                        @endif
                                                        @if(isset($notesData['requested_plan_id']))
                                                            • To Plan ID: {{ $notesData['requested_plan_id'] }}<br>
                                                        @endif
                                                    </small>
                                                </div>

                                                @if(isset($notesData['proration']) && is_array($notesData['proration']))
                                                    <div class="col-md-6">
                                                        <small>
                                                            <strong>Pricing Details:</strong><br>
                                                            • Amount: ${{ number_format($notesData['proration']['net_amount'] ?? 0, 2) }}<br>
                                                            @if(isset($notesData['proration']['current_plan_credit']) && $notesData['proration']['current_plan_credit'] > 0)
                                                                • Credit: ${{ number_format($notesData['proration']['current_plan_credit'], 2) }}<br>
                                                            @endif
                                                            @if(isset($notesData['proration']['new_plan_charge']))
                                                                • New Plan Charge: ${{ number_format($notesData['proration']['new_plan_charge'], 2) }}<br>
                                                            @endif
                                                            @if(isset($notesData['proration']['remaining_days']))
                                                                • Remaining Days: {{ number_format($notesData['proration']['remaining_days'], 0) }} days<br>
                                                            @endif
                                                        </small>
                                                    </div>
                                                @endif
                                            </div>

                                            @if(isset($notesData['proration']['proration_details']))
                                                <div class="mt-2">
                                                    <small class="text-muted">
                                                        <strong>Details:</strong> {{ $notesData['proration']['proration_details'] }}
                                                    </small>
                                                </div>
                                            @endif
                                        </div>
                                    @else
                                        {{-- Display regular notes --}}
                                        <div class="mt-1">{{ $payment->notes }}</div>
                                    @endif
                                </div>
                            @endif
                        </div>
                    </div>

                    <hr class="mt-4">

                    <!-- Footer -->
                    <div class="text-center text-muted">
                        <p class="mb-1">Thank you for your business!</p>
                        <p class="mb-0">This is a computer-generated invoice and does not require a signature.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    .btn, .card-header, nav, .sidebar {
        display: none !important;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    .container-fluid {
        padding: 0 !important;
    }
}
</style>
@endsection
