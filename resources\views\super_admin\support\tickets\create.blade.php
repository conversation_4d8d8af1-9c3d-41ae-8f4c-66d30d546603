@extends('super_admin.layouts.app')

@section('title', 'Create Support Ticket')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Create Support Ticket</h1>
                <a href="{{ route('super.support.tickets.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Tickets
                </a>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-plus me-2"></i>
                                New Support Ticket
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="{{ route('super.support.tickets.store') }}">
                                @csrf
                                
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                                        <input type="text" name="title" id="title" class="form-control @error('title') is-invalid @enderror" 
                                               value="{{ old('title') }}" placeholder="Brief description of the issue" required>
                                        @error('title')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="col-md-6">
                                        <label for="organization_id" class="form-label">Organization <span class="text-danger">*</span></label>
                                        <select name="organization_id" id="organization_id" class="form-select @error('organization_id') is-invalid @enderror" required>
                                            <option value="">Select Organization</option>
                                            @foreach($organizations as $organization)
                                                <option value="{{ $organization->id }}" {{ old('organization_id') == $organization->id ? 'selected' : '' }}>
                                                    {{ $organization->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('organization_id')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label for="priority" class="form-label">Priority <span class="text-danger">*</span></label>
                                        <select name="priority" id="priority" class="form-select @error('priority') is-invalid @enderror" required>
                                            <option value="">Select Priority</option>
                                            <option value="low" {{ old('priority') == 'low' ? 'selected' : '' }}>Low</option>
                                            <option value="normal" {{ old('priority') == 'normal' ? 'selected' : 'selected' }}>Normal</option>
                                            <option value="high" {{ old('priority') == 'high' ? 'selected' : '' }}>High</option>
                                            <option value="urgent" {{ old('priority') == 'urgent' ? 'selected' : '' }}>Urgent</option>
                                            <option value="critical" {{ old('priority') == 'critical' ? 'selected' : '' }}>Critical</option>
                                        </select>
                                        @error('priority')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="col-md-4">
                                        <label for="category" class="form-label">Category <span class="text-danger">*</span></label>
                                        <select name="category" id="category" class="form-select @error('category') is-invalid @enderror" required>
                                            <option value="">Select Category</option>
                                            <option value="technical" {{ old('category') == 'technical' ? 'selected' : '' }}>Technical Support</option>
                                            <option value="billing" {{ old('category') == 'billing' ? 'selected' : '' }}>Billing & Payments</option>
                                            <option value="account" {{ old('category') == 'account' ? 'selected' : '' }}>Account Management</option>
                                            <option value="feature_request" {{ old('category') == 'feature_request' ? 'selected' : '' }}>Feature Request</option>
                                            <option value="bug_report" {{ old('category') == 'bug_report' ? 'selected' : '' }}>Bug Report</option>
                                            <option value="general" {{ old('category') == 'general' ? 'selected' : 'selected' }}>General Inquiry</option>
                                        </select>
                                        @error('category')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="col-md-4">
                                        <label for="assigned_to" class="form-label">Assign To</label>
                                        <select name="assigned_to" id="assigned_to" class="form-select @error('assigned_to') is-invalid @enderror">
                                            <option value="">Unassigned</option>
                                            @foreach($admins as $admin)
                                                <option value="{{ $admin->id }}" {{ old('assigned_to') == $admin->id ? 'selected' : '' }}>
                                                    {{ $admin->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('assigned_to')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="user_id" class="form-label">User (Optional)</label>
                                    <select name="user_id" id="user_id" class="form-select @error('user_id') is-invalid @enderror">
                                        <option value="">No specific user</option>
                                        <!-- Users will be loaded via AJAX based on organization selection -->
                                    </select>
                                    @error('user_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Select a specific user if this ticket is on their behalf</div>
                                </div>

                                <div class="mb-4">
                                    <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                                    <textarea name="description" id="description" class="form-control @error('description') is-invalid @enderror" 
                                              rows="8" placeholder="Detailed description of the issue or request..." required>{{ old('description') }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('super.support.tickets.index') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times"></i> Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-plus"></i> Create Ticket
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- Help Card -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-question-circle me-2"></i>
                                Creating Tickets
                            </h6>
                        </div>
                        <div class="card-body">
                            <h6>Priority Guidelines:</h6>
                            <ul class="list-unstyled">
                                <li><span class="badge bg-secondary">Low</span> - General questions, minor issues</li>
                                <li><span class="badge bg-primary">Normal</span> - Standard support requests</li>
                                <li><span class="badge bg-warning">High</span> - Important issues affecting workflow</li>
                                <li><span class="badge bg-danger">Urgent</span> - Critical issues requiring immediate attention</li>
                                <li><span class="badge bg-dark">Critical</span> - System down, security issues</li>
                            </ul>

                            <h6 class="mt-3">Category Guidelines:</h6>
                            <ul class="list-unstyled">
                                <li><strong>Technical:</strong> System errors, bugs, performance issues</li>
                                <li><strong>Billing:</strong> Payment questions, subscription issues</li>
                                <li><strong>Account:</strong> User management, permissions</li>
                                <li><strong>Feature Request:</strong> New feature suggestions</li>
                                <li><strong>Bug Report:</strong> Software defects</li>
                                <li><strong>General:</strong> Other inquiries</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Recent Tickets -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-clock me-2"></i>
                                Recent Tickets
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="text-center text-muted">
                                <i class="fas fa-ticket-alt fa-2x mb-2"></i>
                                <p class="small">Recent tickets will appear here</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const organizationSelect = document.getElementById('organization_id');
    const userSelect = document.getElementById('user_id');

    organizationSelect.addEventListener('change', function() {
        const organizationId = this.value;
        
        // Clear user options
        userSelect.innerHTML = '<option value="">No specific user</option>';
        
        if (organizationId) {
            // In a real implementation, you would fetch users via AJAX
            // For now, we'll just show a placeholder
            userSelect.innerHTML += '<option value="">Loading users...</option>';
            
            // Simulate AJAX call
            setTimeout(() => {
                userSelect.innerHTML = '<option value="">No specific user</option>';
                userSelect.innerHTML += '<option value="">No users found for this organization</option>';
            }, 500);
        }
    });

    // Auto-resize textarea
    const textarea = document.getElementById('description');
    textarea.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
});
</script>
@endsection
