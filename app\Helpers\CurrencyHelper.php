<?php

namespace App\Helpers;

use App\Models\Setting;
use Illuminate\Support\Facades\Cache;

class CurrencyHelper
{
    /**
     * Get the currency symbol from settings
     *
     * @return string
     */
    public static function getCurrencySymbol()
    {
        return Cache::remember('currency_symbol', 60 * 60, function () {
            $setting = Setting::first();
            return $setting && !empty($setting->currency_symbol) ? $setting->currency_symbol : '₦';
        });
    }

    /**
     * Get the currency code from settings
     *
     * @return string
     */
    public static function getCurrencyCode()
    {
        return Cache::remember('currency_code', 60 * 60, function () {
            $setting = Setting::first();
            return $setting && !empty($setting->currency_code) ? $setting->currency_code : 'NGN';
        });
    }

    /**
     * Format a number as currency
     *
     * @param float $amount
     * @param int $decimals
     * @return string
     */
    public static function format($amount, $decimals = 2)
    {
        return self::getCurrencySymbol() . number_format($amount, $decimals);
    }

    /**
     * Format a number as currency with HTML
     *
     * @param float $amount
     * @param int $decimals
     * @return string
     */
    public static function formatHtml($amount, $decimals = 2)
    {
        return '<span class="currency">' . self::getCurrencySymbol() . '</span>' . number_format($amount, $decimals);
    }
}