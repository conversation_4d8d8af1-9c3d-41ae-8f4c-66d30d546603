<?php

use App\Helpers\CurrencyHelper;

if (!function_exists('currency_symbol')) {
    /**
     * Get the system's currency symbol
     *
     * @return string
     */
    function currency_symbol()
    {
        return CurrencyHelper::getCurrencySymbol();
    }
}

if (!function_exists('currency_code')) {
    /**
     * Get the system's currency code
     *
     * @return string
     */
    function currency_code()
    {
        return CurrencyHelper::getCurrencyCode();
    }
}

if (!function_exists('format_money')) {
    /**
     * Format a number as currency
     *
     * @param float $amount
     * @param int $decimals
     * @return string
     */
    function format_money($amount, $decimals = 2)
    {
        return CurrencyHelper::format($amount, $decimals);
    }
}
