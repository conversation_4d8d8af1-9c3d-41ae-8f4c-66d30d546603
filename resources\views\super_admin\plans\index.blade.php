@extends('super_admin.layouts.app')

@section('title', 'Subscription Plans')
@section('page-title', 'Subscription Plans')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Subscription Plans</h1>
            <p class="text-muted">Manage subscription plans and pricing</p>
        </div>
        <a href="{{ route('super.plans.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Add Plan
        </a>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('super.plans.index') }}" class="row g-3">
                <div class="col-md-6">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search"
                           value="{{ request('search') }}" placeholder="Search plans...">
                </div>
                <div class="col-md-4">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Statuses</option>
                        <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                        <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-search"></i> Filter
                    </button>
                    <a href="{{ route('super.plans.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> Clear
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Plans Table -->
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                Subscription Plans ({{ $plans->total() }})
            </h6>
        </div>
        <div class="card-body">
            @if($plans->count() > 0)
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Plan</th>
                                <th>Price</th>
                                <th>Limits</th>
                                <th>Features</th>
                                <th>Organizations</th>
                                <th>Subscriptions</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($plans as $plan)
                                <tr>
                                    <td>
                                        <div>
                                            <div class="font-weight-bold">{{ $plan->name }}</div>
                                            @if($plan->is_featured)
                                                <span class="badge bg-warning text-dark">Featured</span>
                                            @endif
                                            @if($plan->description)
                                                <small class="text-muted d-block">{{ Str::limit($plan->description, 50) }}</small>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <div class="h5 mb-0">${{ number_format($plan->price, 2) }}</div>
                                        <small class="text-muted">per month</small>
                                    </td>
                                    <td>
                                        <small>
                                            <strong>Branches:</strong> {{ $plan->branch_limit == 999 ? 'Unlimited' : $plan->branch_limit }}<br>
                                            <strong>Users:</strong> {{ $plan->user_limit == 999 ? 'Unlimited' : $plan->user_limit }}<br>
                                            <strong>Orders:</strong> {{ $plan->order_limit === null ? 'Unlimited' : $plan->order_limit . '/month' }}<br>
                                            <strong>Data:</strong> {{ $plan->data_retention_days == 999 ? 'Forever' : $plan->data_retention_days . ' days' }}
                                        </small>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-wrap gap-1">
                                            @if($plan->thermal_printing)
                                                <span class="badge bg-success">Thermal Printing</span>
                                            @endif
                                            @if($plan->advanced_reporting)
                                                <span class="badge bg-info">Advanced Reports</span>
                                            @endif
                                            @if($plan->api_access)
                                                <span class="badge bg-primary">API Access</span>
                                            @endif
                                            @if($plan->white_label)
                                                <span class="badge bg-secondary">White Label</span>
                                            @endif
                                            @if($plan->custom_branding)
                                                <span class="badge bg-warning">Custom Branding</span>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ $plan->organizations_count ?? 0 }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">{{ $plan->subscriptions_count ?? 0 }}</span>
                                    </td>
                                    <td>
                                        @if($plan->is_active)
                                            <span class="badge bg-success">Active</span>
                                        @else
                                            <span class="badge bg-danger">Inactive</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('super.plans.show', $plan) }}"
                                               class="btn btn-sm btn-outline-primary" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('super.plans.edit', $plan) }}"
                                               class="btn btn-sm btn-outline-secondary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            @if($plan->is_active)
                                                <form method="POST" action="{{ route('super.plans.deactivate', $plan) }}"
                                                      class="d-inline">
                                                    @csrf
                                                    <button type="submit" class="btn btn-sm btn-outline-warning"
                                                            title="Deactivate" onclick="return confirm('Are you sure?')">
                                                        <i class="fas fa-pause"></i>
                                                    </button>
                                                </form>
                                            @else
                                                <form method="POST" action="{{ route('super.plans.activate', $plan) }}"
                                                      class="d-inline">
                                                    @csrf
                                                    <button type="submit" class="btn btn-sm btn-outline-success"
                                                            title="Activate">
                                                        <i class="fas fa-play"></i>
                                                    </button>
                                                </form>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div>
                        Showing {{ $plans->firstItem() }} to {{ $plans->lastItem() }}
                        of {{ $plans->total() }} results
                    </div>
                    {{ $plans->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-layer-group fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No plans found</h5>
                    <p class="text-muted">Get started by creating your first subscription plan.</p>
                    <a href="{{ route('super.plans.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add Plan
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
