@extends('super_admin.layouts.app')

@section('title', 'Ticket #' . $ticket->ticket_number)

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Ticket #{{ $ticket->ticket_number }}</h1>
                    <p class="text-muted mb-0">{{ $ticket->title }}</p>
                </div>
                <div>
                    <a href="{{ route('super.support.tickets.index') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left"></i> Back to Tickets
                    </a>
                    <a href="{{ route('super.support.tickets.edit', $ticket) }}" class="btn btn-primary">
                        <i class="fas fa-edit"></i> Edit Ticket
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- Main Content -->
                <div class="col-lg-8">
                    <!-- Ticket Details -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                Ticket Details
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <strong>Priority:</strong> {!! $ticket->priority_badge !!}
                                </div>
                                <div class="col-md-6">
                                    <strong>Status:</strong> {!! $ticket->status_badge !!}
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <strong>Category:</strong> 
                                    <span class="badge bg-secondary">{{ ucfirst(str_replace('_', ' ', $ticket->category)) }}</span>
                                </div>
                                <div class="col-md-6">
                                    <strong>Created:</strong> {{ $ticket->created_at->format('M d, Y H:i') }}
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <strong>Organization:</strong> {{ $ticket->organization->name ?? 'N/A' }}
                                </div>
                                <div class="col-md-6">
                                    <strong>User:</strong> {{ $ticket->user->name ?? 'N/A' }}
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <strong>Assigned To:</strong>
                                    @if($ticket->assignedAdmin)
                                        <span class="badge bg-info">{{ $ticket->assignedAdmin->name }}</span>
                                    @else
                                        <span class="badge bg-warning">Unassigned</span>
                                    @endif
                                </div>
                                <div class="col-md-6">
                                    @if($ticket->resolved_at)
                                        <strong>Resolved:</strong> {{ $ticket->resolved_at->format('M d, Y H:i') }}
                                    @endif
                                </div>
                            </div>
                            <div class="mb-3">
                                <strong>Description:</strong>
                                <div class="mt-2 p-3 bg-light rounded">
                                    {!! nl2br(e($ticket->description)) !!}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Replies -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-comments me-2"></i>
                                Replies ({{ $ticket->replies->count() }})
                            </h5>
                        </div>
                        <div class="card-body">
                            @if($ticket->replies->count() > 0)
                                @foreach($ticket->replies as $reply)
                                <div class="d-flex mb-4 {{ $reply->is_internal ? 'border-warning' : '' }}" 
                                     style="{{ $reply->is_internal ? 'border-left: 4px solid #ffc107; padding-left: 15px;' : '' }}">
                                    <div class="flex-shrink-0 me-3">
                                        <div class="bg-{{ $reply->is_from_admin ? 'primary' : 'success' }} text-white rounded-circle d-flex align-items-center justify-content-center" 
                                             style="width: 40px; height: 40px;">
                                            {{ $reply->replier_avatar }}
                                        </div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <div>
                                                <strong>{{ $reply->replier_name }}</strong>
                                                <span class="badge bg-{{ $reply->is_from_admin ? 'primary' : 'success' }} ms-2">
                                                    {{ $reply->is_from_admin ? 'Admin' : 'User' }}
                                                </span>
                                                @if($reply->is_internal)
                                                    <span class="badge bg-warning ms-1">Internal</span>
                                                @endif
                                                @if($reply->is_solution)
                                                    <span class="badge bg-success ms-1">
                                                        <i class="fas fa-check"></i> Solution
                                                    </span>
                                                @endif
                                            </div>
                                            <small class="text-muted">{{ $reply->created_at->format('M d, Y H:i') }}</small>
                                        </div>
                                        <div class="message-content">
                                            {!! $reply->formatted_message !!}
                                        </div>
                                        @if(!$reply->is_solution && $reply->is_from_admin)
                                            <div class="mt-2">
                                                <form method="POST" action="{{ route('super.support.tickets.reply', $ticket) }}" class="d-inline">
                                                    @csrf
                                                    <input type="hidden" name="mark_solution" value="{{ $reply->id }}">
                                                    <button type="submit" class="btn btn-sm btn-outline-success" 
                                                            onclick="return confirm('Mark this reply as the solution?')">
                                                        <i class="fas fa-check"></i> Mark as Solution
                                                    </button>
                                                </form>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                                @endforeach
                            @else
                                <div class="text-center py-4">
                                    <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">No replies yet. Be the first to respond!</p>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Add Reply -->
                    @if($ticket->status !== 'closed')
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-reply me-2"></i>
                                Add Reply
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="{{ route('super.support.tickets.reply', $ticket) }}">
                                @csrf
                                <div class="mb-3">
                                    <label for="message" class="form-label">Message</label>
                                    <textarea name="message" id="message" class="form-control" rows="5" 
                                              placeholder="Type your reply here..." required></textarea>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="is_internal" id="is_internal" value="1">
                                        <label class="form-check-label" for="is_internal">
                                            <i class="fas fa-eye-slash me-1"></i>
                                            Internal Note (not visible to customer)
                                        </label>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-paper-plane"></i> Send Reply
                                    </button>
                                    <div>
                                        @if($ticket->status !== 'resolved')
                                            <button type="submit" name="resolve_ticket" value="1" class="btn btn-success me-2"
                                                    onclick="return confirm('Send reply and mark ticket as resolved?')">
                                                <i class="fas fa-check"></i> Reply & Resolve
                                            </button>
                                        @endif
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    @endif
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <!-- Quick Actions -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-bolt me-2"></i>
                                Quick Actions
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                @if($ticket->status !== 'resolved' && $ticket->status !== 'closed')
                                    <form method="POST" action="{{ route('super.support.tickets.resolve', $ticket) }}" class="d-inline">
                                        @csrf
                                        <button type="submit" class="btn btn-success w-100" 
                                                onclick="return confirm('Mark this ticket as resolved?')">
                                            <i class="fas fa-check"></i> Mark as Resolved
                                        </button>
                                    </form>
                                @endif

                                @if($ticket->status === 'resolved')
                                    <form method="POST" action="{{ route('super.support.tickets.close', $ticket) }}" class="d-inline">
                                        @csrf
                                        <button type="submit" class="btn btn-secondary w-100" 
                                                onclick="return confirm('Close this ticket?')">
                                            <i class="fas fa-times"></i> Close Ticket
                                        </button>
                                    </form>
                                @endif

                                @if($ticket->status === 'closed' || $ticket->status === 'resolved')
                                    <form method="POST" action="{{ route('super.support.tickets.reopen', $ticket) }}" class="d-inline">
                                        @csrf
                                        <button type="submit" class="btn btn-warning w-100" 
                                                onclick="return confirm('Reopen this ticket?')">
                                            <i class="fas fa-redo"></i> Reopen Ticket
                                        </button>
                                    </form>
                                @endif

                                @if($ticket->user)
                                    <a href="{{ route('super.impersonation.start', $ticket->user) }}" 
                                       class="btn btn-info w-100"
                                       onclick="return confirm('Impersonate this user?')">
                                        <i class="fas fa-user-secret"></i> Impersonate User
                                    </a>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Assignment -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-user-plus me-2"></i>
                                Assignment
                            </h6>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="{{ route('super.support.tickets.assign', $ticket) }}">
                                @csrf
                                <div class="mb-3">
                                    <label for="assigned_to" class="form-label">Assign To</label>
                                    <select name="assigned_to" id="assigned_to" class="form-select" required>
                                        <option value="">Select Admin</option>
                                        @foreach($admins as $admin)
                                            <option value="{{ $admin->id }}" 
                                                    {{ $ticket->assigned_to == $admin->id ? 'selected' : '' }}>
                                                {{ $admin->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-user-check"></i> Assign Ticket
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Ticket Information -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-info me-2"></i>
                                Ticket Information
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-2">
                                <strong>Ticket ID:</strong><br>
                                <code>{{ $ticket->id }}</code>
                            </div>
                            <div class="mb-2">
                                <strong>Created:</strong><br>
                                {{ $ticket->created_at->format('M d, Y H:i') }}<br>
                                <small class="text-muted">{{ $ticket->created_at->diffForHumans() }}</small>
                            </div>
                            @if($ticket->first_response_at)
                            <div class="mb-2">
                                <strong>First Response:</strong><br>
                                {{ $ticket->first_response_at->format('M d, Y H:i') }}<br>
                                <small class="text-muted">{{ $ticket->response_time }}h response time</small>
                            </div>
                            @endif
                            @if($ticket->resolved_at)
                            <div class="mb-2">
                                <strong>Resolved:</strong><br>
                                {{ $ticket->resolved_at->format('M d, Y H:i') }}<br>
                                <small class="text-muted">{{ $ticket->resolution_time }}h resolution time</small>
                            </div>
                            @endif
                            <div class="mb-2">
                                <strong>Last Updated:</strong><br>
                                {{ $ticket->updated_at->format('M d, Y H:i') }}<br>
                                <small class="text-muted">{{ $ticket->updated_at->diffForHumans() }}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.message-content {
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    border-left: 3px solid #007bff;
}
</style>
@endsection
