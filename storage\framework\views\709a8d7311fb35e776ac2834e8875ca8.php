<?php $__env->startSection('title', 'Edit Plan - ' . $plan->name); ?>
<?php $__env->startSection('page-title', 'Edit Plan'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Edit Plan: <?php echo e($plan->name); ?></h1>
            <p class="text-muted">Update subscription plan details</p>
        </div>
        <div class="d-flex gap-2">
            <a href="<?php echo e(route('super.plans.show', $plan)); ?>" class="btn btn-info">
                <i class="fas fa-eye me-2"></i>View Plan
            </a>
            <a href="<?php echo e(route('super.plans.index')); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Plans
            </a>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Plan Details</h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="<?php echo e(route('super.plans.update', $plan)); ?>">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>

                        <!-- Basic Information -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="name" class="form-label">Plan Name *</label>
                                <input type="text" class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="name" name="name" value="<?php echo e(old('name', $plan->name)); ?>" required>
                                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-6">
                                <label for="price" class="form-label">Monthly Price ($) *</label>
                                <input type="number" step="0.01" min="0"
                                       class="form-control <?php $__errorArgs = ['price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="price" name="price" value="<?php echo e(old('price', $plan->price)); ?>" required>
                                <?php $__errorArgs = ['price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                      id="description" name="description" rows="3"><?php echo e(old('description', $plan->description)); ?></textarea>
                            <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Billing Period Discounts -->
                        <h5 class="mb-3">Billing Period Discounts</h5>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Flexible Billing:</strong> Set discount percentages for different billing periods. Users can choose 1, 12, 24, or 48-month billing cycles.
                        </div>
                        <div class="row mb-4">
                            <?php
                                $discounts = $plan->billing_period_discounts ?: [];
                            ?>
                            <div class="col-md-3">
                                <label for="discount_1_month" class="form-label">1 Month Discount (%)</label>
                                <input type="number" step="0.1" min="0" max="50"
                                       class="form-control <?php $__errorArgs = ['discount_1_month'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="discount_1_month" name="discount_1_month" value="<?php echo e(old('discount_1_month', $discounts[1] ?? 0)); ?>">
                                <small class="text-muted">Usually 0% (no discount)</small>
                                <?php $__errorArgs = ['discount_1_month'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-3">
                                <label for="discount_12_months" class="form-label">12 Months Discount (%)</label>
                                <input type="number" step="0.1" min="0" max="50"
                                       class="form-control <?php $__errorArgs = ['discount_12_months'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="discount_12_months" name="discount_12_months" value="<?php echo e(old('discount_12_months', $discounts[12] ?? 15)); ?>">
                                <small class="text-muted">Annual billing discount</small>
                                <?php $__errorArgs = ['discount_12_months'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-3">
                                <label for="discount_24_months" class="form-label">24 Months Discount (%)</label>
                                <input type="number" step="0.1" min="0" max="50"
                                       class="form-control <?php $__errorArgs = ['discount_24_months'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="discount_24_months" name="discount_24_months" value="<?php echo e(old('discount_24_months', $discounts[24] ?? 25)); ?>">
                                <small class="text-muted">2-year commitment discount</small>
                                <?php $__errorArgs = ['discount_24_months'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-3">
                                <label for="discount_48_months" class="form-label">48 Months Discount (%)</label>
                                <input type="number" step="0.1" min="0" max="50"
                                       class="form-control <?php $__errorArgs = ['discount_48_months'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="discount_48_months" name="discount_48_months" value="<?php echo e(old('discount_48_months', $discounts[48] ?? 35)); ?>">
                                <small class="text-muted">4-year commitment discount</small>
                                <?php $__errorArgs = ['discount_48_months'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <!-- Limits -->
                        <h5 class="mb-3">Plan Limits</h5>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <label for="branch_limit" class="form-label">Branch Limit *</label>
                                <input type="number" min="1" max="999"
                                       class="form-control <?php $__errorArgs = ['branch_limit'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="branch_limit" name="branch_limit" value="<?php echo e(old('branch_limit', $plan->branch_limit)); ?>" required>
                                <small class="text-muted">Use 999 for unlimited</small>
                                <?php $__errorArgs = ['branch_limit'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-3">
                                <label for="user_limit" class="form-label">User Limit *</label>
                                <input type="number" min="1" max="999"
                                       class="form-control <?php $__errorArgs = ['user_limit'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="user_limit" name="user_limit" value="<?php echo e(old('user_limit', $plan->user_limit)); ?>" required>
                                <small class="text-muted">Use 999 for unlimited</small>
                                <?php $__errorArgs = ['user_limit'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-3">
                                <label for="order_limit" class="form-label">Order Limit (Monthly)</label>
                                <input type="number" min="1"
                                       class="form-control <?php $__errorArgs = ['order_limit'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="order_limit" name="order_limit" value="<?php echo e(old('order_limit', $plan->order_limit)); ?>">
                                <small class="text-muted">Leave empty for unlimited</small>
                                <?php $__errorArgs = ['order_limit'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-3">
                                <label for="data_retention_days" class="form-label">Data Retention (Days) *</label>
                                <input type="number" min="1" max="999"
                                       class="form-control <?php $__errorArgs = ['data_retention_days'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="data_retention_days" name="data_retention_days" value="<?php echo e(old('data_retention_days', $plan->data_retention_days)); ?>" required>
                                <small class="text-muted">Use 999 for forever</small>
                                <?php $__errorArgs = ['data_retention_days'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <!-- Features -->
                        <h5 class="mb-3">Plan Features</h5>
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input type="checkbox" class="form-check-input" id="thermal_printing"
                                           name="thermal_printing" value="1" <?php echo e(old('thermal_printing', $plan->thermal_printing) ? 'checked' : ''); ?>>
                                    <label class="form-check-label" for="thermal_printing">
                                        <strong>Thermal Printing</strong>
                                        <br><small class="text-muted">Enable thermal receipt printing</small>
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input type="checkbox" class="form-check-input" id="advanced_reporting"
                                           name="advanced_reporting" value="1" <?php echo e(old('advanced_reporting', $plan->advanced_reporting) ? 'checked' : ''); ?>>
                                    <label class="form-check-label" for="advanced_reporting">
                                        <strong>Advanced Reporting</strong>
                                        <br><small class="text-muted">Access to detailed analytics and reports</small>
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input type="checkbox" class="form-check-input" id="api_access"
                                           name="api_access" value="1" <?php echo e(old('api_access', $plan->api_access) ? 'checked' : ''); ?>>
                                    <label class="form-check-label" for="api_access">
                                        <strong>API Access</strong>
                                        <br><small class="text-muted">Access to REST API for integrations</small>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input type="checkbox" class="form-check-input" id="white_label"
                                           name="white_label" value="1" <?php echo e(old('white_label', $plan->white_label) ? 'checked' : ''); ?>>
                                    <label class="form-check-label" for="white_label">
                                        <strong>White Label</strong>
                                        <br><small class="text-muted">Remove branding and use custom domain</small>
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input type="checkbox" class="form-check-input" id="custom_branding"
                                           name="custom_branding" value="1" <?php echo e(old('custom_branding', $plan->custom_branding) ? 'checked' : ''); ?>>
                                    <label class="form-check-label" for="custom_branding">
                                        <strong>Custom Branding</strong>
                                        <br><small class="text-muted">Upload custom logos and colors</small>
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input type="checkbox" class="form-check-input" id="is_featured"
                                           name="is_featured" value="1" <?php echo e(old('is_featured', $plan->is_featured) ? 'checked' : ''); ?>>
                                    <label class="form-check-label" for="is_featured">
                                        <strong>Featured Plan</strong>
                                        <br><small class="text-muted">Highlight this plan as recommended</small>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="<?php echo e(route('super.plans.show', $plan)); ?>" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Plan
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Current Plan Preview -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Current Plan</h6>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <h4><?php echo e($plan->name); ?></h4>
                        <?php if($plan->is_featured): ?>
                            <span class="badge bg-warning text-dark mb-2">Featured</span>
                        <?php endif; ?>
                        <div class="h2 text-primary">$<?php echo e(number_format($plan->price, 2)); ?></div>
                        <small class="text-muted">per month</small>
                        <hr>
                        <div class="text-start">
                            <small class="text-muted">
                                <strong>Current Limits:</strong><br>
                                • <?php echo e($plan->branch_limit == 999 ? 'Unlimited' : $plan->branch_limit); ?> Branch(es)<br>
                                • <?php echo e($plan->user_limit == 999 ? 'Unlimited' : $plan->user_limit); ?> User(s)<br>
                                • <?php echo e($plan->order_limit === null ? 'Unlimited' : $plan->order_limit); ?> Orders/Month<br>
                                • <?php echo e($plan->data_retention_days == 999 ? 'Forever' : $plan->data_retention_days . ' Days'); ?> Data Retention<br>
                            </small>
                        </div>
                        <hr>
                        <div class="text-start">
                            <small class="text-muted">
                                <strong>Active Features:</strong><br>
                                <?php if($plan->thermal_printing): ?> • Thermal Printing<br> <?php endif; ?>
                                <?php if($plan->advanced_reporting): ?> • Advanced Reporting<br> <?php endif; ?>
                                <?php if($plan->api_access): ?> • API Access<br> <?php endif; ?>
                                <?php if($plan->white_label): ?> • White Label<br> <?php endif; ?>
                                <?php if($plan->custom_branding): ?> • Custom Branding<br> <?php endif; ?>
                                <?php if(!$plan->thermal_printing && !$plan->advanced_reporting && !$plan->api_access && !$plan->white_label && !$plan->custom_branding): ?>
                                    No additional features
                                <?php endif; ?>
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Usage Statistics -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Usage</h6>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="row">
                            <div class="col-6">
                                <div class="h5 text-primary"><?php echo e($plan->organizations()->count()); ?></div>
                                <small class="text-muted">Organizations</small>
                            </div>
                            <div class="col-6">
                                <div class="h5 text-success"><?php echo e($plan->subscriptions()->where('status', 'active')->count()); ?></div>
                                <small class="text-muted">Active Subs</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Live preview updates
document.addEventListener('DOMContentLoaded', function() {
    const nameInput = document.getElementById('name');
    const priceInput = document.getElementById('price');
    const branchInput = document.getElementById('branch_limit');
    const userInput = document.getElementById('user_limit');
    const retentionInput = document.getElementById('data_retention_days');

    function updatePreview() {
        // Update preview if elements exist (for create page)
        const previewName = document.getElementById('preview-name');
        const previewPrice = document.getElementById('preview-price');
        const previewBranches = document.getElementById('preview-branches');
        const previewUsers = document.getElementById('preview-users');
        const previewRetention = document.getElementById('preview-retention');

        if (previewName) previewName.textContent = nameInput.value || 'Plan Name';
        if (previewPrice) previewPrice.textContent = '$' + (parseFloat(priceInput.value) || 0).toFixed(2);
        if (previewBranches) previewBranches.textContent = branchInput.value == 999 ? 'Unlimited' : (branchInput.value || '1');
        if (previewUsers) previewUsers.textContent = userInput.value == 999 ? 'Unlimited' : (userInput.value || '5');
        if (previewRetention) previewRetention.textContent = retentionInput.value == 999 ? 'Forever' : (retentionInput.value || '365') + ' Days';
    }

    [nameInput, priceInput, branchInput, userInput, retentionInput].forEach(input => {
        if (input) input.addEventListener('input', updatePreview);
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('super_admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\SalesManagementSystem\resources\views/super_admin/plans/edit.blade.php ENDPATH**/ ?>