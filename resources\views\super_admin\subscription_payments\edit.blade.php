@extends('super_admin.layouts.app')

@section('title', 'Edit Payment')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Edit Payment</h1>
                <div>
                    <a href="{{ route('super.subscription-payments.show', $subscriptionPayment) }}" class="btn btn-secondary me-2">
                        <i class="fas fa-eye"></i> View Payment
                    </a>
                    <a href="{{ route('super.subscription-payments.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Payments
                    </a>
                </div>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if($errors->any())
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <ul class="mb-0">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if($subscriptionPayment->isApproved())
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Warning:</strong> This payment has already been approved. Changes should be made carefully.
                </div>
            @endif

            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Payment Information</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="{{ route('super.subscription-payments.update', $subscriptionPayment) }}">
                                @csrf
                                @method('PUT')

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="payment_reference" class="form-label">Payment Reference <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('payment_reference') is-invalid @enderror" 
                                                   id="payment_reference" name="payment_reference" 
                                                   value="{{ old('payment_reference', $subscriptionPayment->payment_reference) }}" required>
                                            @error('payment_reference')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="amount" class="form-label">Amount <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <span class="input-group-text">$</span>
                                                <input type="number" class="form-control @error('amount') is-invalid @enderror" 
                                                       id="amount" name="amount" step="0.01" min="0.01"
                                                       value="{{ old('amount', $subscriptionPayment->amount) }}" required>
                                            </div>
                                            @error('amount')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="payment_method" class="form-label">Payment Method <span class="text-danger">*</span></label>
                                            <select class="form-select @error('payment_method') is-invalid @enderror" 
                                                    id="payment_method" name="payment_method" required>
                                                <option value="">Select Payment Method</option>
                                                <option value="bank_transfer" {{ old('payment_method', $subscriptionPayment->payment_method) == 'bank_transfer' ? 'selected' : '' }}>Bank Transfer</option>
                                                <option value="cash" {{ old('payment_method', $subscriptionPayment->payment_method) == 'cash' ? 'selected' : '' }}>Cash</option>
                                                <option value="check" {{ old('payment_method', $subscriptionPayment->payment_method) == 'check' ? 'selected' : '' }}>Check</option>
                                                <option value="mobile_money" {{ old('payment_method', $subscriptionPayment->payment_method) == 'mobile_money' ? 'selected' : '' }}>Mobile Money</option>
                                            </select>
                                            @error('payment_method')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="payment_date" class="form-label">Payment Date <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control @error('payment_date') is-invalid @enderror" 
                                                   id="payment_date" name="payment_date" 
                                                   value="{{ old('payment_date', $subscriptionPayment->payment_date->format('Y-m-d')) }}" required>
                                            @error('payment_date')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="notes" class="form-label">Notes</label>
                                    <textarea class="form-control @error('notes') is-invalid @enderror" 
                                              id="notes" name="notes" rows="3"
                                              placeholder="Additional notes about this payment...">{{ old('notes', $subscriptionPayment->notes) }}</textarea>
                                    @error('notes')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                @if(!$subscriptionPayment->isApproved())
                                    <div class="mb-3">
                                        <label for="status" class="form-label">Status</label>
                                        <select class="form-select @error('status') is-invalid @enderror" 
                                                id="status" name="status">
                                            <option value="pending" {{ old('status', $subscriptionPayment->status) == 'pending' ? 'selected' : '' }}>Pending</option>
                                            <option value="approved" {{ old('status', $subscriptionPayment->status) == 'approved' ? 'selected' : '' }}>Approved</option>
                                            <option value="rejected" {{ old('status', $subscriptionPayment->status) == 'rejected' ? 'selected' : '' }}>Rejected</option>
                                        </select>
                                        @error('status')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                @endif

                                <div class="d-flex justify-content-between">
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>
                                            Update Payment
                                        </button>
                                        <a href="{{ route('super.subscription-payments.show', $subscriptionPayment) }}" class="btn btn-secondary">
                                            Cancel
                                        </a>
                                    </div>
                                    @if(!$subscriptionPayment->isApproved())
                                        <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                                            <i class="fas fa-trash me-2"></i>
                                            Delete Payment
                                        </button>
                                    @endif
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- Organization & Subscription Info -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Organization & Subscription</h5>
                        </div>
                        <div class="card-body">
                            <h6 class="text-primary">{{ $subscriptionPayment->organization->name }}</h6>
                            <p class="text-muted mb-2">{{ $subscriptionPayment->subscription->plan->name }}</p>
                            <p class="text-muted mb-2">Monthly: ${{ number_format($subscriptionPayment->subscription->plan->price, 2) }}</p>
                            <p class="text-muted mb-0">
                                Status: 
                                <span class="badge bg-{{ $subscriptionPayment->subscription->status === 'active' ? 'success' : 'warning' }}">
                                    {{ ucfirst($subscriptionPayment->subscription->status) }}
                                </span>
                            </p>
                        </div>
                    </div>

                    <!-- Financial Summary -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Financial Summary</h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <h6 class="text-muted">Total Paid</h6>
                                    <h5 class="text-success">${{ number_format($subscriptionPayment->subscription->calculateTotalPaid(), 2) }}</h5>
                                </div>
                                <div class="col-6">
                                    <h6 class="text-muted">Outstanding</h6>
                                    <h5 class="text-warning">${{ number_format($subscriptionPayment->subscription->getOutstandingBalance(), 2) }}</h5>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Quick Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                @if($subscriptionPayment->isPending())
                                    <form method="POST" action="{{ route('super.subscription-payments.approve', $subscriptionPayment) }}">
                                        @csrf
                                        <button type="submit" class="btn btn-success w-100" onclick="return confirm('Approve this payment?')">
                                            <i class="fas fa-check me-2"></i>
                                            Approve Payment
                                        </button>
                                    </form>
                                @endif
                                
                                <a href="{{ route('super.subscription-payments.show', $subscriptionPayment) }}" class="btn btn-outline-info">
                                    <i class="fas fa-eye me-2"></i>
                                    View Details
                                </a>
                                
                                <a href="{{ route('super.organizations.show', $subscriptionPayment->organization) }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-building me-2"></i>
                                    View Organization
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Form -->
<form id="deleteForm" method="POST" action="{{ route('super.subscription-payments.destroy', $subscriptionPayment) }}" style="display: none;">
    @csrf
    @method('DELETE')
</form>

<script>
function confirmDelete() {
    if (confirm('Are you sure you want to delete this payment record? This action cannot be undone.')) {
        document.getElementById('deleteForm').submit();
    }
}
</script>
@endsection
