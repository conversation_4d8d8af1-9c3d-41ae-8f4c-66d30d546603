@extends('super_admin.layouts.app')

@section('title', 'Payment Accounts')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Payment Accounts</h1>
                <a href="{{ route('super.payment-accounts.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Add Payment Account
                </a>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error') || $errors->any())
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') ?? $errors->first() }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Manage Payment Accounts</h5>
                </div>
                <div class="card-body">
                    @if($accounts->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Bank Name</th>
                                        <th>Account Name</th>
                                        <th>Account Number</th>
                                        <th>Type</th>
                                        <th>Status</th>
                                        <th>Primary</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($accounts as $account)
                                        <tr>
                                            <td>
                                                <strong>{{ $account->bank_name }}</strong>
                                                @if($account->swift_code)
                                                    <br><small class="text-muted">SWIFT: {{ $account->swift_code }}</small>
                                                @endif
                                            </td>
                                            <td>{{ $account->account_name }}</td>
                                            <td>
                                                <code>{{ $account->masked_account_number }}</code>
                                                <button class="btn btn-sm btn-outline-secondary ms-1" 
                                                        onclick="showFullAccountNumber('{{ $account->account_number }}')"
                                                        title="Show full account number">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">{{ ucfirst($account->account_type) }}</span>
                                            </td>
                                            <td>
                                                @if($account->is_active)
                                                    <span class="badge bg-success">Active</span>
                                                @else
                                                    <span class="badge bg-danger">Inactive</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($account->is_primary)
                                                    <span class="badge bg-primary">Primary</span>
                                                @else
                                                    <form method="POST" action="{{ route('super.payment-accounts.set-primary', $account) }}" class="d-inline">
                                                        @csrf
                                                        <button type="submit" class="btn btn-sm btn-outline-primary" 
                                                                onclick="return confirm('Set this as primary account?')">
                                                            Set Primary
                                                        </button>
                                                    </form>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('super.payment-accounts.show', $account) }}" 
                                                       class="btn btn-sm btn-outline-info">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('super.payment-accounts.edit', $account) }}" 
                                                       class="btn btn-sm btn-outline-warning">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form method="POST" action="{{ route('super.payment-accounts.toggle-status', $account) }}" class="d-inline">
                                                        @csrf
                                                        <button type="submit" class="btn btn-sm btn-outline-{{ $account->is_active ? 'danger' : 'success' }}"
                                                                onclick="return confirm('{{ $account->is_active ? 'Deactivate' : 'Activate' }} this account?')">
                                                            <i class="fas fa-{{ $account->is_active ? 'ban' : 'check' }}"></i>
                                                        </button>
                                                    </form>
                                                    @if(!$account->is_primary || $accounts->where('is_active', true)->count() > 1)
                                                        <form method="POST" action="{{ route('super.payment-accounts.destroy', $account) }}" class="d-inline">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="btn btn-sm btn-outline-danger"
                                                                    onclick="return confirm('Are you sure you want to delete this account?')">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </form>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-university fa-3x text-muted mb-3"></i>
                            <h5>No Payment Accounts</h5>
                            <p class="text-muted">Add your first payment account to start receiving payments.</p>
                            <a href="{{ route('super.payment-accounts.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Add Payment Account
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Account Number Modal -->
<div class="modal fade" id="accountNumberModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Full Account Number</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <h4 id="fullAccountNumber" class="font-monospace"></h4>
                    <button class="btn btn-outline-secondary" onclick="copyAccountNumber()">
                        <i class="fas fa-copy"></i> Copy
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function showFullAccountNumber(accountNumber) {
    document.getElementById('fullAccountNumber').textContent = accountNumber;
    new bootstrap.Modal(document.getElementById('accountNumberModal')).show();
}

function copyAccountNumber() {
    const accountNumber = document.getElementById('fullAccountNumber').textContent;
    navigator.clipboard.writeText(accountNumber).then(() => {
        alert('Account number copied to clipboard!');
    });
}
</script>
@endsection
