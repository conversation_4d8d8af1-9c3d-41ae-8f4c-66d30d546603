@extends('layouts.app')

@section('title', 'Orders')

@section('content')
<div class="py-6 md:py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center mb-6">
            <div class="flex items-center space-x-4">
                <h2 class="text-2xl font-bold">Orders</h2>
                <form id="receiptForm" action="{{ route('orders.receipt.multiple') }}" method="POST" class="inline">
                    @csrf
                    <input type="hidden" name="order_ids" id="selectedOrderIds">
                    <button type="submit" id="generateReceiptBtn" disabled
                        class="bg-gray-300 text-white px-4 py-2 rounded-lg inline-flex items-center transition-colors disabled:opacity-50 disabled:cursor-not-allowed hover:bg-blue-600">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                        </svg>
                        Generate Receipt
                    </button>
                </form>
            </div>
            @if(auth()->user()->hasRole('Staff'))
                <a href="{{ route('orders.create') }}" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center justify-center gap-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                    </svg>
                    Create Order
                </a>
            @endif
        </div>

        <!-- Search and Create Order -->
        <div class="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6">
            <form action="{{ route('orders.index') }}" method="GET" class="flex-1 flex flex-col sm:flex-row gap-2">
                <div class="flex-1 relative">
                    <input type="search"
                           name="search"
                           value="{{ request('search') }}"
                           placeholder="Search orders..."
                           class="w-full rounded-md border-gray-300 pl-10 focus:border-blue-500 focus:ring-blue-500">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                    </div>
                </div>
                <div class="flex gap-2">
                    <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 flex-shrink-0">
                        Search
                    </button>
                    @if(request()->has('search'))
                        <a href="{{ route('orders.index') }}" class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 flex-shrink-0">
                            Clear
                        </a>
                    @endif
                </div>
            </form>
        </div>

        <!-- Date Filter Form -->
        <div class="bg-white p-4 rounded-lg shadow mb-6">
            <form action="{{ route('orders.index') }}" method="GET" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                    <label for="start_date" class="block text-sm font-medium text-gray-700">Start Date</label>
                    <input type="date" name="start_date" value="{{ request('start_date') }}" class="mt-1 block w-full rounded-md border-gray-300">
                </div>
                <div>
                    <label for="end_date" class="block text-sm font-medium text-gray-700">End Date</label>
                    <input type="date" name="end_date" value="{{ request('end_date') }}" class="mt-1 block w-full rounded-md border-gray-300">
                </div>
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                    <select name="status" class="mt-1 block w-full rounded-md border-gray-300">
                        <option value="">All Statuses</option>
                        <option value="Pending" {{ request('status') == 'Pending' ? 'selected' : '' }}>Pending</option>
                        <option value="Processing" {{ request('status') == 'Processing' ? 'selected' : '' }}>Processing</option>
                        <option value="Completed" {{ request('status') == 'Completed' ? 'selected' : '' }}>Completed</option>
                        <option value="Delivered" {{ request('status') == 'Delivered' ? 'selected' : '' }}>Delivered</option>
                    </select>
                </div>
                <div class="flex items-end gap-2">
                    <button type="submit" class="flex-1 bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">
                        Filter
                    </button>
                    @if(request()->hasAny(['start_date', 'end_date', 'status']))
                        <a href="{{ route('orders.index') }}" class="flex-1 bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 text-center">
                            Reset
                        </a>
                    @endif
                </div>
            </form>
        </div>

        <!-- Orders List -->
        <div class="bg-white shadow-sm rounded-lg overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="w-12 px-4 py-3">
                                <input type="checkbox" id="selectAllOrders" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            </th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order Info</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">Customer</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell">Status</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell">Amount</th>
                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($orders as $order)
                            <tr>
                                <td class="px-4 py-4">
                                    @if($order->customer_name)
                                        <input type="checkbox"
                                               class="order-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                               data-order-id="{{ $order->id }}"
                                               data-customer="{{ $order->customer_name }}">
                                    @endif
                                </td>
                                <td class="px-4 py-4">
                                    <div class="text-sm font-medium text-gray-900">{{ $order->order_number }}</div>
                                    <div class="text-sm text-gray-500 sm:hidden">{{ $order->customer_name }}</div>
                                    <div class="text-xs text-gray-500">Created by: {{ $order->user ? $order->user->name : 'System' }}</div>
                                    <div class="text-sm text-gray-500 md:hidden">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $order->status_badge_color }}">
                                            {{ $order->status }}
                                        </span>
                                    </div>
                                    <div class="text-sm text-gray-500 lg:hidden">{{ format_money($order->total_amount) }}</div>
                                </td>
                                <td class="px-4 py-4 hidden sm:table-cell">
                                    <div class="text-sm text-gray-900">{{ $order->customer_name }}</div>
                                    <div class="text-sm text-gray-500">{{ $order->phone_number }}</div>
                                </td>
                                <td class="px-4 py-4 hidden md:table-cell">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $order->status_badge_color }}">
                                        {{ $order->status }}
                                    </span>
                                </td>
                                <td class="px-4 py-4 hidden lg:table-cell">
                                    <div class="text-sm text-gray-900">{{ format_money($order->total_amount) }}</div>
                                    @if($order->pending_payment > 0)
                                        <div class="text-xs text-red-500">Pending: {{ format_money($order->pending_payment) }}</div>
                                    @endif
                                </td>
                                <td class="px-4 py-4 text-right text-sm font-medium">
                                    <div class="flex justify-end items-center space-x-2">
                                        <a href="{{ route('orders.show', $order) }}" class="text-blue-600 hover:text-blue-900">View</a>
                                        <a href="{{ route('orders.receipt', $order) }}" target="_blank" class="text-green-600 hover:text-green-900">Receipt</a>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="px-4 py-3 border-t border-gray-200">
                {{ $orders->links() }}
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');

    // Set max date to today
    const today = new Date().toISOString().split('T')[0];
    startDateInput.setAttribute('max', today);
    endDateInput.setAttribute('max', today);

    // Update min date of end date input when start date changes
    startDateInput.addEventListener('change', function() {
        endDateInput.setAttribute('min', this.value);
        if (endDateInput.value && endDateInput.value < this.value) {
            endDateInput.value = this.value;
        }
    });

    // Update max date of start date input when end date changes
    endDateInput.addEventListener('change', function() {
        startDateInput.setAttribute('max', this.value);
        if (startDateInput.value && startDateInput.value > this.value) {
            startDateInput.value = this.value;
        }
    });
});
</script>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const generateReceiptBtn = document.getElementById('generateReceiptBtn');
    const selectAllCheckbox = document.getElementById('selectAllOrders');
    const orderCheckboxes = document.querySelectorAll('.order-checkbox');
    const receiptForm = document.getElementById('receiptForm');
    const selectedOrderIdsInput = document.getElementById('selectedOrderIds');
    let selectedCustomer = null;

    // Handle individual checkbox changes
    orderCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const customerName = this.dataset.customer;
            const isChecked = this.checked;

            if (isChecked) {
                if (!selectedCustomer) {
                    selectedCustomer = customerName;
                } else if (selectedCustomer !== customerName) {
                    this.checked = false;
                    Swal.fire({
                        icon: 'warning',
                        title: 'Different Customer',
                        text: 'You can only select orders from the same customer',
                        toast: true,
                        position: 'top-end',
                        showConfirmButton: false,
                        timer: 3000
                    });
                    return;
                }
            } else if (!anyCheckboxChecked()) {
                selectedCustomer = null;
            }

            updateGenerateButton();
            updateSelectedOrderIds();
        });
    });

    // Handle select all checkbox
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const isChecked = this.checked;

            if (isChecked && !selectedCustomer) {
                // Find the first available customer
                const firstCheckbox = [...orderCheckboxes].find(cb => cb.dataset.customer);
                if (firstCheckbox) {
                    selectedCustomer = firstCheckbox.dataset.customer;
                }
            }

            orderCheckboxes.forEach(checkbox => {
                if (!selectedCustomer || checkbox.dataset.customer === selectedCustomer) {
                    checkbox.checked = isChecked;
                }
            });

            if (!isChecked) {
                selectedCustomer = null;
            }

            updateGenerateButton();
            updateSelectedOrderIds();
        });
    }

    function anyCheckboxChecked() {
        return [...orderCheckboxes].some(cb => cb.checked);
    }

    function updateSelectedOrderIds() {
        const selectedIds = [...orderCheckboxes]
            .filter(cb => cb.checked)
            .map(cb => cb.dataset.orderId);
        selectedOrderIdsInput.value = selectedIds.join(',');
    }

    function updateGenerateButton() {
        const anyChecked = anyCheckboxChecked();
        generateReceiptBtn.disabled = !anyChecked;
        generateReceiptBtn.classList.toggle('bg-gray-300', !anyChecked);
        generateReceiptBtn.classList.toggle('bg-blue-500', anyChecked);
    }

    // Initialize button state
    updateGenerateButton();
});
</script>
@endpush

