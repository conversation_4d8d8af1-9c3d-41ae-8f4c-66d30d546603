@extends('super_admin.layouts.app')

@section('title', 'Create Plan')
@section('page-title', 'Create Plan')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Create Subscription Plan</h1>
            <p class="text-muted">Add a new subscription plan</p>
        </div>
        <a href="{{ route('super.plans.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Plans
        </a>
    </div>

    <!-- Create Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Plan Details</h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('super.plans.store') }}">
                        @csrf

                        <!-- Basic Information -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="name" class="form-label">Plan Name *</label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror"
                                       id="name" name="name" value="{{ old('name') }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="price" class="form-label">Monthly Price ($) *</label>
                                <input type="number" step="0.01" min="0"
                                       class="form-control @error('price') is-invalid @enderror"
                                       id="price" name="price" value="{{ old('price') }}" required>
                                @error('price')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror"
                                      id="description" name="description" rows="3">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Annual Billing -->
                        <h5 class="mb-3">Billing Options</h5>
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <label for="billing_period" class="form-label">Billing Period *</label>
                                <select class="form-control @error('billing_period') is-invalid @enderror"
                                        id="billing_period" name="billing_period" required>
                                    <option value="monthly" {{ old('billing_period', 'both') == 'monthly' ? 'selected' : '' }}>Monthly Only</option>
                                    <option value="annual" {{ old('billing_period') == 'annual' ? 'selected' : '' }}>Annual Only</option>
                                    <option value="both" {{ old('billing_period', 'both') == 'both' ? 'selected' : '' }}>Both Monthly & Annual</option>
                                </select>
                                @error('billing_period')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-4">
                                <label for="annual_price" class="form-label">Annual Price ($)</label>
                                <input type="number" step="0.01" min="0"
                                       class="form-control @error('annual_price') is-invalid @enderror"
                                       id="annual_price" name="annual_price" value="{{ old('annual_price') }}">
                                <small class="text-muted">Leave empty to use discount percentage</small>
                                @error('annual_price')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-4">
                                <label for="annual_discount_percentage" class="form-label">Annual Discount (%)</label>
                                <input type="number" step="1" min="0" max="50"
                                       class="form-control @error('annual_discount_percentage') is-invalid @enderror"
                                       id="annual_discount_percentage" name="annual_discount_percentage" value="{{ old('annual_discount_percentage', 15) }}">
                                <small class="text-muted">Used if no annual price set</small>
                                @error('annual_discount_percentage')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Limits -->
                        <h5 class="mb-3">Plan Limits</h5>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <label for="branch_limit" class="form-label">Branch Limit *</label>
                                <input type="number" min="1" max="999"
                                       class="form-control @error('branch_limit') is-invalid @enderror"
                                       id="branch_limit" name="branch_limit" value="{{ old('branch_limit', 1) }}" required>
                                <small class="text-muted">Use 999 for unlimited</small>
                                @error('branch_limit')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-3">
                                <label for="user_limit" class="form-label">User Limit *</label>
                                <input type="number" min="1" max="999"
                                       class="form-control @error('user_limit') is-invalid @enderror"
                                       id="user_limit" name="user_limit" value="{{ old('user_limit', 5) }}" required>
                                <small class="text-muted">Use 999 for unlimited</small>
                                @error('user_limit')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-3">
                                <label for="order_limit" class="form-label">Order Limit (Monthly)</label>
                                <input type="number" min="1"
                                       class="form-control @error('order_limit') is-invalid @enderror"
                                       id="order_limit" name="order_limit" value="{{ old('order_limit', 50) }}">
                                <small class="text-muted">Leave empty for unlimited</small>
                                @error('order_limit')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-3">
                                <label for="data_retention_days" class="form-label">Data Retention (Days) *</label>
                                <input type="number" min="1" max="999"
                                       class="form-control @error('data_retention_days') is-invalid @enderror"
                                       id="data_retention_days" name="data_retention_days" value="{{ old('data_retention_days', 365) }}" required>
                                <small class="text-muted">Use 999 for forever</small>
                                @error('data_retention_days')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Features -->
                        <h5 class="mb-3">Plan Features</h5>
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input type="checkbox" class="form-check-input" id="thermal_printing"
                                           name="thermal_printing" value="1" {{ old('thermal_printing') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="thermal_printing">
                                        <strong>Thermal Printing</strong>
                                        <br><small class="text-muted">Enable thermal receipt printing</small>
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input type="checkbox" class="form-check-input" id="advanced_reporting"
                                           name="advanced_reporting" value="1" {{ old('advanced_reporting') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="advanced_reporting">
                                        <strong>Advanced Reporting</strong>
                                        <br><small class="text-muted">Access to detailed analytics and reports</small>
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input type="checkbox" class="form-check-input" id="api_access"
                                           name="api_access" value="1" {{ old('api_access') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="api_access">
                                        <strong>API Access</strong>
                                        <br><small class="text-muted">Access to REST API for integrations</small>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input type="checkbox" class="form-check-input" id="white_label"
                                           name="white_label" value="1" {{ old('white_label') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="white_label">
                                        <strong>White Label</strong>
                                        <br><small class="text-muted">Remove branding and use custom domain</small>
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input type="checkbox" class="form-check-input" id="custom_branding"
                                           name="custom_branding" value="1" {{ old('custom_branding') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="custom_branding">
                                        <strong>Custom Branding</strong>
                                        <br><small class="text-muted">Upload custom logos and colors</small>
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input type="checkbox" class="form-check-input" id="is_featured"
                                           name="is_featured" value="1" {{ old('is_featured') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_featured">
                                        <strong>Featured Plan</strong>
                                        <br><small class="text-muted">Highlight this plan as recommended</small>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('super.plans.index') }}" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Create Plan
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Preview Card -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Plan Preview</h6>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <h4 id="preview-name">Plan Name</h4>
                        <div class="h2 text-primary" id="preview-price">$0.00</div>
                        <small class="text-muted" id="preview-period">per month</small>

                        <!-- Annual Pricing Display -->
                        <div id="preview-annual" style="display: none;" class="mt-2">
                            <div class="h4 text-success" id="preview-annual-price">$0.00</div>
                            <small class="text-muted">per year</small>
                            <div class="badge bg-success" id="preview-savings" style="display: none;">Save 0%</div>
                        </div>
                        <hr>
                        <div class="text-start">
                            <small class="text-muted">
                                <strong>Includes:</strong><br>
                                • <span id="preview-branches">1</span> Branch(es)<br>
                                • <span id="preview-users">5</span> User(s)<br>
                                • <span id="preview-orders">50</span> Orders/Month<br>
                                • <span id="preview-retention">365</span> Days Data Retention<br>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Live preview updates
document.addEventListener('DOMContentLoaded', function() {
    const nameInput = document.getElementById('name');
    const priceInput = document.getElementById('price');
    const annualPriceInput = document.getElementById('annual_price');
    const annualDiscountInput = document.getElementById('annual_discount_percentage');
    const billingPeriodInput = document.getElementById('billing_period');
    const branchInput = document.getElementById('branch_limit');
    const userInput = document.getElementById('user_limit');
    const orderInput = document.getElementById('order_limit');
    const retentionInput = document.getElementById('data_retention_days');

    function updatePreview() {
        const monthlyPrice = parseFloat(priceInput.value) || 0;
        const annualPrice = parseFloat(annualPriceInput.value) || 0;
        const discountPercentage = parseInt(annualDiscountInput.value) || 0;
        const billingPeriod = billingPeriodInput.value;

        // Update basic info
        document.getElementById('preview-name').textContent = nameInput.value || 'Plan Name';
        document.getElementById('preview-price').textContent = '$' + monthlyPrice.toFixed(2);
        document.getElementById('preview-branches').textContent = branchInput.value == 999 ? 'Unlimited' : (branchInput.value || '1');
        document.getElementById('preview-users').textContent = userInput.value == 999 ? 'Unlimited' : (userInput.value || '5');
        document.getElementById('preview-orders').textContent = !orderInput.value ? 'Unlimited' : (orderInput.value || '50');
        document.getElementById('preview-retention').textContent = retentionInput.value == 999 ? 'Forever' : (retentionInput.value || '365') + ' Days';

        // Update annual pricing display
        const annualSection = document.getElementById('preview-annual');
        const savingsBadge = document.getElementById('preview-savings');

        if (billingPeriod === 'annual' || billingPeriod === 'both') {
            let effectiveAnnualPrice = annualPrice;

            // Calculate annual price if not explicitly set
            if (!effectiveAnnualPrice && discountPercentage > 0) {
                const monthlyTotal = monthlyPrice * 12;
                effectiveAnnualPrice = monthlyTotal * (1 - discountPercentage / 100);
            }

            if (effectiveAnnualPrice > 0) {
                document.getElementById('preview-annual-price').textContent = '$' + effectiveAnnualPrice.toFixed(2);
                annualSection.style.display = 'block';

                // Calculate and show savings
                const monthlyTotal = monthlyPrice * 12;
                const savings = monthlyTotal - effectiveAnnualPrice;
                const savingsPercentage = ((savings / monthlyTotal) * 100).toFixed(0);

                if (savings > 0) {
                    savingsBadge.textContent = `Save ${savingsPercentage}%`;
                    savingsBadge.style.display = 'inline-block';
                } else {
                    savingsBadge.style.display = 'none';
                }
            } else {
                annualSection.style.display = 'none';
            }
        } else {
            annualSection.style.display = 'none';
        }

        // Update period label
        if (billingPeriod === 'annual') {
            document.getElementById('preview-period').textContent = 'per year';
            document.getElementById('preview-price').textContent = '$' + (annualPrice || (monthlyPrice * 12 * (1 - discountPercentage / 100))).toFixed(2);
        } else {
            document.getElementById('preview-period').textContent = 'per month';
        }
    }

    [nameInput, priceInput, annualPriceInput, annualDiscountInput, billingPeriodInput, branchInput, userInput, orderInput, retentionInput].forEach(input => {
        input.addEventListener('input', updatePreview);
        input.addEventListener('change', updatePreview);
    });

    // Initial update
    updatePreview();
});
</script>
@endsection
