<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        // Register custom middleware aliases
        $middleware->alias([
            'role' => \App\Http\Middleware\Role::class,
            'super_admin' => \App\Http\Middleware\SuperAdminMiddleware::class,
            'plan_feature' => \App\Http\Middleware\CheckPlanFeatures::class,
            'plan_limit' => \App\Http\Middleware\CheckPlanLimits::class,
            'impersonation' => \App\Http\Middleware\ImpersonationMiddleware::class,
        ]);

        // Add impersonation middleware to web group
        $middleware->web(append: [
            \App\Http\Middleware\ImpersonationMiddleware::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
