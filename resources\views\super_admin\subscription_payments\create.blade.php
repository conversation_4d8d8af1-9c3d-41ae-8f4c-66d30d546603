@extends('super_admin.layouts.app')

@section('title', 'Add Payment Record')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Add Payment Record</h1>
                <a href="{{ route('super.subscription-payments.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Payments
                </a>
            </div>

            @if($errors->any())
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <ul class="mb-0">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Payment Information</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="{{ route('super.subscription-payments.store') }}">
                                @csrf

                                <div class="mb-3">
                                    <label for="organization_id" class="form-label">Organization <span class="text-danger">*</span></label>
                                    <select class="form-select @error('organization_id') is-invalid @enderror" 
                                            id="organization_id" name="organization_id" required onchange="loadSubscriptions()">
                                        <option value="">Select Organization</option>
                                        @foreach($organizations as $organization)
                                            <option value="{{ $organization->id }}" {{ old('organization_id') == $organization->id ? 'selected' : '' }}>
                                                {{ $organization->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('organization_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="subscription_id" class="form-label">Subscription <span class="text-danger">*</span></label>
                                    <select class="form-select @error('subscription_id') is-invalid @enderror" 
                                            id="subscription_id" name="subscription_id" required disabled>
                                        <option value="">Select Organization First</option>
                                    </select>
                                    @error('subscription_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="payment_reference" class="form-label">Payment Reference <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('payment_reference') is-invalid @enderror" 
                                                   id="payment_reference" name="payment_reference" 
                                                   value="{{ old('payment_reference') }}" required
                                                   placeholder="Bank transfer ID, receipt number, etc.">
                                            @error('payment_reference')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="amount" class="form-label">Amount <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <span class="input-group-text">$</span>
                                                <input type="number" class="form-control @error('amount') is-invalid @enderror" 
                                                       id="amount" name="amount" step="0.01" min="0.01"
                                                       value="{{ old('amount') }}" required>
                                            </div>
                                            @error('amount')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="payment_method" class="form-label">Payment Method <span class="text-danger">*</span></label>
                                            <select class="form-select @error('payment_method') is-invalid @enderror" 
                                                    id="payment_method" name="payment_method" required>
                                                <option value="">Select Payment Method</option>
                                                <option value="bank_transfer" {{ old('payment_method') == 'bank_transfer' ? 'selected' : '' }}>Bank Transfer</option>
                                                <option value="cash" {{ old('payment_method') == 'cash' ? 'selected' : '' }}>Cash</option>
                                                <option value="check" {{ old('payment_method') == 'check' ? 'selected' : '' }}>Check</option>
                                                <option value="mobile_money" {{ old('payment_method') == 'mobile_money' ? 'selected' : '' }}>Mobile Money</option>
                                            </select>
                                            @error('payment_method')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="payment_date" class="form-label">Payment Date <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control @error('payment_date') is-invalid @enderror" 
                                                   id="payment_date" name="payment_date" 
                                                   value="{{ old('payment_date', date('Y-m-d')) }}" required>
                                            @error('payment_date')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="notes" class="form-label">Notes</label>
                                    <textarea class="form-control @error('notes') is-invalid @enderror" 
                                              id="notes" name="notes" rows="3"
                                              placeholder="Additional notes about this payment...">{{ old('notes') }}</textarea>
                                    @error('notes')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="status" class="form-label">Status</label>
                                    <select class="form-select @error('status') is-invalid @enderror" 
                                            id="status" name="status">
                                        <option value="pending" {{ old('status', 'pending') == 'pending' ? 'selected' : '' }}>Pending</option>
                                        <option value="approved" {{ old('status') == 'approved' ? 'selected' : '' }}>Approved</option>
                                        <option value="rejected" {{ old('status') == 'rejected' ? 'selected' : '' }}>Rejected</option>
                                    </select>
                                    @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="d-flex justify-content-between">
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>
                                            Create Payment Record
                                        </button>
                                        <a href="{{ route('super.subscription-payments.index') }}" class="btn btn-secondary">
                                            Cancel
                                        </a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- Instructions -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Instructions</h5>
                        </div>
                        <div class="card-body">
                            <h6 class="text-primary">Adding Payment Records</h6>
                            <ul class="small text-muted">
                                <li>Select the organization first to load their subscriptions</li>
                                <li>Choose the subscription this payment is for</li>
                                <li>Enter the payment reference (bank transfer ID, receipt number, etc.)</li>
                                <li>Set the payment amount and method</li>
                                <li>Choose the appropriate status</li>
                            </ul>
                            
                            <h6 class="text-info mt-3">Payment Status</h6>
                            <ul class="small text-muted">
                                <li><strong>Pending:</strong> Awaiting review</li>
                                <li><strong>Approved:</strong> Payment accepted and processed</li>
                                <li><strong>Rejected:</strong> Payment declined</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Selected Organization Info -->
                    <div class="card" id="organizationInfo" style="display: none;">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Organization Details</h5>
                        </div>
                        <div class="card-body" id="organizationDetails">
                            <!-- Will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
async function loadSubscriptions() {
    const organizationId = document.getElementById('organization_id').value;
    const subscriptionSelect = document.getElementById('subscription_id');
    const organizationInfo = document.getElementById('organizationInfo');
    const organizationDetails = document.getElementById('organizationDetails');
    
    // Reset subscription dropdown
    subscriptionSelect.innerHTML = '<option value="">Loading...</option>';
    subscriptionSelect.disabled = true;
    
    if (!organizationId) {
        subscriptionSelect.innerHTML = '<option value="">Select Organization First</option>';
        organizationInfo.style.display = 'none';
        return;
    }
    
    try {
        // Load subscriptions for the selected organization
        const response = await fetch(`/super-admin/organizations/${organizationId}/subscriptions`);
        const data = await response.json();
        
        // Populate subscription dropdown
        subscriptionSelect.innerHTML = '<option value="">Select Subscription</option>';
        data.subscriptions.forEach(subscription => {
            const option = document.createElement('option');
            option.value = subscription.id;
            option.textContent = `${subscription.plan.name} - $${subscription.plan.price}/month (${subscription.status})`;
            subscriptionSelect.appendChild(option);
        });
        
        subscriptionSelect.disabled = false;
        
        // Show organization info
        organizationDetails.innerHTML = `
            <h6 class="text-primary">${data.organization.name}</h6>
            <p class="text-muted mb-1">Active Subscriptions: ${data.subscriptions.length}</p>
            <p class="text-muted mb-0">Status: <span class="badge bg-${data.organization.is_active ? 'success' : 'danger'}">${data.organization.is_active ? 'Active' : 'Inactive'}</span></p>
        `;
        organizationInfo.style.display = 'block';
        
    } catch (error) {
        console.error('Error loading subscriptions:', error);
        subscriptionSelect.innerHTML = '<option value="">Error loading subscriptions</option>';
    }
}
</script>
@endsection
