@extends('affiliate.layouts.app')

@section('title', 'Earnings')
@section('page-title', 'Earnings')

@section('content')
<div class="container-fluid">
    <!-- Summary Cards -->
    <div class="row mb-4">
        <!-- Total Earnings -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Earnings
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">${{ number_format($summary['total_earnings'] ?? 0, 2) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Available Balance -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Available Balance
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">${{ number_format($summary['available_balance'] ?? 0, 2) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-wallet fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pending Earnings -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Pending Earnings
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">${{ number_format($summary['pending_earnings'] ?? 0, 2) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Withdrawn -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Total Withdrawn
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">${{ number_format($summary['total_withdrawn'] ?? 0, 2) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">Ready to withdraw your earnings?</h6>
                            <p class="text-muted mb-0">You have ${{ number_format($summary['available_balance'] ?? 0, 2) }} available for withdrawal.</p>
                        </div>
                        <div>
                            @if(($summary['available_balance'] ?? 0) > 0)
                                <a href="{{ route('affiliate.withdrawals.create') }}" class="btn btn-success">
                                    <i class="fas fa-money-bill-wave me-2"></i>Request Withdrawal
                                </a>
                            @else
                                <button class="btn btn-secondary" disabled>
                                    <i class="fas fa-money-bill-wave me-2"></i>No Balance Available
                                </button>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filter Earnings</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('affiliate.earnings') }}">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select id="status" name="status" class="form-select">
                            <option value="">All Statuses</option>
                            <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>Pending</option>
                            <option value="approved" {{ request('status') === 'approved' ? 'selected' : '' }}>Approved</option>
                            <option value="paid" {{ request('status') === 'paid' ? 'selected' : '' }}>Paid</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="organization" class="form-label">Organization</label>
                        <input type="text" id="organization" name="organization" value="{{ request('organization') }}" 
                               placeholder="Organization name" class="form-control">
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="date_from" class="form-label">From Date</label>
                        <input type="date" id="date_from" name="date_from" value="{{ request('date_from') }}" 
                               class="form-control">
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="date_to" class="form-label">To Date</label>
                        <input type="date" id="date_to" name="date_to" value="{{ request('date_to') }}" 
                               class="form-control">
                    </div>
                    <div class="col-md-2 mb-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>Filter
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Earnings Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Earnings History</h6>
        </div>
        <div class="card-body">
            @if($earnings->count() > 0)
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Organization</th>
                                <th>Description</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Payment Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($earnings as $earning)
                                <tr>
                                    <td>
                                        {{ $earning->earned_at ? $earning->earned_at->format('M d, Y') : 'N/A' }}
                                        @if($earning->earned_at)
                                            <br>
                                            <small class="text-muted">{{ $earning->earned_at->format('h:i A') }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        @if($earning->referral && $earning->referral->organization)
                                            <div>
                                                <strong>{{ $earning->referral->organization->name }}</strong>
                                                <br>
                                                <small class="text-muted">{{ $earning->referral->organization->industry ?? 'Not specified' }}</small>
                                            </div>
                                        @else
                                            <span class="text-muted">Organization not found</span>
                                        @endif
                                    </td>
                                    <td>
                                        {{ $earning->description ?? 'Commission payment' }}
                                        @if($earning->subscription_payment_id)
                                            <br>
                                            <small class="text-muted">Payment #{{ $earning->subscription_payment_id }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        <strong class="text-success">${{ number_format($earning->amount, 2) }}</strong>
                                        @if($earning->commission_rate)
                                            <br>
                                            <small class="text-muted">{{ number_format($earning->commission_rate, 1) }}% commission</small>
                                        @endif
                                    </td>
                                    <td>
                                        @if($earning->status === 'pending')
                                            <span class="badge bg-warning">Pending</span>
                                        @elseif($earning->status === 'approved')
                                            <span class="badge bg-info">Approved</span>
                                        @elseif($earning->status === 'paid')
                                            <span class="badge bg-success">Paid</span>
                                        @else
                                            <span class="badge bg-secondary">{{ ucfirst($earning->status) }}</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($earning->paid_at)
                                            {{ $earning->paid_at->format('M d, Y') }}
                                            <br>
                                            <small class="text-muted">{{ $earning->paid_at->format('h:i A') }}</small>
                                        @else
                                            <span class="text-muted">Not paid yet</span>
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center">
                    {{ $earnings->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-chart-line fa-4x text-gray-300 mb-3"></i>
                    <h5 class="text-gray-600">No earnings found</h5>
                    <p class="text-muted">Start referring customers to earn your first commission!</p>
                    <a href="{{ route('affiliate.referral-tools') }}" class="btn btn-primary">
                        <i class="fas fa-link me-2"></i>Get Referral Links
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
