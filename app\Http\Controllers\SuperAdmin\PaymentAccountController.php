<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\PaymentAccount;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class PaymentAccountController extends Controller
{
    /**
     * Display a listing of payment accounts.
     */
    public function index()
    {
        $accounts = PaymentAccount::orderBy('is_primary', 'desc')
                                 ->orderBy('is_active', 'desc')
                                 ->orderBy('created_at', 'desc')
                                 ->get();

        return view('super_admin.payment_accounts.index', compact('accounts'));
    }

    /**
     * Show the form for creating a new payment account.
     */
    public function create()
    {
        return view('super_admin.payment_accounts.create');
    }

    /**
     * Store a newly created payment account.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'account_name' => 'required|string|max:255',
            'bank_name' => 'required|string|max:255',
            'account_number' => 'required|string|max:50',
            'account_type' => 'required|string|in:savings,current,checking,business',
            'routing_number' => 'nullable|string|max:50',
            'swift_code' => 'nullable|string|max:20',
            'additional_instructions' => 'nullable|string|max:1000',
            'is_active' => 'boolean',
            'is_primary' => 'boolean',
        ]);

        DB::beginTransaction();
        try {
            $account = PaymentAccount::create($validated);

            // If this is set as primary, update others
            if ($validated['is_primary'] ?? false) {
                $account->setPrimary();
            }

            DB::commit();
            return redirect()->route('super.payment-accounts.index')
                           ->with('success', 'Payment account created successfully.');
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'Failed to create payment account: ' . $e->getMessage()])
                        ->withInput();
        }
    }

    /**
     * Display the specified payment account.
     */
    public function show(PaymentAccount $paymentAccount)
    {
        return view('super_admin.payment_accounts.show', compact('paymentAccount'));
    }

    /**
     * Show the form for editing the specified payment account.
     */
    public function edit(PaymentAccount $paymentAccount)
    {
        return view('super_admin.payment_accounts.edit', compact('paymentAccount'));
    }

    /**
     * Update the specified payment account.
     */
    public function update(Request $request, PaymentAccount $paymentAccount)
    {
        $validated = $request->validate([
            'account_name' => 'required|string|max:255',
            'bank_name' => 'required|string|max:255',
            'account_number' => 'required|string|max:50',
            'account_type' => 'required|string|in:savings,current,checking,business',
            'routing_number' => 'nullable|string|max:50',
            'swift_code' => 'nullable|string|max:20',
            'additional_instructions' => 'nullable|string|max:1000',
            'is_active' => 'boolean',
            'is_primary' => 'boolean',
        ]);

        DB::beginTransaction();
        try {
            $paymentAccount->update($validated);

            // If this is set as primary, update others
            if ($validated['is_primary'] ?? false) {
                $paymentAccount->setPrimary();
            }

            DB::commit();
            return redirect()->route('super.payment-accounts.index')
                           ->with('success', 'Payment account updated successfully.');
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'Failed to update payment account: ' . $e->getMessage()])
                        ->withInput();
        }
    }

    /**
     * Remove the specified payment account.
     */
    public function destroy(PaymentAccount $paymentAccount)
    {
        try {
            // Don't allow deletion of primary account if it's the only one
            if ($paymentAccount->is_primary && PaymentAccount::active()->count() === 1) {
                return back()->withErrors(['error' => 'Cannot delete the only active payment account.']);
            }

            $paymentAccount->delete();
            return redirect()->route('super.payment-accounts.index')
                           ->with('success', 'Payment account deleted successfully.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to delete payment account: ' . $e->getMessage()]);
        }
    }

    /**
     * Toggle account status.
     */
    public function toggleStatus(PaymentAccount $paymentAccount)
    {
        try {
            // Don't allow deactivating primary account if it's the only active one
            if ($paymentAccount->is_active && $paymentAccount->is_primary && 
                PaymentAccount::active()->count() === 1) {
                return back()->withErrors(['error' => 'Cannot deactivate the only active payment account.']);
            }

            $paymentAccount->update(['is_active' => !$paymentAccount->is_active]);
            
            $status = $paymentAccount->is_active ? 'activated' : 'deactivated';
            return back()->with('success', "Payment account {$status} successfully.");
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to update account status: ' . $e->getMessage()]);
        }
    }

    /**
     * Set account as primary.
     */
    public function setPrimary(PaymentAccount $paymentAccount)
    {
        try {
            if (!$paymentAccount->is_active) {
                return back()->withErrors(['error' => 'Cannot set inactive account as primary.']);
            }

            $paymentAccount->setPrimary();
            return back()->with('success', 'Primary account updated successfully.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to set primary account: ' . $e->getMessage()]);
        }
    }
}
