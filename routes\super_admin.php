<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\SuperAdmin\AuthController;
use App\Http\Controllers\SuperAdmin\DashboardController;
use App\Http\Controllers\SuperAdmin\OrganizationController;
use App\Http\Controllers\SuperAdmin\PlanController;
use App\Http\Controllers\SuperAdmin\SubscriptionController;
use App\Http\Controllers\SuperAdmin\PaymentAccountController;
use App\Http\Controllers\SuperAdmin\SubscriptionPaymentController;
use App\Http\Controllers\SuperAdmin\ReportController;
use App\Http\Controllers\SuperAdmin\AffiliateController;
use App\Http\Controllers\SuperAdmin\AffiliateWithdrawalController;
use App\Http\Controllers\SuperAdmin\AffiliateEarningController;

/*
|--------------------------------------------------------------------------
| Super Admin Routes
|--------------------------------------------------------------------------
|
| These routes are used for the super admin functionality.
| All routes use the 'super_admin' prefix and the 'web' middleware group.
|
*/

// Guest routes
Route::group(['prefix' => 'super', 'as' => 'super.'], function () {
    Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);
    Route::post('/logout', [AuthController::class, 'logout'])->name('logout');
});

// Protected routes
Route::group(['prefix' => 'super', 'as' => 'super.', 'middleware' => ['web', 'super_admin']], function () {
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Organizations
    Route::resource('organizations', OrganizationController::class);
    Route::post('/organizations/{organization}/activate', [OrganizationController::class, 'activate'])->name('organizations.activate');
    Route::post('/organizations/{organization}/deactivate', [OrganizationController::class, 'deactivate'])->name('organizations.deactivate');

    // Plans
    Route::resource('plans', PlanController::class);
    Route::post('/plans/{plan}/activate', [PlanController::class, 'activate'])->name('plans.activate');
    Route::post('/plans/{plan}/deactivate', [PlanController::class, 'deactivate'])->name('plans.deactivate');

    // Subscriptions
    Route::resource('subscriptions', SubscriptionController::class);
    Route::post('/subscriptions/{subscription}/cancel', [SubscriptionController::class, 'cancel'])->name('subscriptions.cancel');
    Route::post('/subscriptions/{subscription}/reactivate', [SubscriptionController::class, 'reactivate'])->name('subscriptions.reactivate');
    Route::post('/subscriptions/{subscription}/extend', [SubscriptionController::class, 'extend'])->name('subscriptions.extend');

    // Payment Accounts
    Route::resource('payment-accounts', PaymentAccountController::class);
    Route::post('/payment-accounts/{paymentAccount}/toggle-status', [PaymentAccountController::class, 'toggleStatus'])->name('payment-accounts.toggle-status');
    Route::post('/payment-accounts/{paymentAccount}/set-primary', [PaymentAccountController::class, 'setPrimary'])->name('payment-accounts.set-primary');

    // Subscription Payments
    Route::resource('subscription-payments', SubscriptionPaymentController::class);
    Route::post('/subscription-payments/{subscriptionPayment}/approve', [SubscriptionPaymentController::class, 'approve'])->name('subscription-payments.approve');
    Route::post('/subscription-payments/{subscriptionPayment}/reject', [SubscriptionPaymentController::class, 'reject'])->name('subscription-payments.reject');

    // Affiliates
    Route::resource('affiliates', AffiliateController::class);
    Route::post('/affiliates/{affiliate}/approve', [AffiliateController::class, 'approve'])->name('affiliates.approve');
    Route::post('/affiliates/{affiliate}/reject', [AffiliateController::class, 'reject'])->name('affiliates.reject');
    Route::post('/affiliates/{affiliate}/suspend', [AffiliateController::class, 'suspend'])->name('affiliates.suspend');
    Route::post('/affiliates/{affiliate}/reactivate', [AffiliateController::class, 'reactivate'])->name('affiliates.reactivate');
    Route::post('/affiliates/{affiliate}/add-bonus', [AffiliateController::class, 'addBonus'])->name('affiliates.add-bonus');
    Route::post('/affiliates/{affiliate}/add-adjustment', [AffiliateController::class, 'addAdjustment'])->name('affiliates.add-adjustment');
    Route::post('/affiliates/bulk-action', [AffiliateController::class, 'bulkAction'])->name('affiliates.bulk-action');

    // Affiliate Earnings
    Route::resource('affiliate-earnings', AffiliateEarningController::class);
    Route::post('/affiliate-earnings/{affiliateEarning}/approve', [AffiliateEarningController::class, 'approve'])->name('affiliate-earnings.approve');
    Route::post('/affiliate-earnings/{affiliateEarning}/reject', [AffiliateEarningController::class, 'reject'])->name('affiliate-earnings.reject');
    Route::post('/affiliate-earnings/bulk-approve', [AffiliateEarningController::class, 'bulkApprove'])->name('affiliate-earnings.bulk-approve');
    Route::post('/affiliate-earnings/bulk-reject', [AffiliateEarningController::class, 'bulkReject'])->name('affiliate-earnings.bulk-reject');

    // Affiliate Withdrawals
    Route::resource('affiliate-withdrawals', AffiliateWithdrawalController::class)->parameters([
        'affiliate-withdrawals' => 'withdrawal'
    ]);
    Route::post('/affiliate-withdrawals/{withdrawal}/approve', [AffiliateWithdrawalController::class, 'approve'])->name('affiliate-withdrawals.approve');
    Route::post('/affiliate-withdrawals/{withdrawal}/reject', [AffiliateWithdrawalController::class, 'reject'])->name('affiliate-withdrawals.reject');
    Route::post('/affiliate-withdrawals/{withdrawal}/mark-as-paid', [AffiliateWithdrawalController::class, 'markAsPaid'])->name('affiliate-withdrawals.mark-as-paid');
    Route::post('/affiliate-withdrawals/bulk-approve', [AffiliateWithdrawalController::class, 'bulkApprove'])->name('affiliate-withdrawals.bulk-approve');
    Route::post('/affiliate-withdrawals/bulk-reject', [AffiliateWithdrawalController::class, 'bulkReject'])->name('affiliate-withdrawals.bulk-reject');
    Route::get('/affiliate-withdrawals/export', [AffiliateWithdrawalController::class, 'export'])->name('affiliate-withdrawals.export');

    // Debug route
    Route::get('/debug-withdrawals', function() {
        $withdrawals = \App\Models\AffiliateWithdrawal::with(['affiliate.user'])->get();
        $affiliates = \App\Models\Affiliate::with('user')->get();

        // Create a test withdrawal if none exist and we have affiliates
        if ($withdrawals->count() === 0 && $affiliates->count() > 0) {
            $testAffiliate = $affiliates->first();
            $testWithdrawal = \App\Models\AffiliateWithdrawal::create([
                'affiliate_id' => $testAffiliate->id,
                'amount' => 100.00,
                'payment_method' => 'bank_transfer',
                'payment_details' => [
                    'method' => 'bank_transfer',
                    'bank_name' => 'Test Bank',
                    'account_number' => '*********0',
                    'account_name' => 'Test Account',
                    'routing_number' => '*********',
                ],
                'status' => 'pending',
                'fee_amount' => 5.00,
                'net_amount' => 95.00,
                'requested_at' => now(),
            ]);

            $withdrawals = \App\Models\AffiliateWithdrawal::with(['affiliate.user'])->get();
        }

        return response()->json([
            'withdrawals_count' => $withdrawals->count(),
            'withdrawals' => $withdrawals->map(function($w) {
                return [
                    'id' => $w->id,
                    'amount' => $w->amount,
                    'status' => $w->status,
                    'payment_method' => $w->payment_method,
                    'payment_details' => $w->payment_details,
                    'affiliate_id' => $w->affiliate_id,
                    'affiliate_name' => $w->affiliate->user->name ?? 'No affiliate',
                    'created_at' => $w->created_at
                ];
            }),
            'affiliates_count' => $affiliates->count(),
            'affiliates' => $affiliates->map(function($a) {
                return [
                    'id' => $a->id,
                    'user_name' => $a->user->name ?? 'No user',
                    'affiliate_code' => $a->affiliate_code,
                    'status' => $a->status
                ];
            })
        ]);
    });

    // Reports
    Route::prefix('reports')->name('reports.')->group(function () {
        Route::get('/revenue', [ReportController::class, 'revenue'])->name('revenue');
        Route::get('/organizations', [ReportController::class, 'organizations'])->name('organizations');
        Route::get('/subscriptions', [ReportController::class, 'subscriptions'])->name('subscriptions');
        Route::get('/export/{type}', [ReportController::class, 'export'])->name('export');
    });
});
