<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class Role
{
    public function handle(Request $request, Closure $next, ...$roles)
    {
        if (!$request->user()) {
            return redirect()->route('login');
        }

        try {
            // Convert pipe-separated roles to array
            $rolesArray = count($roles) === 1 ? explode('|', $roles[0]) : $roles;

            if (!$request->user()->hasAnyRole($rolesArray)) {
                Log::warning('Unauthorized access attempt', [
                    'user' => $request->user()->email,
                    'required_roles' => $rolesArray,
                    'user_roles' => $request->user()->roles()->pluck('name')->toArray(),
                    'url' => $request->fullUrl()
                ]);

                if ($request->wantsJson()) {
                    return response()->json(['message' => 'Unauthorized'], 403);
                }
                
                return redirect()->back()->with('error', 'You do not have permission to access this page.');
            }

            return $next($request);

        } catch (\Exception $e) {
            Log::error('Role middleware error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'url' => $request->fullUrl()
            ]);

            abort(500, 'An error occurred while checking permissions');
        }
    }
}