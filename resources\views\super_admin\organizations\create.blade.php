@extends('super_admin.layouts.app')

@section('title', 'Create Organization')
@section('page-title', 'Create Organization')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Create Organization</h1>
            <p class="text-muted">Add a new organization to the system</p>
        </div>
        <a href="{{ route('super.organizations.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Organizations
        </a>
    </div>

    <!-- Create Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Organization Details</h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('super.organizations.store') }}" enctype="multipart/form-data">
                        @csrf

                        <!-- Basic Information -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="name" class="form-label">Organization Name *</label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                       id="name" name="name" value="{{ old('name') }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="email" class="form-label">Email Address *</label>
                                <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                       id="email" name="email" value="{{ old('email') }}" required>
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control @error('phone') is-invalid @enderror" 
                                       id="phone" name="phone" value="{{ old('phone') }}">
                                @error('phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="website" class="form-label">Website</label>
                                <input type="url" class="form-control @error('website') is-invalid @enderror" 
                                       id="website" name="website" value="{{ old('website') }}" 
                                       placeholder="https://example.com">
                                @error('website')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="address" class="form-label">Address</label>
                            <textarea class="form-control @error('address') is-invalid @enderror" 
                                      id="address" name="address" rows="3">{{ old('address') }}</textarea>
                            @error('address')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3" 
                                      placeholder="Brief description of the organization">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Plan Selection -->
                        <div class="mb-4">
                            <label for="plan_id" class="form-label">Subscription Plan *</label>
                            <select class="form-select @error('plan_id') is-invalid @enderror" 
                                    id="plan_id" name="plan_id" required>
                                <option value="">Select a Plan</option>
                                @foreach($plans as $plan)
                                    <option value="{{ $plan->id }}" 
                                            data-price="{{ $plan->price }}"
                                            data-features="{{ json_encode([
                                                'branches' => $plan->branch_limit,
                                                'users' => $plan->user_limit,
                                                'retention' => $plan->data_retention_days,
                                                'thermal_printing' => $plan->thermal_printing,
                                                'advanced_reporting' => $plan->advanced_reporting,
                                                'api_access' => $plan->api_access
                                            ]) }}"
                                            {{ old('plan_id') == $plan->id ? 'selected' : '' }}>
                                        {{ $plan->name }} - ${{ number_format($plan->price, 2) }}/month
                                    </option>
                                @endforeach
                            </select>
                            @error('plan_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Trial Period -->
                        <div class="mb-4">
                            <label for="trial_ends_at" class="form-label">Trial End Date</label>
                            <input type="date" class="form-control @error('trial_ends_at') is-invalid @enderror" 
                                   id="trial_ends_at" name="trial_ends_at" value="{{ old('trial_ends_at') }}">
                            <small class="text-muted">Leave empty if no trial period</small>
                            @error('trial_ends_at')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Logo Upload -->
                        <div class="mb-4">
                            <label for="logo" class="form-label">Organization Logo</label>
                            <input type="file" class="form-control @error('logo') is-invalid @enderror" 
                                   id="logo" name="logo" accept="image/*">
                            <small class="text-muted">Supported formats: JPEG, PNG, JPG, GIF. Max size: 2MB</small>
                            @error('logo')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('super.organizations.index') }}" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Create Organization
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Preview Card -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Organization Preview</h6>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="mb-3">
                            <div id="logo-preview" class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center" 
                                 style="width: 60px; height: 60px;">
                                <span class="text-white font-weight-bold fs-4" id="logo-initials">?</span>
                            </div>
                        </div>
                        <h5 id="preview-name">Organization Name</h5>
                        <p class="text-muted" id="preview-email"><EMAIL></p>
                        <div class="text-start">
                            <small class="text-muted">
                                <strong>Plan:</strong> <span id="preview-plan">Select a plan</span><br>
                                <strong>Price:</strong> <span id="preview-price">$0.00/month</span><br>
                                <strong>Phone:</strong> <span id="preview-phone">Not provided</span><br>
                                <strong>Website:</strong> <span id="preview-website">Not provided</span>
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Plan Features -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Plan Features</h6>
                </div>
                <div class="card-body" id="plan-features">
                    <p class="text-muted">Select a plan to see features</p>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="setTrialPeriod()">
                            <i class="fas fa-clock me-2"></i>Set 30-day Trial
                        </button>
                        <button type="button" class="btn btn-outline-success btn-sm" onclick="clearTrial()">
                            <i class="fas fa-times me-2"></i>No Trial Period
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const nameInput = document.getElementById('name');
    const emailInput = document.getElementById('email');
    const phoneInput = document.getElementById('phone');
    const websiteInput = document.getElementById('website');
    const planSelect = document.getElementById('plan_id');
    const logoInput = document.getElementById('logo');

    function updatePreview() {
        // Update name and initials
        const name = nameInput.value || 'Organization Name';
        document.getElementById('preview-name').textContent = name;
        
        const initials = name.split(' ').map(word => word.charAt(0)).join('').substring(0, 2).toUpperCase();
        document.getElementById('logo-initials').textContent = initials || '?';

        // Update email
        document.getElementById('preview-email').textContent = emailInput.value || '<EMAIL>';

        // Update phone
        document.getElementById('preview-phone').textContent = phoneInput.value || 'Not provided';

        // Update website
        document.getElementById('preview-website').textContent = websiteInput.value || 'Not provided';

        // Update plan info
        const selectedOption = planSelect.options[planSelect.selectedIndex];
        if (selectedOption.value) {
            const planName = selectedOption.text.split(' - ')[0];
            const planPrice = selectedOption.dataset.price;
            const features = JSON.parse(selectedOption.dataset.features || '{}');

            document.getElementById('preview-plan').textContent = planName;
            document.getElementById('preview-price').textContent = '$' + parseFloat(planPrice).toFixed(2) + '/month';

            // Update plan features
            let featuresHtml = '<div class="row">';
            featuresHtml += `<div class="col-12 mb-2"><small><strong>Limits:</strong></small></div>`;
            featuresHtml += `<div class="col-12"><small>• ${features.branches == 999 ? 'Unlimited' : features.branches} Branches</small></div>`;
            featuresHtml += `<div class="col-12"><small>• ${features.users == 999 ? 'Unlimited' : features.users} Users</small></div>`;
            featuresHtml += `<div class="col-12 mb-2"><small>• ${features.retention == 999 ? 'Forever' : features.retention + ' Days'} Data Retention</small></div>`;
            featuresHtml += `<div class="col-12 mb-2"><small><strong>Features:</strong></small></div>`;
            if (features.thermal_printing) featuresHtml += `<div class="col-12"><small>• Thermal Printing</small></div>`;
            if (features.advanced_reporting) featuresHtml += `<div class="col-12"><small>• Advanced Reporting</small></div>`;
            if (features.api_access) featuresHtml += `<div class="col-12"><small>• API Access</small></div>`;
            featuresHtml += '</div>';

            document.getElementById('plan-features').innerHTML = featuresHtml;
        } else {
            document.getElementById('preview-plan').textContent = 'Select a plan';
            document.getElementById('preview-price').textContent = '$0.00/month';
            document.getElementById('plan-features').innerHTML = '<p class="text-muted">Select a plan to see features</p>';
        }
    }

    // Handle logo preview
    logoInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const logoPreview = document.getElementById('logo-preview');
                logoPreview.innerHTML = `<img src="${e.target.result}" alt="Logo" class="rounded-circle" style="width: 60px; height: 60px; object-fit: cover;">`;
            };
            reader.readAsDataURL(file);
        }
    });

    // Add event listeners
    [nameInput, emailInput, phoneInput, websiteInput, planSelect].forEach(element => {
        if (element) {
            element.addEventListener('input', updatePreview);
            element.addEventListener('change', updatePreview);
        }
    });

    // Initial update
    updatePreview();
});

// Quick action functions
function setTrialPeriod() {
    const today = new Date();
    const trialEnd = new Date(today);
    trialEnd.setDate(today.getDate() + 30);
    document.getElementById('trial_ends_at').value = trialEnd.toISOString().split('T')[0];
}

function clearTrial() {
    document.getElementById('trial_ends_at').value = '';
}
</script>
@endsection
