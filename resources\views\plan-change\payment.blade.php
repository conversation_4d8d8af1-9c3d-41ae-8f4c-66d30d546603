@extends('layouts.app')

@section('title', 'Plan Change Payment')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Plan Change Payment</h1>
            <p class="text-muted">Complete payment to activate your new plan</p>
        </div>
        <a href="{{ route('plan-change.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Plans
        </a>
    </div>

    <div class="row">
        <!-- Payment Form -->
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Payment Details</h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('plan-change.process-payment', $plan) }}">
                        @csrf

                        <!-- Payment Method -->
                        <div class="mb-3">
                            <label for="payment_method" class="form-label">Payment Method *</label>
                            <select class="form-select @error('payment_method') is-invalid @enderror" 
                                    id="payment_method" name="payment_method" required>
                                <option value="">Select Payment Method</option>
                                <option value="bank_transfer" {{ old('payment_method') == 'bank_transfer' ? 'selected' : '' }}>
                                    Bank Transfer
                                </option>
                                <option value="credit_card" {{ old('payment_method') == 'credit_card' ? 'selected' : '' }}>
                                    Credit Card
                                </option>
                                <option value="paypal" {{ old('payment_method') == 'paypal' ? 'selected' : '' }}>
                                    PayPal
                                </option>
                                <option value="cash" {{ old('payment_method') == 'cash' ? 'selected' : '' }}>
                                    Cash
                                </option>
                            </select>
                            @error('payment_method')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Payment Reference -->
                        <div class="mb-3">
                            <label for="payment_reference" class="form-label">Payment Reference *</label>
                            <input type="text" class="form-control @error('payment_reference') is-invalid @enderror" 
                                   id="payment_reference" name="payment_reference" 
                                   value="{{ old('payment_reference') }}" required
                                   placeholder="Transaction ID, Check Number, etc.">
                            <small class="text-muted">Enter transaction ID, check number, or other payment reference</small>
                            @error('payment_reference')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Payment Amount -->
                        <div class="mb-3">
                            <label for="payment_amount" class="form-label">Payment Amount *</label>
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                <input type="number" step="0.01" min="0" 
                                       class="form-control @error('payment_amount') is-invalid @enderror" 
                                       id="payment_amount" name="payment_amount" 
                                       value="{{ old('payment_amount', number_format($amountDue, 2, '.', '')) }}" required>
                            </div>
                            @error('payment_amount')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Notes -->
                        <div class="mb-4">
                            <label for="notes" class="form-label">Additional Notes (Optional)</label>
                            <textarea class="form-control @error('notes') is-invalid @enderror" 
                                      id="notes" name="notes" rows="3"
                                      placeholder="Any additional information about this payment...">{{ old('notes') }}</textarea>
                            @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Admin Approval Notice -->
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Payment Approval Required:</strong> Your payment will be reviewed by our admin team. 
                            Your plan change will be activated once the payment is approved.
                        </div>

                        <!-- Submit Button -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('plan-change.index') }}" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-credit-card me-2"></i>Submit Payment
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Order Summary -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Order Summary</h6>
                </div>
                <div class="card-body">
                    <!-- Plan Change Details -->
                    <div class="mb-3">
                        <h6>Plan Change</h6>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="text-muted">From:</span>
                            <span>{{ $currentPlan ? $currentPlan->name : 'No Plan' }}</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="text-muted">To:</span>
                            <strong>{{ $plan->name }}</strong>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="text-muted">Billing Period:</span>
                            <span>{{ $selectedPeriod }} Month{{ $selectedPeriod > 1 ? 's' : '' }}</span>
                        </div>
                        @if($selectedDiscount > 0)
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="text-muted">Discount:</span>
                            <span class="text-success">{{ $selectedDiscount }}% off</span>
                        </div>
                        @endif
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="text-muted">Type:</span>
                            <span>{{ ucfirst(str_replace('_', ' ', $planChangeData['change_type'])) }}</span>
                        </div>
                    </div>

                    <hr>

                    <!-- Pricing Breakdown -->
                    <div class="mb-3">
                        <h6>Pricing Details</h6>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>{{ $plan->name }} Plan ({{ $selectedPeriod }} Month{{ $selectedPeriod > 1 ? 's' : '' }})</span>
                            <span>${{ number_format($baseAmount, 2) }}</span>
                        </div>
                        @if($selectedDiscount > 0)
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="text-success">Discount</span>
                            <span class="text-success">- ${{ number_format($discountAmount, 2) }}</span>
                        </div>
                        @endif
                        @if($proration['type'] !== 'new_subscription')
                            @if(isset($proration['current_plan_credit']) && $proration['current_plan_credit'] > 0)
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="text-success">Current Plan Credit</span>
                                    <span class="text-success">-${{ number_format($proration['current_plan_credit'], 2) }}</span>
                                </div>
                            @endif
                            @if(isset($proration['new_plan_charge']) && $proration['new_plan_charge'] > 0)
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span>{{ $plan->name }} Plan Charge</span>
                                    <span>${{ number_format($proration['new_plan_charge'], 2) }}</span>
                                </div>
                            @endif
                        @endif
                    </div>

                    <hr>

                    <!-- Total Amount -->
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <strong>Total Amount Due:</strong>
                        <strong class="text-primary h5">${{ number_format($amountDue, 2) }}</strong>
                    </div>

                    <!-- Plan Features -->
                    <div class="mb-3">
                        <h6>{{ $plan->name }} Features</h6>
                        <small class="text-muted">
                            • {{ $plan->branch_limit == 999 ? 'Unlimited' : $plan->branch_limit }} Branches<br>
                            • {{ $plan->user_limit == 999 ? 'Unlimited' : $plan->user_limit }} Users<br>
                            • {{ $plan->order_limit === null ? 'Unlimited' : $plan->order_limit }} Orders/Month<br>
                            • {{ $plan->data_retention_days == 999 ? 'Forever' : $plan->data_retention_days . ' Days' }} Data Retention<br>
                            @if($plan->thermal_printing) • Thermal Printing<br> @endif
                            @if($plan->advanced_reporting) • Advanced Reporting<br> @endif
                            @if($plan->api_access) • API Access<br> @endif
                            @if($plan->white_label) • White Label<br> @endif
                            @if($plan->custom_branding) • Custom Branding<br> @endif
                        </small>
                    </div>

                    @if(isset($proration['proration_details']))
                        <div class="alert alert-light">
                            <small class="text-muted">
                                <strong>Note:</strong> {{ $proration['proration_details'] }}
                            </small>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Support -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Need Help?</h6>
                </div>
                <div class="card-body">
                    <p class="text-muted">Questions about payment or plan changes?</p>
                    <div class="d-grid">
                        <button type="button" class="btn btn-outline-primary" onclick="alert('Support contact coming soon!')">
                            <i class="fas fa-headset me-2"></i>Contact Support
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
