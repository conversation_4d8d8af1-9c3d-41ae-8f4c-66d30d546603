@extends('affiliate.layouts.app')

@section('page-title', 'Withdrawals')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-1">Withdrawal History</h2>
            <p class="text-muted mb-0">Manage your withdrawal requests and view payment history</p>
        </div>

        @if($summary['available_balance'] >= 50)
        <a href="{{ route('affiliate.withdrawals.create') }}" class="btn btn-success">
            <i class="fas fa-plus me-2"></i>
            Request Withdrawal
        </a>
        @endif
    </div>
        </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Available Balance
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                ${{ number_format($summary['available_balance'], 2) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-wallet fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Total Requested
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                ${{ number_format($summary['total_requested'], 2) }}
                            </div>
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                Excludes rejected/cancelled
                            </small>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Pending
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                ${{ number_format($summary['pending_amount'], 2) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Paid
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                ${{ number_format($summary['total_paid'], 2) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('affiliate.withdrawals') }}">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select id="status" name="status" class="form-select">
                            <option value="">All Statuses</option>
                            <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                            <option value="approved" {{ request('status') == 'approved' ? 'selected' : '' }}>Approved</option>
                            <option value="paid" {{ request('status') == 'paid' ? 'selected' : '' }}>Paid</option>
                            <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>Rejected</option>
                            <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                        </select>
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="date_from" class="form-label">From Date</label>
                        <input type="date" id="date_from" name="date_from" value="{{ request('date_from') }}" class="form-control">
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="date_to" class="form-label">To Date</label>
                        <input type="date" id="date_to" name="date_to" value="{{ request('date_to') }}" class="form-control">
                    </div>

                    <div class="col-md-3 mb-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-filter me-2"></i>Filter
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Withdrawals Table -->
    <div class="card shadow">
        <div class="card-body">
            @if($withdrawals->count() > 0)
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Request Date</th>
                                <th>Amount</th>
                                <th>Payment Method</th>
                                <th>Status</th>
                                <th>Processed Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($withdrawals as $withdrawal)
                            <tr>
                                <td>
                                    {{ $withdrawal->requested_at->format('M j, Y') }}
                                    <br><small class="text-muted">{{ $withdrawal->requested_at->format('g:i A') }}</small>
                                </td>
                                <td>
                                    <strong>${{ number_format($withdrawal->amount, 2) }}</strong>
                                    @if($withdrawal->fee_amount > 0)
                                        <br><small class="text-muted">Fee: ${{ number_format($withdrawal->fee_amount, 2) }}</small>
                                        <br><small class="text-muted">Net: ${{ number_format($withdrawal->net_amount, 2) }}</small>
                                    @endif
                                </td>
                                <td>{{ $withdrawal->payment_method_display }}</td>
                                <td>
                                    @php
                                        $badgeClass = match($withdrawal->status) {
                                            'pending' => 'bg-warning',
                                            'approved' => 'bg-info',
                                            'paid' => 'bg-success',
                                            'rejected' => 'bg-danger',
                                            'cancelled' => 'bg-secondary',
                                            default => 'bg-secondary'
                                        };
                                    @endphp
                                    <span class="badge {{ $badgeClass }}">{{ ucfirst($withdrawal->status) }}</span>
                                    @if($withdrawal->status === 'rejected' && $withdrawal->rejection_reason)
                                        <br>
                                        <small class="text-muted mt-1" title="{{ $withdrawal->rejection_reason }}">
                                            <i class="fas fa-info-circle me-1"></i>
                                            {{ Str::limit($withdrawal->rejection_reason, 50) }}
                                        </small>
                                    @endif
                                </td>
                                <td>
                                    @if($withdrawal->processed_at)
                                        {{ $withdrawal->processed_at->format('M j, Y') }}
                                        <br><small class="text-muted">{{ $withdrawal->processed_at->format('g:i A') }}</small>
                                    @else
                                        <span class="text-muted">-</span>
                                    @endif
                                </td>
                                <td>
                                    <a href="{{ route('affiliate.withdrawals.show', $withdrawal) }}" class="btn btn-sm btn-outline-primary me-1">View</a>
                                    @if($withdrawal->isPending())
                                        <form method="POST" action="{{ route('affiliate.withdrawals.cancel', $withdrawal) }}" class="d-inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to cancel this withdrawal request?')">Cancel</button>
                                        </form>
                                    @endif
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-3">
                    {{ $withdrawals->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-money-bill-wave text-muted" style="font-size: 3rem;"></i>
                    <h4 class="mt-3">No withdrawal requests yet</h4>
                    <p class="text-muted mb-4">You haven't made any withdrawal requests.</p>
                    @if($summary['available_balance'] >= 50)
                        <a href="{{ route('affiliate.withdrawals.create') }}" class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>Request Your First Withdrawal
                        </a>
                    @else
                        <p class="text-muted">Minimum withdrawal amount is $50.00</p>
                    @endif
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
