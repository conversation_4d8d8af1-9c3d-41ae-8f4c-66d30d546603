@extends('super_admin.layouts.app')

@section('title', 'Edit Affiliate')
@section('page-title', 'Edit Affiliate')

@section('content')
<!-- Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h2 mb-1">Edit Affiliate</h1>
        <p class="text-muted">{{ $affiliate->user->name }} ({{ $affiliate->affiliate_code }})</p>
    </div>
    <div>
        <a href="{{ route('super.affiliates.show', $affiliate) }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>
            Back to Details
        </a>
    </div>
</div>

<!-- Edit Form -->
<div class="row">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Affiliate Information</h6>
            </div>
            <div class="card-body">
                <form action="{{ route('super.affiliates.update', $affiliate) }}" method="POST">
                    @csrf
                    @method('PUT')

                    <!-- Personal Information -->
                    <div class="mb-4">
                        <h6 class="text-gray-800 mb-3">Personal Information</h6>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">Full Name</label>
                                <input type="text" 
                                       class="form-control @error('name') is-invalid @enderror" 
                                       id="name" 
                                       name="name" 
                                       value="{{ old('name', $affiliate->user->name) }}" 
                                       required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email Address</label>
                                <input type="email" 
                                       class="form-control @error('email') is-invalid @enderror" 
                                       id="email" 
                                       name="email" 
                                       value="{{ old('email', $affiliate->user->email) }}" 
                                       required>
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="text" 
                                       class="form-control @error('phone') is-invalid @enderror" 
                                       id="phone" 
                                       name="phone" 
                                       value="{{ old('phone', $affiliate->user->phone) }}">
                                @error('phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select @error('status') is-invalid @enderror" 
                                        id="status" 
                                        name="status" 
                                        required>
                                    <option value="pending" {{ old('status', $affiliate->status) === 'pending' ? 'selected' : '' }}>
                                        Pending Approval
                                    </option>
                                    <option value="active" {{ old('status', $affiliate->status) === 'active' ? 'selected' : '' }}>
                                        Active
                                    </option>
                                    <option value="suspended" {{ old('status', $affiliate->status) === 'suspended' ? 'selected' : '' }}>
                                        Suspended
                                    </option>
                                    <option value="inactive" {{ old('status', $affiliate->status) === 'inactive' ? 'selected' : '' }}>
                                        Inactive
                                    </option>
                                </select>
                                @error('status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Affiliate Settings -->
                    <div class="mb-4">
                        <h6 class="text-gray-800 mb-3">Affiliate Settings</h6>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="commission_rate" class="form-label">Commission Rate (%)</label>
                                <input type="number" 
                                       class="form-control @error('commission_rate') is-invalid @enderror" 
                                       id="commission_rate" 
                                       name="commission_rate" 
                                       value="{{ old('commission_rate', $affiliate->commission_rate) }}" 
                                       step="0.01" 
                                       min="0" 
                                       max="100" 
                                       required>
                                @error('commission_rate')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="affiliate_code" class="form-label">Affiliate Code</label>
                                <input type="text" 
                                       class="form-control" 
                                       id="affiliate_code" 
                                       value="{{ $affiliate->affiliate_code }}" 
                                       readonly>
                                <small class="form-text text-muted">Affiliate code cannot be changed</small>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="bio" class="form-label">Bio</label>
                            <textarea class="form-control @error('bio') is-invalid @enderror" 
                                      id="bio" 
                                      name="bio" 
                                      rows="3">{{ old('bio', $affiliate->bio) }}</textarea>
                            @error('bio')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="website" class="form-label">Website</label>
                                <input type="url" 
                                       class="form-control @error('website') is-invalid @enderror" 
                                       id="website" 
                                       name="website" 
                                       value="{{ old('website', $affiliate->website) }}">
                                @error('website')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="social_media" class="form-label">Social Media</label>
                                <input type="text" 
                                       class="form-control @error('social_media') is-invalid @enderror" 
                                       id="social_media" 
                                       name="social_media" 
                                       value="{{ old('social_media', $affiliate->social_media) }}" 
                                       placeholder="@username or profile URL">
                                @error('social_media')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Payment Information -->
                    @if($affiliate->payment_details)
                        <div class="mb-4">
                            <h6 class="text-gray-800 mb-3">Payment Information</h6>
                            
                            @if($affiliate->payment_details['method'] === 'paypal')
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-paypal me-2"></i>PayPal Payment</h6>
                                    <p class="mb-0"><strong>Email:</strong> {{ $affiliate->payment_details['paypal_email'] }}</p>
                                </div>
                            @elseif($affiliate->payment_details['method'] === 'bank_transfer')
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-university me-2"></i>Bank Transfer</h6>
                                    <p class="mb-1"><strong>Bank:</strong> {{ $affiliate->payment_details['bank_name'] }}</p>
                                    <p class="mb-1"><strong>Account Number:</strong> {{ $affiliate->payment_details['account_number'] }}</p>
                                    <p class="mb-0"><strong>Account Name:</strong> {{ $affiliate->payment_details['account_name'] }}</p>
                                    @if(isset($affiliate->payment_details['routing_number']))
                                        <p class="mb-0"><strong>Routing Number:</strong> {{ $affiliate->payment_details['routing_number'] }}</p>
                                    @endif
                                </div>
                            @endif
                            
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                Payment details can only be updated by the affiliate from their dashboard.
                            </small>
                        </div>
                    @endif

                    <!-- Form Actions -->
                    <div class="d-flex justify-content-between">
                        <a href="{{ route('super.affiliates.show', $affiliate) }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            Update Affiliate
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="col-lg-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
            </div>
            <div class="card-body">
                @if($affiliate->status === 'pending')
                    <form action="{{ route('super.affiliates.approve', $affiliate) }}" method="POST" class="mb-2">
                        @csrf
                        <button type="submit" class="btn btn-success btn-sm w-100">
                            <i class="fas fa-check me-2"></i>
                            Approve Affiliate
                        </button>
                    </form>
                    <form action="{{ route('super.affiliates.reject', $affiliate) }}" method="POST" class="mb-2">
                        @csrf
                        <button type="submit" class="btn btn-danger btn-sm w-100" 
                                onclick="return confirm('Are you sure you want to reject this affiliate?')">
                            <i class="fas fa-times me-2"></i>
                            Reject Affiliate
                        </button>
                    </form>
                @elseif($affiliate->status === 'active')
                    <form action="{{ route('super.affiliates.suspend', $affiliate) }}" method="POST" class="mb-2">
                        @csrf
                        <button type="submit" class="btn btn-warning btn-sm w-100"
                                onclick="return confirm('Are you sure you want to suspend this affiliate?')">
                            <i class="fas fa-ban me-2"></i>
                            Suspend Affiliate
                        </button>
                    </form>
                @elseif($affiliate->status === 'suspended' || $affiliate->status === 'inactive')
                    <form action="{{ route('super.affiliates.reactivate', $affiliate) }}" method="POST" class="mb-2">
                        @csrf
                        <button type="submit" class="btn btn-success btn-sm w-100">
                            <i class="fas fa-play me-2"></i>
                            Reactivate Affiliate
                        </button>
                    </form>
                @endif

                <hr>

                <div class="text-center">
                    <small class="text-muted">
                        <i class="fas fa-calendar me-1"></i>
                        Joined: {{ $affiliate->created_at->format('M d, Y') }}
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
