@extends('super_admin.layouts.app')

@section('title', 'Affiliate Withdrawals')
@section('page-title', 'Affiliate Withdrawals')

@section('content')
<!-- Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h2 mb-1">Affiliate Withdrawals</h1>
        <p class="text-muted">Manage affiliate withdrawal requests and payments</p>
    </div>
    <div>
        <a href="{{ route('super.affiliate-withdrawals.export') }}" class="btn btn-outline-primary">
            <i class="fas fa-download me-2"></i>
            Export Data
        </a>
    </div>
</div>

<!-- Stats Cards -->
<div class="row mb-4">
    <!-- Total Requests -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Requests
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total_requests'] }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Amount -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Pending Amount
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">${{ number_format($stats['pending_amount'], 2) }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Total Paid -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Total Paid
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">${{ number_format($stats['total_paid'], 2) }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Total Rejected -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            Total Rejected
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total_rejected'] }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Filters</h6>
    </div>
    <div class="card-body">
        <form method="GET" action="{{ route('super.affiliate-withdrawals.index') }}">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Statuses</option>
                        <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>Pending</option>
                        <option value="approved" {{ request('status') === 'approved' ? 'selected' : '' }}>Approved</option>
                        <option value="paid" {{ request('status') === 'paid' ? 'selected' : '' }}>Paid</option>
                        <option value="rejected" {{ request('status') === 'rejected' ? 'selected' : '' }}>Rejected</option>
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search"
                           value="{{ request('search') }}" placeholder="Affiliate name or email">
                </div>
                <div class="col-md-2 mb-3">
                    <label for="min_amount" class="form-label">Min Amount</label>
                    <input type="number" class="form-control" id="min_amount" name="min_amount"
                           value="{{ request('min_amount') }}" step="0.01">
                </div>
                <div class="col-md-2 mb-3">
                    <label for="max_amount" class="form-label">Max Amount</label>
                    <input type="number" class="form-control" id="max_amount" name="max_amount"
                           value="{{ request('max_amount') }}" step="0.01">
                </div>
                <div class="col-md-2 mb-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>Filter
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Withdrawals Table -->
<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">Withdrawal Requests</h6>
        @if($withdrawals->where('status', 'pending')->count() > 0)
            <div class="dropdown">
                <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-cogs me-2"></i>Bulk Actions
                </button>
                <ul class="dropdown-menu">
                    <li>
                        <form action="{{ route('super.affiliate-withdrawals.bulk-approve') }}" method="POST" class="d-inline">
                            @csrf
                            <button type="submit" class="dropdown-item" onclick="return confirm('Approve selected withdrawals?')">
                                <i class="fas fa-check text-success me-2"></i>Bulk Approve
                            </button>
                        </form>
                    </li>
                    <li>
                        <form action="{{ route('super.affiliate-withdrawals.bulk-reject') }}" method="POST" class="d-inline">
                            @csrf
                            <button type="submit" class="dropdown-item" onclick="return confirm('Reject selected withdrawals?')">
                                <i class="fas fa-times text-danger me-2"></i>Bulk Reject
                            </button>
                        </form>
                    </li>
                </ul>
            </div>
        @endif
    </div>
    <div class="card-body">
        @if($withdrawals->count() > 0)
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            @if($withdrawals->where('status', 'pending')->count() > 0)
                                <th width="30">
                                    <input type="checkbox" id="selectAll">
                                </th>
                            @endif
                            <th>Affiliate</th>
                            <th>Amount</th>
                            <th>Method</th>
                            <th>Status</th>
                            <th>Requested</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($withdrawals as $withdrawal)
                            <tr>
                                @if($withdrawals->where('status', 'pending')->count() > 0)
                                    <td>
                                        @if($withdrawal->status === 'pending')
                                            <input type="checkbox" name="withdrawal_ids[]" value="{{ $withdrawal->id }}" class="withdrawal-checkbox">
                                        @endif
                                    </td>
                                @endif
                                <td>
                                    <div>
                                        <strong>{{ $withdrawal->affiliate->user->name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ $withdrawal->affiliate->user->email }}</small>
                                        <br>
                                        <small class="text-muted">{{ $withdrawal->affiliate->affiliate_code }}</small>
                                    </div>
                                </td>
                                <td>
                                    <strong>${{ number_format($withdrawal->amount, 2) }}</strong>
                                    @if($withdrawal->fee_amount > 0)
                                        <br>
                                        <small class="text-muted">Fee: ${{ number_format($withdrawal->fee_amount, 2) }}</small>
                                        <br>
                                        <small class="text-success">Net: ${{ number_format($withdrawal->net_amount, 2) }}</small>
                                    @endif
                                </td>
                                <td>
                                    @if($withdrawal->payment_method === 'paypal')
                                        <i class="fab fa-paypal text-primary me-1"></i>PayPal
                                        <br>
                                        <small class="text-muted">{{ $withdrawal->payment_details['paypal_email'] ?? 'N/A' }}</small>
                                    @elseif($withdrawal->payment_method === 'bank_transfer')
                                        <i class="fas fa-university text-info me-1"></i>Bank Transfer
                                        <br>
                                        <small class="text-muted">{{ $withdrawal->payment_details['bank_name'] ?? 'N/A' }}</small>
                                    @else
                                        {{ ucfirst($withdrawal->payment_method) }}
                                    @endif
                                </td>
                                <td>
                                    @if($withdrawal->status === 'pending')
                                        <span class="badge bg-warning">Pending</span>
                                    @elseif($withdrawal->status === 'approved')
                                        <span class="badge bg-info">Approved</span>
                                    @elseif($withdrawal->status === 'paid')
                                        <span class="badge bg-success">Paid</span>
                                    @elseif($withdrawal->status === 'rejected')
                                        <span class="badge bg-danger">Rejected</span>
                                    @endif
                                </td>
                                <td>
                                    {{ $withdrawal->created_at->format('M d, Y') }}
                                    <br>
                                    <small class="text-muted">{{ $withdrawal->created_at->format('h:i A') }}</small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('super.affiliate-withdrawals.show', $withdrawal) }}"
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>

                                        @if($withdrawal->status === 'pending')
                                            <form action="{{ route('super.affiliate-withdrawals.approve', $withdrawal) }}" method="POST" class="d-inline">
                                                @csrf
                                                <button type="submit" class="btn btn-sm btn-success"
                                                        onclick="return confirm('Approve this withdrawal?')">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            </form>
                                            <button type="button" class="btn btn-sm btn-danger"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#rejectModal{{ $withdrawal->id }}">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        @elseif($withdrawal->status === 'approved')
                                            <button type="button" class="btn btn-sm btn-info"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#markAsPaidModal{{ $withdrawal->id }}">
                                                <i class="fas fa-money-bill-wave"></i>
                                            </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center">
                {{ $withdrawals->links() }}
            </div>
        @else
            <div class="text-center py-4">
                <i class="fas fa-money-bill-wave fa-3x text-gray-300 mb-3"></i>
                <h5 class="text-gray-600">No withdrawal requests found</h5>
                <p class="text-muted">Withdrawal requests will appear here when affiliates request payouts.</p>
            </div>
        @endif
    </div>
</div>

<!-- Mark as Paid Modals -->
@foreach($withdrawals as $withdrawal)
    @if($withdrawal->status === 'approved')
        <div class="modal fade" id="markAsPaidModal{{ $withdrawal->id }}" tabindex="-1" aria-labelledby="markAsPaidModalLabel{{ $withdrawal->id }}" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="markAsPaidModalLabel{{ $withdrawal->id }}">
                            Mark Withdrawal as Paid
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form action="{{ route('super.affiliate-withdrawals.mark-as-paid', $withdrawal) }}" method="POST">
                        @csrf
                        <div class="modal-body">
                            <div class="mb-3">
                                <h6>Withdrawal Details:</h6>
                                <ul class="list-unstyled">
                                    <li><strong>Affiliate:</strong> {{ $withdrawal->affiliate->user->name }}</li>
                                    <li><strong>Amount:</strong> ${{ number_format($withdrawal->amount, 2) }}</li>
                                    <li><strong>Net Amount:</strong> ${{ number_format($withdrawal->net_amount, 2) }}</li>
                                    <li><strong>Payment Method:</strong> {{ ucfirst(str_replace('_', ' ', $withdrawal->payment_method)) }}</li>
                                </ul>
                            </div>

                            <div class="mb-3">
                                <label for="transaction_reference{{ $withdrawal->id }}" class="form-label">
                                    Transaction Reference <span class="text-danger">*</span>
                                </label>
                                <input type="text"
                                       class="form-control"
                                       id="transaction_reference{{ $withdrawal->id }}"
                                       name="transaction_reference"
                                       required
                                       placeholder="Enter transaction ID, reference number, or payment confirmation">
                                <div class="form-text">
                                    Enter the transaction reference from your payment system (e.g., PayPal transaction ID, bank transfer reference, etc.)
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="notes{{ $withdrawal->id }}" class="form-label">Notes (Optional)</label>
                                <textarea class="form-control"
                                          id="notes{{ $withdrawal->id }}"
                                          name="notes"
                                          rows="3"
                                          placeholder="Add any additional notes about this payment..."></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-check me-2"></i>Mark as Paid
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    @endif
@endforeach

<!-- Reject Withdrawal Modals -->
@foreach($withdrawals as $withdrawal)
    @if($withdrawal->status === 'pending')
        <div class="modal fade" id="rejectModal{{ $withdrawal->id }}" tabindex="-1" aria-labelledby="rejectModalLabel{{ $withdrawal->id }}" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="rejectModalLabel{{ $withdrawal->id }}">
                            Reject Withdrawal Request
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form action="{{ route('super.affiliate-withdrawals.reject', $withdrawal) }}" method="POST">
                        @csrf
                        <div class="modal-body">
                            <div class="mb-3">
                                <h6>Withdrawal Details:</h6>
                                <ul class="list-unstyled">
                                    <li><strong>Affiliate:</strong> {{ $withdrawal->affiliate->user->name }}</li>
                                    <li><strong>Amount:</strong> ${{ number_format($withdrawal->amount, 2) }}</li>
                                    <li><strong>Net Amount:</strong> ${{ number_format($withdrawal->net_amount, 2) }}</li>
                                    <li><strong>Payment Method:</strong> {{ ucfirst(str_replace('_', ' ', $withdrawal->payment_method)) }}</li>
                                </ul>
                            </div>

                            <div class="mb-3">
                                <label for="rejection_reason{{ $withdrawal->id }}" class="form-label">
                                    Rejection Reason <span class="text-danger">*</span>
                                </label>
                                <textarea class="form-control"
                                          id="rejection_reason{{ $withdrawal->id }}"
                                          name="rejection_reason"
                                          rows="4"
                                          required
                                          placeholder="Please provide a clear reason for rejecting this withdrawal request..."></textarea>
                                <div class="form-text">
                                    This reason will be visible to the affiliate. Please be clear and professional.
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-times me-2"></i>Reject Withdrawal
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    @endif
@endforeach

@push('scripts')
<script>
    // Select all functionality
    document.getElementById('selectAll')?.addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.withdrawal-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });

    // Update select all when individual checkboxes change
    document.querySelectorAll('.withdrawal-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const allCheckboxes = document.querySelectorAll('.withdrawal-checkbox');
            const checkedCheckboxes = document.querySelectorAll('.withdrawal-checkbox:checked');
            const selectAllCheckbox = document.getElementById('selectAll');

            if (selectAllCheckbox) {
                selectAllCheckbox.checked = allCheckboxes.length === checkedCheckboxes.length;
                selectAllCheckbox.indeterminate = checkedCheckboxes.length > 0 && checkedCheckboxes.length < allCheckboxes.length;
            }
        });
    });
</script>
@endpush
@endsection
