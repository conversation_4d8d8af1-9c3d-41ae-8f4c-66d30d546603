@extends('super_admin.layouts.app')

@section('title', 'Edit Ticket #' . $ticket->ticket_number)

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Edit Ticket #{{ $ticket->ticket_number }}</h1>
                    <p class="text-muted mb-0">{{ $ticket->title }}</p>
                </div>
                <div>
                    <a href="{{ route('super.support.tickets.show', $ticket) }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left"></i> Back to Ticket
                    </a>
                    <a href="{{ route('super.support.tickets.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-list"></i> All Tickets
                    </a>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-edit me-2"></i>
                                Edit Ticket Details
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="{{ route('super.support.tickets.update', $ticket) }}">
                                @csrf
                                @method('PUT')
                                
                                <div class="row mb-3">
                                    <div class="col-md-8">
                                        <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                                        <input type="text" name="title" id="title" class="form-control @error('title') is-invalid @enderror" 
                                               value="{{ old('title', $ticket->title) }}" required>
                                        @error('title')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="col-md-4">
                                        <label for="ticket_number" class="form-label">Ticket Number</label>
                                        <input type="text" id="ticket_number" class="form-control" 
                                               value="{{ $ticket->ticket_number }}" readonly>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label for="priority" class="form-label">Priority <span class="text-danger">*</span></label>
                                        <select name="priority" id="priority" class="form-select @error('priority') is-invalid @enderror" required>
                                            <option value="low" {{ old('priority', $ticket->priority) == 'low' ? 'selected' : '' }}>Low</option>
                                            <option value="normal" {{ old('priority', $ticket->priority) == 'normal' ? 'selected' : '' }}>Normal</option>
                                            <option value="high" {{ old('priority', $ticket->priority) == 'high' ? 'selected' : '' }}>High</option>
                                            <option value="urgent" {{ old('priority', $ticket->priority) == 'urgent' ? 'selected' : '' }}>Urgent</option>
                                            <option value="critical" {{ old('priority', $ticket->priority) == 'critical' ? 'selected' : '' }}>Critical</option>
                                        </select>
                                        @error('priority')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="col-md-4">
                                        <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                        <select name="status" id="status" class="form-select @error('status') is-invalid @enderror" required>
                                            <option value="open" {{ old('status', $ticket->status) == 'open' ? 'selected' : '' }}>Open</option>
                                            <option value="in_progress" {{ old('status', $ticket->status) == 'in_progress' ? 'selected' : '' }}>In Progress</option>
                                            <option value="waiting_customer" {{ old('status', $ticket->status) == 'waiting_customer' ? 'selected' : '' }}>Waiting for Customer</option>
                                            <option value="waiting_admin" {{ old('status', $ticket->status) == 'waiting_admin' ? 'selected' : '' }}>Waiting for Admin</option>
                                            <option value="resolved" {{ old('status', $ticket->status) == 'resolved' ? 'selected' : '' }}>Resolved</option>
                                            <option value="closed" {{ old('status', $ticket->status) == 'closed' ? 'selected' : '' }}>Closed</option>
                                        </select>
                                        @error('status')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="col-md-4">
                                        <label for="category" class="form-label">Category <span class="text-danger">*</span></label>
                                        <select name="category" id="category" class="form-select @error('category') is-invalid @enderror" required>
                                            <option value="technical" {{ old('category', $ticket->category) == 'technical' ? 'selected' : '' }}>Technical Support</option>
                                            <option value="billing" {{ old('category', $ticket->category) == 'billing' ? 'selected' : '' }}>Billing & Payments</option>
                                            <option value="account" {{ old('category', $ticket->category) == 'account' ? 'selected' : '' }}>Account Management</option>
                                            <option value="feature_request" {{ old('category', $ticket->category) == 'feature_request' ? 'selected' : '' }}>Feature Request</option>
                                            <option value="bug_report" {{ old('category', $ticket->category) == 'bug_report' ? 'selected' : '' }}>Bug Report</option>
                                            <option value="general" {{ old('category', $ticket->category) == 'general' ? 'selected' : '' }}>General Inquiry</option>
                                        </select>
                                        @error('category')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="assigned_to" class="form-label">Assigned To</label>
                                        <select name="assigned_to" id="assigned_to" class="form-select @error('assigned_to') is-invalid @enderror">
                                            <option value="">Unassigned</option>
                                            @foreach($admins as $admin)
                                                <option value="{{ $admin->id }}" {{ old('assigned_to', $ticket->assigned_to) == $admin->id ? 'selected' : '' }}>
                                                    {{ $admin->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('assigned_to')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="col-md-6">
                                        <label for="organization" class="form-label">Organization</label>
                                        <input type="text" id="organization" class="form-control" 
                                               value="{{ $ticket->organization->name ?? 'N/A' }}" readonly>
                                        <div class="form-text">Organization cannot be changed after ticket creation</div>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                                    <textarea name="description" id="description" class="form-control @error('description') is-invalid @enderror" 
                                              rows="8" required>{{ old('description', $ticket->description) }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('super.support.tickets.show', $ticket) }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times"></i> Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> Update Ticket
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- Current Status -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                Current Status
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-2">
                                <strong>Priority:</strong> {!! $ticket->priority_badge !!}
                            </div>
                            <div class="mb-2">
                                <strong>Status:</strong> {!! $ticket->status_badge !!}
                            </div>
                            <div class="mb-2">
                                <strong>Category:</strong> 
                                <span class="badge bg-secondary">{{ ucfirst(str_replace('_', ' ', $ticket->category)) }}</span>
                            </div>
                            <div class="mb-2">
                                <strong>Assigned To:</strong>
                                @if($ticket->assignedAdmin)
                                    <span class="badge bg-info">{{ $ticket->assignedAdmin->name }}</span>
                                @else
                                    <span class="badge bg-warning">Unassigned</span>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Ticket Timeline -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-clock me-2"></i>
                                Timeline
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-2">
                                <strong>Created:</strong><br>
                                {{ $ticket->created_at->format('M d, Y H:i') }}<br>
                                <small class="text-muted">{{ $ticket->created_at->diffForHumans() }}</small>
                            </div>
                            @if($ticket->first_response_at)
                            <div class="mb-2">
                                <strong>First Response:</strong><br>
                                {{ $ticket->first_response_at->format('M d, Y H:i') }}<br>
                                <small class="text-muted">{{ $ticket->response_time }}h response time</small>
                            </div>
                            @endif
                            @if($ticket->resolved_at)
                            <div class="mb-2">
                                <strong>Resolved:</strong><br>
                                {{ $ticket->resolved_at->format('M d, Y H:i') }}<br>
                                <small class="text-muted">{{ $ticket->resolution_time }}h resolution time</small>
                            </div>
                            @endif
                            <div class="mb-2">
                                <strong>Last Updated:</strong><br>
                                {{ $ticket->updated_at->format('M d, Y H:i') }}<br>
                                <small class="text-muted">{{ $ticket->updated_at->diffForHumans() }}</small>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-bolt me-2"></i>
                                Quick Actions
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="{{ route('super.support.tickets.show', $ticket) }}" class="btn btn-outline-info">
                                    <i class="fas fa-eye"></i> View Ticket
                                </a>
                                @if($ticket->user)
                                    <a href="{{ route('super.impersonation.start', $ticket->user) }}" 
                                       class="btn btn-outline-warning"
                                       onclick="return confirm('Impersonate this user?')">
                                        <i class="fas fa-user-secret"></i> Impersonate User
                                    </a>
                                @endif
                                <a href="{{ route('super.support.tickets.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-list"></i> All Tickets
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-resize textarea
    const textarea = document.getElementById('description');
    textarea.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });

    // Status change warnings
    const statusSelect = document.getElementById('status');
    statusSelect.addEventListener('change', function() {
        const status = this.value;
        if (status === 'closed') {
            if (!confirm('Are you sure you want to close this ticket? This action should only be done after the issue is fully resolved.')) {
                this.value = '{{ $ticket->status }}'; // Reset to original value
            }
        }
    });
});
</script>
@endsection
