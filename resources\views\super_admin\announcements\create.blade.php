@extends('super_admin.layouts.app')

@section('title', isset($announcement) ? 'Edit Announcement' : 'Create Announcement')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">{{ isset($announcement) ? 'Edit' : 'Create' }} Announcement</h1>
                    <p class="text-muted mb-0">{{ isset($announcement) ? 'Update announcement settings and content' : 'Communicate important information to users' }}</p>
                </div>
                <div>
                    @if(isset($announcement))
                        <a href="{{ route('super.announcements.show', $announcement) }}" class="btn btn-outline-info me-2">
                            <i class="fas fa-eye"></i> View Announcement
                        </a>
                    @endif
                    <a href="{{ route('super.announcements.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Announcements
                    </a>
                </div>
            </div>

            <form method="POST" action="{{ isset($announcement) ? route('super.announcements.update', $announcement) : route('super.announcements.store') }}">
                @if(isset($announcement))
                    @method('PUT')
                @endif
                @csrf
                <div class="row">
                    <!-- Main Content -->
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Announcement Content</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                                    <input type="text" name="title" id="title"
                                           class="form-control @error('title') is-invalid @enderror"
                                           value="{{ old('title', isset($announcement) ? $announcement->title : request('title')) }}" required>
                                    @error('title')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="content" class="form-label">Content <span class="text-danger">*</span></label>
                                    <textarea name="content" id="content"
                                              class="form-control @error('content') is-invalid @enderror"
                                              rows="8" required>{{ old('content', isset($announcement) ? $announcement->content : '') }}</textarea>
                                    @error('content')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">
                                        You can use HTML formatting for rich content.
                                    </div>
                                </div>

                                <div class="mb-3" id="affectedFeaturesSection" style="display: none;">
                                    <label for="affected_features" class="form-label">Affected Features</label>
                                    <input type="text" name="affected_features" id="affected_features"
                                           class="form-control @error('affected_features') is-invalid @enderror"
                                           value="{{ old('affected_features', isset($announcement) && $announcement->affected_features ? implode(', ', $announcement->affected_features) : '') }}"
                                           placeholder="orders, billing, reports">
                                    @error('affected_features')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">
                                        Comma-separated list of features affected by maintenance.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sidebar -->
                    <div class="col-lg-4">
                        <!-- Announcement Settings -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="card-title mb-0">Announcement Settings</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="type" class="form-label">Type <span class="text-danger">*</span></label>
                                    <select name="type" id="type"
                                            class="form-control @error('type') is-invalid @enderror" required>
                                        @foreach(\App\Models\Announcement::getTypeOptions() as $value => $label)
                                            <option value="{{ $value }}" {{ old('type', isset($announcement) ? $announcement->type : request('type')) === $value ? 'selected' : '' }}>
                                                {{ $label }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="priority" class="form-label">Priority <span class="text-danger">*</span></label>
                                    <select name="priority" id="priority"
                                            class="form-control @error('priority') is-invalid @enderror" required>
                                        @foreach(\App\Models\Announcement::getPriorityOptions() as $value => $label)
                                            <option value="{{ $value }}" {{ old('priority', isset($announcement) ? $announcement->priority : request('priority', 'normal')) === $value ? 'selected' : '' }}>
                                                {{ $label }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('priority')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="target_audience" class="form-label">Target Audience <span class="text-danger">*</span></label>
                                    <select name="target_audience" id="target_audience"
                                            class="form-control @error('target_audience') is-invalid @enderror" required>
                                        @foreach(\App\Models\Announcement::getTargetAudienceOptions() as $value => $label)
                                            <option value="{{ $value }}" {{ old('target_audience', isset($announcement) ? $announcement->target_audience : 'all') === $value ? 'selected' : '' }}>
                                                {{ $label }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('target_audience')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Display Settings -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="card-title mb-0">Display Settings</h6>
                            </div>
                            <div class="card-body">
                                <div class="form-check mb-3">
                                    <input type="checkbox" name="show_on_dashboard" id="show_on_dashboard"
                                           class="form-check-input" value="1" {{ old('show_on_dashboard', isset($announcement) ? $announcement->show_on_dashboard : true) ? 'checked' : '' }}>
                                    <label for="show_on_dashboard" class="form-check-label">
                                        Show on Dashboard
                                    </label>
                                    <div class="form-text">Display this announcement on user dashboards</div>
                                </div>

                                <div class="form-check mb-3">
                                    <input type="checkbox" name="show_on_login" id="show_on_login"
                                           class="form-check-input" value="1" {{ old('show_on_login', isset($announcement) ? $announcement->show_on_login : false) ? 'checked' : '' }}>
                                    <label for="show_on_login" class="form-check-label">
                                        Show on Login Page
                                    </label>
                                    <div class="form-text">Display this announcement on the login page</div>
                                </div>

                                <div class="form-check mb-3">
                                    <input type="checkbox" name="is_dismissible" id="is_dismissible"
                                           class="form-check-input" value="1" {{ old('is_dismissible', isset($announcement) ? $announcement->is_dismissible : true) ? 'checked' : '' }}>
                                    <label for="is_dismissible" class="form-check-label">
                                        Dismissible
                                    </label>
                                    <div class="form-text">Allow users to dismiss this announcement</div>
                                </div>

                                <div class="form-check">
                                    <input type="checkbox" name="send_email" id="send_email"
                                           class="form-check-input" value="1" {{ old('send_email', isset($announcement) ? $announcement->send_email : false) ? 'checked' : '' }}>
                                    <label for="send_email" class="form-check-label">
                                        Send Email Notifications
                                    </label>
                                    <div class="form-text">Send email notifications to target audience</div>
                                </div>
                            </div>
                        </div>

                        <!-- Schedule Settings -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="card-title mb-0">Schedule Settings</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="starts_at" class="form-label">Start Date & Time</label>
                                    <input type="datetime-local" name="starts_at" id="starts_at"
                                           class="form-control @error('starts_at') is-invalid @enderror"
                                           value="{{ old('starts_at', isset($announcement) && $announcement->starts_at ? $announcement->starts_at->format('Y-m-d\TH:i') : '') }}">
                                    @error('starts_at')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Leave empty to start immediately when published</div>
                                </div>

                                <div class="mb-3">
                                    <label for="ends_at" class="form-label">End Date & Time</label>
                                    <input type="datetime-local" name="ends_at" id="ends_at"
                                           class="form-control @error('ends_at') is-invalid @enderror"
                                           value="{{ old('ends_at', isset($announcement) && $announcement->ends_at ? $announcement->ends_at->format('Y-m-d\TH:i') : '') }}">
                                    @error('ends_at')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Leave empty for permanent announcement</div>
                                </div>
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">Actions</h6>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    @if(isset($announcement))
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save"></i> Update Announcement
                                        </button>
                                        @if(!$announcement->published_at || $announcement->published_at > now())
                                            <button type="submit" name="publish_now" value="1" class="btn btn-success">
                                                <i class="fas fa-globe"></i> Update & Publish
                                            </button>
                                        @endif
                                    @else
                                        <button type="submit" name="publish_now" value="1" class="btn btn-primary">
                                            <i class="fas fa-globe"></i> Create & Publish
                                        </button>
                                        <button type="submit" name="publish_now" value="0" class="btn btn-outline-secondary">
                                            <i class="fas fa-save"></i> Save as Draft
                                        </button>
                                    @endif
                                    <a href="{{ route('super.announcements.index') }}" class="btn btn-outline-danger">
                                        <i class="fas fa-times"></i> Cancel
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const typeSelect = document.getElementById('type');
    const affectedFeaturesSection = document.getElementById('affectedFeaturesSection');

    function toggleAffectedFeatures() {
        if (typeSelect.value === 'maintenance') {
            affectedFeaturesSection.style.display = 'block';
        } else {
            affectedFeaturesSection.style.display = 'none';
        }
    }

    typeSelect.addEventListener('change', toggleAffectedFeatures);
    toggleAffectedFeatures(); // Initial check

    // Auto-fill content based on type
    typeSelect.addEventListener('change', function() {
        const contentTextarea = document.getElementById('content');
        const titleInput = document.getElementById('title');

        if (!contentTextarea.value && !titleInput.value) {
            switch (this.value) {
                case 'maintenance':
                    titleInput.value = 'Scheduled Maintenance';
                    contentTextarea.value = 'We will be performing scheduled maintenance on our system. During this time, some features may be temporarily unavailable. We apologize for any inconvenience.';
                    break;
                case 'info':
                    titleInput.value = 'System Update';
                    contentTextarea.value = 'We have released new features and improvements to enhance your experience. Check out the latest updates!';
                    break;
                case 'warning':
                    titleInput.value = 'Important Notice';
                    contentTextarea.value = 'Please be aware of the following important information regarding our service.';
                    break;
                case 'danger':
                    titleInput.value = 'Urgent Alert';
                    contentTextarea.value = 'This is an urgent notification that requires your immediate attention.';
                    break;
                case 'success':
                    titleInput.value = 'Good News!';
                    contentTextarea.value = 'We are pleased to announce some positive updates to our service.';
                    break;
            }
        }
    });
});
</script>
@endsection
