@extends('layouts.auth')

@section('content')
<div class="row justify-content-center">
    <div class="col-lg-5">
        <div class="card shadow-lg">
            <div class="card-header text-white">
                <div class="shape-1"></div>
                <div class="shape-2"></div>
                <h3 class="text-center font-weight-light my-2">Create Your Organization</h3>
            </div>
            <div class="card-body">
                @if ($errors->any())
                <div class="alert alert-danger border-start border-5 border-danger">
                    <ul class="mb-0">
                        @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
                @endif

                <form method="POST" action="{{ route('register.submit') }}" id="registration-form">
                    @csrf

                    <!-- Step indicator -->
                    <div class="d-flex justify-content-center mb-4">
                        <div class="step-indicator">
                            <div class="step active" id="step-1-indicator">
                                <div class="step-number">1</div>
                                <div class="step-text">Organization</div>
                            </div>
                            <div class="step-line"></div>
                            <div class="step" id="step-2-indicator">
                                <div class="step-number">2</div>
                                <div class="step-text">Branch</div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 1: Organization Details -->
                    <div id="step-1">
                        <div class="form-floating mb-4">
                            <input class="form-control @error('organization_name') is-invalid @enderror"
                                id="organization_name" type="text" name="organization_name"
                                value="{{ old('organization_name') }}" required autofocus />
                            <label for="organization_name"><i class="fas fa-building me-2"></i>Organization Name</label>
                            @error('organization_name')
                            <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-floating mb-4">
                            <input class="form-control @error('name') is-invalid @enderror"
                                id="name" type="text" name="name"
                                value="{{ old('name') }}" required />
                            <label for="name"><i class="fas fa-user me-2"></i>Your Name</label>
                            @error('name')
                            <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-floating mb-4">
                            <input class="form-control @error('email') is-invalid @enderror"
                                id="email" type="email" name="email"
                                value="{{ old('email') }}" required />
                            <label for="email"><i class="fas fa-envelope me-2"></i>Email Address</label>
                            @error('email')
                            <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="form-floating mb-3 mb-md-0 position-relative">
                                    <input class="form-control @error('password') is-invalid @enderror"
                                        id="password" type="password" name="password" required />
                                    <label for="password"><i class="fas fa-lock me-2"></i>Password</label>
                                    <button type="button" class="btn btn-link position-absolute end-0 top-50 translate-middle-y text-decoration-none" style="z-index: 10;" onclick="togglePasswordVisibility('password')">
                                        <i class="fas fa-eye-slash toggle-password" data-target="password"></i>
                                    </button>
                                    @error('password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating position-relative">
                                    <input class="form-control" id="password-confirm"
                                        type="password" name="password_confirmation" required />
                                    <label for="password-confirm"><i class="fas fa-check-circle me-2"></i>Confirm Password</label>
                                    <button type="button" class="btn btn-link position-absolute end-0 top-50 translate-middle-y text-decoration-none" style="z-index: 10;" onclick="togglePasswordVisibility('password-confirm')">
                                        <i class="fas fa-eye-slash toggle-password" data-target="password-confirm"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex align-items-center justify-content-between mt-4 mb-2">
                            <a class="small text-info" href="{{ route('login') }}">
                                <i class="fas fa-arrow-left me-1"></i>Already have an account?
                            </a>
                            <button type="button" class="btn btn-primary px-4" id="next-step-btn">
                                <i class="fas fa-arrow-right me-2"></i>Next
                            </button>
                        </div>
                    </div>

                    <!-- Step 2: Branch Details -->
                    <div id="step-2" style="display: none;">
                        <div class="mb-3">
                            <label for="branch_name" class="form-label">Branch Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('branch_name') is-invalid @enderror"
                                   id="branch_name" name="branch_name" value="{{ old('branch_name') }}" required>
                            @error('branch_name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="branch_email" class="form-label">Branch Email</label>
                            <input type="email" class="form-control @error('branch_email') is-invalid @enderror"
                                   id="branch_email" name="branch_email" value="{{ old('branch_email') }}">
                            @error('branch_email')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="branch_phone" class="form-label">Branch Phone</label>
                            <input type="tel" class="form-control @error('branch_phone') is-invalid @enderror"
                                   id="branch_phone" name="branch_phone" value="{{ old('branch_phone') }}">
                            <input type="hidden" name="branch_phone_full" id="branch_phone_full">
                            @error('branch_phone')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="branch_address" class="form-label">Branch Address</label>
                            <textarea class="form-control @error('branch_address') is-invalid @enderror"
                                      id="branch_address" name="branch_address" rows="3">{{ old('branch_address') }}</textarea>
                            @error('branch_address')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="branch_description" class="form-label">Description</label>
                            <textarea class="form-control @error('branch_description') is-invalid @enderror"
                                      id="branch_description" name="branch_description" rows="3">{{ old('branch_description') }}</textarea>
                            @error('branch_description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <input type="hidden" name="switch_to_branch" value="1">
                        <input type="hidden" name="redirect_to_dashboard" value="1">

                        <div class="d-flex align-items-center justify-content-between mt-4 mb-2">
                            <button type="button" class="btn btn-secondary px-4" id="prev-step-btn">
                                <i class="fas fa-arrow-left me-2"></i>Back
                            </button>
                            <button type="submit" class="btn btn-primary px-4">
                                <i class="fas fa-user-plus me-2"></i>Register
                            </button>
                        </div>
                    </div>
                </form>
            </div>
            <div class="card-footer text-center py-3">
                <div class="small">
                    <p class="text-muted mb-0">
                        <i class="fas fa-shield-alt me-1"></i>
                        By registering, you'll become the Organization Owner
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .step-indicator {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
    }
    .step {
        display: flex;
        flex-direction: column;
        align-items: center;
    }
    .step-number {
        background-color: #dee2e6;
        color: #212529;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 5px;
    }
    .step.active .step-number {
        background-color: #0d6efd;
        color: white;
    }
    .step-text {
        font-size: 0.8rem;
        color: #6c757d;
    }
    .step.active .step-text {
        color: #0d6efd;
        font-weight: bold;
    }
    .step-line {
        height: 2px;
        background-color: #dee2e6;
        width: 50px;
        margin: 0 10px;
        margin-bottom: 15px;
    }

    /* Custom styles for intl-tel-input to match Bootstrap form design */
    .iti {
        width: 100%;
    }
    .iti__flag-container {
        display: flex;
    }
    .iti__selected-flag {
        border-radius: 0.375rem 0 0 0.375rem;
        background-color: #fff;
        border: 1px solid #ced4da;
        border-right: none;
        height: calc(3.5rem + 2px);
    }
    .iti--allow-dropdown input {
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
        height: calc(3.5rem + 2px);
    }
    .iti__country-list {
        z-index: 1050;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const step1 = document.getElementById('step-1');
        const step2 = document.getElementById('step-2');
        const step1Indicator = document.getElementById('step-1-indicator');
        const step2Indicator = document.getElementById('step-2-indicator');
        const nextStepBtn = document.getElementById('next-step-btn');
        const prevStepBtn = document.getElementById('prev-step-btn');

        nextStepBtn.addEventListener('click', function() {
            // Validate step 1 fields
            const orgName = document.getElementById('organization_name').value;
            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const passwordConfirm = document.getElementById('password-confirm').value;

            if (!orgName || !name || !email || !password || !passwordConfirm) {
                alert('Please fill in all required fields.');
                return;
            }

            if (password !== passwordConfirm) {
                alert('Passwords do not match.');
                return;
            }

            // Move to step 2
            step1.style.display = 'none';
            step2.style.display = 'block';
            step1Indicator.classList.remove('active');
            step2Indicator.classList.add('active');
        });

        prevStepBtn.addEventListener('click', function() {
            // Go back to step 1
            step2.style.display = 'none';
            step1.style.display = 'block';
            step2Indicator.classList.remove('active');
            step1Indicator.classList.add('active');
        });

        // Initialize international telephone input for branch phone
        const branchPhoneInputField = document.querySelector("#branch_phone");
        const branchPhoneInput = window.intlTelInput(branchPhoneInputField, {
            utilsScript: "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/js/utils.js",
            initialCountry: "auto",
            geoIpLookup: function(callback) {
                fetch("https://ipapi.co/json")
                  .then(function(res) { return res.json(); })
                  .then(function(data) { callback(data.country_code); })
                  .catch(function() { callback("us"); });
            },
            preferredCountries: ["ng", "us", "gb", "ca"],
            separateDialCode: true,
            formatOnDisplay: true,
        });

        // Store the full number with country code when submitting the form
        document.getElementById('registration-form').addEventListener('submit', function() {
            const fullNumber = branchPhoneInput.getNumber();
            document.getElementById('branch_phone_full').value = fullNumber;
        });
    });
</script>
@endsection
