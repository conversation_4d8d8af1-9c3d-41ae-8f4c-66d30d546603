@extends('super_admin.layouts.app')

@section('title', 'Knowledge Base Analytics')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Knowledge Base Analytics</h1>
                    <p class="text-muted mb-0">Performance metrics and insights for your help content</p>
                </div>
                <div>
                    <a href="{{ route('super.knowledge-base.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Articles
                    </a>
                </div>
            </div>

            <!-- Key Metrics -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ $metrics['total_articles'] }}</h4>
                                    <p class="mb-0">Total Articles</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-book fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ $metrics['published_articles'] }}</h4>
                                    <p class="mb-0">Published</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-globe fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ number_format($metrics['total_views']) }}</h4>
                                    <p class="mb-0">Total Views</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-eye fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ $metrics['avg_helpfulness'] }}%</h4>
                                    <p class="mb-0">Avg Helpfulness</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-thumbs-up fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Popular Articles -->
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-star me-2"></i>
                                Most Popular Articles
                            </h5>
                        </div>
                        <div class="card-body">
                            @if($metrics['popular_articles']->count() > 0)
                                @foreach($metrics['popular_articles'] as $article)
                                <div class="d-flex justify-content-between align-items-center mb-3 {{ !$loop->last ? 'border-bottom pb-3' : '' }}">
                                    <div class="flex-grow-1">
                                        <div class="fw-bold">
                                            <a href="{{ route('super.knowledge-base.show', $article) }}" class="text-decoration-none">
                                                {{ $article->title }}
                                            </a>
                                        </div>
                                        <small class="text-muted">
                                            {{ $article->category->name ?? 'Uncategorized' }}
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge bg-primary">{{ number_format($article->view_count) }} views</span>
                                        @if($article->helpfulness_percentage)
                                            <div class="mt-1">
                                                <span class="badge bg-success">{{ $article->helpfulness_percentage }}% helpful</span>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                                @endforeach
                            @else
                                <div class="text-center py-4">
                                    <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">No article data available yet.</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Recent Feedback -->
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-comments me-2"></i>
                                Recent User Feedback
                            </h5>
                        </div>
                        <div class="card-body">
                            @if($metrics['recent_feedback']->count() > 0)
                                @foreach($metrics['recent_feedback'] as $feedback)
                                <div class="d-flex align-items-start mb-3 {{ !$loop->last ? 'border-bottom pb-3' : '' }}">
                                    <div class="me-3">
                                        @if($feedback->is_helpful)
                                            <span class="badge bg-success">
                                                <i class="fas fa-thumbs-up"></i>
                                            </span>
                                        @else
                                            <span class="badge bg-danger">
                                                <i class="fas fa-thumbs-down"></i>
                                            </span>
                                        @endif
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="fw-bold">
                                            <a href="{{ route('super.knowledge-base.show', $feedback->article) }}" class="text-decoration-none">
                                                {{ Str::limit($feedback->article->title, 40) }}
                                            </a>
                                        </div>
                                        @if($feedback->comment)
                                            <p class="mb-1 small">{{ Str::limit($feedback->comment, 80) }}</p>
                                        @endif
                                        <small class="text-muted">
                                            {{ $feedback->created_at->diffForHumans() }}
                                            @if($feedback->user)
                                                by {{ $feedback->user->name }}
                                            @endif
                                        </small>
                                    </div>
                                </div>
                                @endforeach
                            @else
                                <div class="text-center py-4">
                                    <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">No user feedback yet.</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Performance -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-line me-2"></i>
                                Content Performance Overview
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-3 col-md-6 text-center mb-3">
                                    <h4 class="text-primary">{{ $metrics['total_articles'] }}</h4>
                                    <p class="text-muted mb-0">Total Articles</p>
                                    <small class="text-success">
                                        {{ $metrics['published_articles'] }} published
                                    </small>
                                </div>
                                <div class="col-lg-3 col-md-6 text-center mb-3">
                                    <h4 class="text-info">{{ number_format($metrics['total_views']) }}</h4>
                                    <p class="text-muted mb-0">Total Page Views</p>
                                    <small class="text-muted">
                                        {{ $metrics['published_articles'] > 0 ? number_format($metrics['total_views'] / $metrics['published_articles']) : 0 }} avg per article
                                    </small>
                                </div>
                                <div class="col-lg-3 col-md-6 text-center mb-3">
                                    <h4 class="text-success">{{ $metrics['avg_helpfulness'] }}%</h4>
                                    <p class="text-muted mb-0">Average Helpfulness</p>
                                    <small class="text-muted">
                                        Based on user feedback
                                    </small>
                                </div>
                                <div class="col-lg-3 col-md-6 text-center mb-3">
                                    <h4 class="text-warning">
                                        @if($metrics['popular_articles']->count() > 0)
                                            {{ $metrics['popular_articles']->first()->view_count }}
                                        @else
                                            0
                                        @endif
                                    </h4>
                                    <p class="text-muted mb-0">Most Popular Article</p>
                                    <small class="text-muted">
                                        @if($metrics['popular_articles']->count() > 0)
                                            {{ Str::limit($metrics['popular_articles']->first()->title, 20) }}
                                        @else
                                            No articles yet
                                        @endif
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mt-4">
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-plus-circle fa-2x text-primary mb-3"></i>
                            <h5>Create New Article</h5>
                            <p class="text-muted">Add more helpful content for your users</p>
                            <a href="{{ route('super.knowledge-base.create') }}" class="btn btn-primary">
                                Create Article
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-folder fa-2x text-info mb-3"></i>
                            <h5>Manage Categories</h5>
                            <p class="text-muted">Organize your content structure</p>
                            <a href="{{ route('super.knowledge-base.categories.index') }}" class="btn btn-info">
                                Manage Categories
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-list fa-2x text-success mb-3"></i>
                            <h5>View All Articles</h5>
                            <p class="text-muted">Browse and edit existing content</p>
                            <a href="{{ route('super.knowledge-base.index') }}" class="btn btn-success">
                                View Articles
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
