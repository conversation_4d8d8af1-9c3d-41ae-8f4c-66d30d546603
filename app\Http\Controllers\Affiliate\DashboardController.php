<?php

namespace App\Http\Controllers\Affiliate;

use App\Http\Controllers\Controller;
use App\Models\Affiliate;
use App\Models\AffiliateEarning;
use App\Models\AffiliateReferral;
use App\Models\AffiliateWithdrawal;
use App\Services\ReferralTrackingService;
use App\Services\CommissionCalculationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class DashboardController extends Controller
{
    protected $referralTrackingService;
    protected $commissionCalculationService;

    public function __construct(
        ReferralTrackingService $referralTrackingService,
        CommissionCalculationService $commissionCalculationService
    ) {
        $this->referralTrackingService = $referralTrackingService;
        $this->commissionCalculationService = $commissionCalculationService;
    }

    /**
     * Display the affiliate dashboard
     */
    public function index(Request $request)
    {
        // Get affiliate from middleware or directly
        $affiliate = $request->affiliate ?? $this->getAffiliate();

        if (!$affiliate) {
            return redirect()->route('affiliate.register')
                ->with('error', 'You need to register as an affiliate first.');
        }

        if (!$affiliate->isActive()) {
            return redirect()->route('affiliate.pending')
                ->with('info', 'Your affiliate account is pending approval.');
        }

        // Get dashboard statistics
        $stats = $this->getDashboardStats($affiliate);

        // Get recent referrals
        $recentReferrals = $affiliate->referrals()
            ->with(['organization'])
            ->latest('registration_date')
            ->take(5)
            ->get();

        // Get recent earnings
        $recentEarnings = $affiliate->earnings()
            ->with(['organization'])
            ->latest('earned_at')
            ->take(5)
            ->get();

        // Get performance data for charts
        $performanceData = $this->getPerformanceData($affiliate);

        return view('affiliate.dashboard', compact(
            'affiliate',
            'stats',
            'recentReferrals',
            'recentEarnings',
            'performanceData'
        ));
    }

    /**
     * Get dashboard statistics
     */
    protected function getDashboardStats(Affiliate $affiliate): array
    {
        $today = Carbon::today();
        $thisMonth = Carbon::now()->startOfMonth();
        $lastMonth = Carbon::now()->subMonth()->startOfMonth();
        $lastMonthEnd = Carbon::now()->subMonth()->endOfMonth();

        return [
            // Referral stats
            'total_referrals' => $affiliate->referrals()->count(),
            'pending_referrals' => $affiliate->referrals()->where('status', AffiliateReferral::STATUS_PENDING)->count(),
            'converted_referrals' => $affiliate->referrals()->where('status', AffiliateReferral::STATUS_CONVERTED)->count(),
            'conversion_rate' => $affiliate->conversion_rate,

            // Earnings stats
            'total_earnings' => $affiliate->total_earnings,
            'available_balance' => $affiliate->available_balance,
            'pending_balance' => $affiliate->pending_balance,
            'withdrawn_amount' => $affiliate->withdrawn_amount,

            // Monthly stats
            'this_month_referrals' => $affiliate->referrals()
                ->where('registration_date', '>=', $thisMonth)
                ->count(),
            'this_month_earnings' => $affiliate->earnings()
                ->where('earned_at', '>=', $thisMonth)
                ->where('status', '!=', AffiliateEarning::STATUS_REJECTED)
                ->sum('amount'),
            'last_month_earnings' => $affiliate->earnings()
                ->whereBetween('earned_at', [$lastMonth, $lastMonthEnd])
                ->where('status', '!=', AffiliateEarning::STATUS_REJECTED)
                ->sum('amount'),

            // Recent activity
            'recent_referrals_count' => $affiliate->referrals()
                ->where('registration_date', '>=', $today->subDays(7))
                ->count(),
            'recent_earnings_count' => $affiliate->earnings()
                ->where('earned_at', '>=', $today->subDays(7))
                ->count(),

            // Withdrawal stats
            'pending_withdrawals' => $affiliate->withdrawals()
                ->where('status', AffiliateWithdrawal::STATUS_PENDING)
                ->sum('amount'),
            'total_withdrawals' => $affiliate->withdrawals()
                ->where('status', AffiliateWithdrawal::STATUS_PAID)
                ->count(),
            'rejected_withdrawals' => $affiliate->withdrawals()
                ->where('status', AffiliateWithdrawal::STATUS_REJECTED)
                ->whereNotNull('rejection_reason')
                ->where('processed_at', '>=', Carbon::now()->subDays(30)) // Only show rejections from last 30 days
                ->latest('processed_at')
                ->take(3)
                ->get(),
        ];
    }

    /**
     * Get performance data for charts
     */
    protected function getPerformanceData(Affiliate $affiliate): array
    {
        $last12Months = collect();
        $startDate = Carbon::now()->subMonths(11)->startOfMonth();

        for ($i = 0; $i < 12; $i++) {
            $monthStart = $startDate->copy()->addMonths($i);
            $monthEnd = $monthStart->copy()->endOfMonth();

            $referrals = $affiliate->referrals()
                ->whereBetween('registration_date', [$monthStart, $monthEnd])
                ->count();

            $earnings = $affiliate->earnings()
                ->whereBetween('earned_at', [$monthStart, $monthEnd])
                ->where('status', '!=', AffiliateEarning::STATUS_REJECTED)
                ->sum('amount');

            $last12Months->push([
                'month' => $monthStart->format('M Y'),
                'referrals' => $referrals,
                'earnings' => (float) $earnings,
            ]);
        }

        return [
            'monthly_data' => $last12Months,
            'top_referrals' => $affiliate->referrals()
                ->with(['organization'])
                ->where('status', AffiliateReferral::STATUS_CONVERTED)
                ->orderBy('commission_earned', 'desc')
                ->take(5)
                ->get(),
        ];
    }



    /**
     * Show referral tools and links
     */
    public function referralTools(Request $request)
    {
        $affiliate = $request->affiliate ?? $this->getAffiliate();

        if (!$affiliate || !$affiliate->isActive()) {
            return redirect()->route('affiliate.dashboard');
        }

        // Generate different referral links
        $referralLinks = [
            'basic' => $this->referralTrackingService->generateReferralLink($affiliate),
            'social_media' => $this->referralTrackingService->generateReferralLink($affiliate, [
                'utm_source' => 'social',
                'utm_medium' => 'social_media',
                'utm_campaign' => 'affiliate_share'
            ]),
            'email' => $this->referralTrackingService->generateReferralLink($affiliate, [
                'utm_source' => 'email',
                'utm_medium' => 'email',
                'utm_campaign' => 'affiliate_email'
            ]),
            'website' => $this->referralTrackingService->generateReferralLink($affiliate, [
                'utm_source' => 'website',
                'utm_medium' => 'referral',
                'utm_campaign' => 'affiliate_website'
            ]),
        ];

        return view('affiliate.referral-tools', compact('affiliate', 'referralLinks'));
    }

    /**
     * Show earnings history
     */
    public function earnings(Request $request)
    {
        $affiliate = $request->affiliate ?? $this->getAffiliate();

        if (!$affiliate || !$affiliate->isActive()) {
            return redirect()->route('affiliate.dashboard');
        }

        $query = $affiliate->earnings()->with(['organization', 'referral']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->where('earned_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->where('earned_at', '<=', $request->date_to);
        }

        $earnings = $query->latest('earned_at')->paginate(20);

        // Get summary for the filtered results
        $summary = $this->commissionCalculationService->getAffiliateSummary($affiliate);

        return view('affiliate.earnings', compact('affiliate', 'earnings', 'summary'));
    }

    /**
     * Show referrals list
     */
    public function referrals(Request $request)
    {
        $affiliate = $request->affiliate ?? $this->getAffiliate();

        if (!$affiliate || !$affiliate->isActive()) {
            return redirect()->route('affiliate.dashboard');
        }

        $query = $affiliate->referrals()->with(['organization']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Search by organization name
        if ($request->filled('search')) {
            $query->whereHas('organization', function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%');
            });
        }

        $referrals = $query->latest('registration_date')->paginate(20);

        // Get referral stats
        $stats = $this->referralTrackingService->getReferralStats($affiliate);

        return view('affiliate.referrals', compact('affiliate', 'referrals', 'stats'));
    }

    /**
     * Get the current user's affiliate record
     */
    protected function getAffiliate(): ?Affiliate
    {
        return Affiliate::where('user_id', Auth::guard('affiliate')->id())->first();
    }
}
