<?php $__env->startSection('title', 'Join Affiliate Program - Sales Management System'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid vh-100">
    <div class="row h-100">
        <!-- Left Side - Branding -->
        <div class="col-lg-6 d-none d-lg-flex align-items-center justify-content-center bg-primary text-white">
            <div class="text-center">
                <div class="mb-4">
                    <i class="fas fa-star" style="font-size: 4rem;"></i>
                </div>
                <h2 class="display-4 fw-bold mb-3">Join Our Team!</h2>
                <p class="lead mb-4">
                    Become an affiliate partner and start earning commissions by referring
                    new customers to our platform.
                </p>
                <div class="row text-center">
                    <div class="col-4">
                        <div class="mb-2">
                            <i class="fas fa-percentage fa-2x"></i>
                        </div>
                        <small>High Commissions</small>
                    </div>
                    <div class="col-4">
                        <div class="mb-2">
                            <i class="fas fa-link fa-2x"></i>
                        </div>
                        <small>Unique Links</small>
                    </div>
                    <div class="col-4">
                        <div class="mb-2">
                            <i class="fas fa-money-bill-wave fa-2x"></i>
                        </div>
                        <small>Fast Payouts</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Side - Registration Form -->
        <div class="col-lg-6 d-flex align-items-center justify-content-center auth-form-side">
            <div class="w-100" style="max-width: 500px;">
                <div class="text-center mb-4">
                    <h3 class="fw-bold text-dark">Join Affiliate Program</h3>
                    <p class="text-muted">Start earning commissions today</p>
                </div>

                <?php if($referralData): ?>
                <div class="alert alert-info border-0 rounded-3 mb-4">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-info-circle me-2"></i>
                        <div>
                            <strong>Referral Detected!</strong> You were referred by an affiliate. Complete your registration to help them earn a commission.
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <?php if($errors->any()): ?>
                <div class="alert alert-danger border-0 rounded-3 mb-4">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <div>
                            <strong>Please fix the following errors:</strong>
                            <ul class="mb-0 mt-2">
                                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li><?php echo e($error); ?></li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <form method="POST" action="<?php echo e(route('affiliate.register')); ?>">
                    <?php echo csrf_field(); ?>

                    <!-- Personal Information Section -->
                    <div class="mb-4">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-user me-2"></i>
                            Personal Information
                        </h6>

                        <div class="form-floating mb-3">
                            <input type="text"
                                   class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   id="name"
                                   name="name"
                                   value="<?php echo e(old('name')); ?>"
                                   required
                                   autofocus
                                   placeholder="Enter your full name">
                            <label for="name">
                                <i class="fas fa-user me-2"></i>Full Name
                            </label>
                            <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="form-floating mb-3">
                            <input type="email"
                                   class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   id="email"
                                   name="email"
                                   value="<?php echo e(old('email')); ?>"
                                   required
                                   placeholder="Enter your email address">
                            <label for="email">
                                <i class="fas fa-envelope me-2"></i>Email Address
                            </label>
                            <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="form-floating mb-3">
                            <input type="tel"
                                   class="form-control <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   id="phone"
                                   name="phone"
                                   value="<?php echo e(old('phone')); ?>"
                                   required
                                   placeholder="Enter your phone number">
                            <label for="phone">
                                <i class="fas fa-phone me-2"></i>Phone Number
                            </label>
                            <input type="hidden" id="phone_full" name="phone_full" value="<?php echo e(old('phone_full')); ?>">
                            <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <?php $__errorArgs = ['phone_full'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3 position-relative">
                                    <input type="password"
                                           class="form-control <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           id="password"
                                           name="password"
                                           required
                                           placeholder="Enter password">
                                    <label for="password">
                                        <i class="fas fa-lock me-2"></i>Password
                                    </label>
                                    <button type="button" class="btn btn-link position-absolute end-0 top-50 translate-middle-y text-decoration-none password-toggle" style="z-index: 10; margin-right: 10px;" onclick="togglePasswordVisibility('password')">
                                        <i class="fas fa-eye-slash" id="password-toggle-icon"></i>
                                    </button>
                                    <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-floating mb-3 position-relative">
                                    <input type="password"
                                           class="form-control <?php $__errorArgs = ['password_confirmation'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           id="password_confirmation"
                                           name="password_confirmation"
                                           required
                                           placeholder="Confirm password">
                                    <label for="password_confirmation">
                                        <i class="fas fa-lock me-2"></i>Confirm Password
                                    </label>
                                    <button type="button" class="btn btn-link position-absolute end-0 top-50 translate-middle-y text-decoration-none password-toggle" style="z-index: 10; margin-right: 10px;" onclick="togglePasswordVisibility('password_confirmation')">
                                        <i class="fas fa-eye-slash" id="password_confirmation-toggle-icon"></i>
                                    </button>
                                    <?php $__errorArgs = ['password_confirmation'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
        </div>
    </div>

    <!-- Additional Information -->
    <div class="mb-4">
        <h5 class="text-primary mb-3">
            <i class="fas fa-info-circle me-2"></i>
            Additional Information (Optional)
        </h5>

        <div class="mb-3">
            <label for="website" class="form-label">Website URL</label>
            <input type="url"
                   class="form-control <?php $__errorArgs = ['website'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                   id="website"
                   name="website"
                   value="<?php echo e(old('website')); ?>"
                   placeholder="https://example.com">
            <?php $__errorArgs = ['website'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <div class="invalid-feedback"><?php echo e($message); ?></div>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>

        <div class="mb-3">
            <label for="social_media" class="form-label">Social Media Profile</label>
            <input type="text"
                   class="form-control <?php $__errorArgs = ['social_media'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                   id="social_media"
                   name="social_media"
                   value="<?php echo e(old('social_media')); ?>"
                   placeholder="e.g., @username or profile URL">
            <?php $__errorArgs = ['social_media'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <div class="invalid-feedback"><?php echo e($message); ?></div>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>

        <div class="mb-3">
            <label for="bio" class="form-label">Brief Bio</label>
            <textarea id="bio"
                      name="bio"
                      rows="3"
                      class="form-control <?php $__errorArgs = ['bio'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                      placeholder="Tell us about yourself and how you plan to promote our services"><?php echo e(old('bio')); ?></textarea>
            <?php $__errorArgs = ['bio'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <div class="invalid-feedback"><?php echo e($message); ?></div>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>
    </div>

    <!-- Payment Information -->
    <div class="mb-4">
        <h5 class="text-primary mb-3">
            <i class="fas fa-credit-card me-2"></i>
            Payment Information
        </h5>

        <div class="mb-3">
            <label for="payment_method" class="form-label">Payment Method</label>
            <input type="hidden" name="payment_method" value="bank_transfer">
            <div class="alert alert-info">
                <i class="fas fa-university me-2"></i>
                <strong>Bank Transfer</strong> - All affiliate payments are processed via bank transfer for security and compliance.
            </div>
        </div>

        <!-- Bank Transfer Fields -->
        <div id="bank_fields" class="d-none">
            <div class="mb-3">
                <label for="bank_name" class="form-label">Bank Name</label>
                <input type="text"
                       class="form-control <?php $__errorArgs = ['bank_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                       id="bank_name"
                       name="bank_name"
                       value="<?php echo e(old('bank_name')); ?>"
                       placeholder="Enter bank name">
                <?php $__errorArgs = ['bank_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <div class="mb-3">
                <label for="account_name" class="form-label">Account Holder Name</label>
                <input type="text"
                       class="form-control <?php $__errorArgs = ['account_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                       id="account_name"
                       name="account_name"
                       value="<?php echo e(old('account_name')); ?>"
                       placeholder="Enter account holder name">
                <?php $__errorArgs = ['account_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <div class="mb-3">
                <label for="account_number" class="form-label">Account Number</label>
                <input type="text"
                       class="form-control <?php $__errorArgs = ['account_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                       id="account_number"
                       name="account_number"
                       value="<?php echo e(old('account_number')); ?>"
                       placeholder="Enter account number">
                <?php $__errorArgs = ['account_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <div class="mb-3">
                <label for="routing_number" class="form-label">Routing Number (Optional)</label>
                <input type="text"
                       class="form-control <?php $__errorArgs = ['routing_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                       id="routing_number"
                       name="routing_number"
                       value="<?php echo e(old('routing_number')); ?>"
                       placeholder="Enter routing number">
                <?php $__errorArgs = ['routing_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
        </div>


    </div>

    <!-- Terms and Conditions -->
    <div class="mb-4">
        <div class="form-check">
            <input type="checkbox"
                   class="form-check-input <?php $__errorArgs = ['terms_accepted'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                   id="terms_accepted"
                   name="terms_accepted"
                   value="1"
                   <?php echo e(old('terms_accepted') ? 'checked' : ''); ?>

                   required>
            <label for="terms_accepted" class="form-check-label">
                I agree to the <a href="#" class="text-primary">Affiliate Program Terms and Conditions</a>
            </label>
            <?php $__errorArgs = ['terms_accepted'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <div class="invalid-feedback"><?php echo e($message); ?></div>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>
    </div>

    <!-- Commission Information -->
    <div class="alert alert-success mb-4">
        <h6 class="alert-heading">
            <i class="fas fa-percentage me-2"></i>Commission Structure
        </h6>
        <ul class="mb-0">
            <li>Earn <?php echo e(number_format($settings->default_commission_rate, 1)); ?>% commission on each successful referral</li>
            <li>Minimum withdrawal amount: $<?php echo e(number_format($settings->minimum_withdrawal, 2)); ?></li>
            <li>Monthly payouts for approved earnings</li>
            <li>Real-time tracking of your referrals and earnings</li>
        </ul>
    </div>

                    <!-- Submit Button -->
                    <div class="d-grid mb-4">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-user-plus me-2"></i>Join Affiliate Program
                        </button>
                    </div>
                </form>

                <!-- Additional Info -->
                <div class="text-center mt-4 pt-4 border-top">
                    <p class="text-muted mb-2">
                        Already have an account?
                        <a href="<?php echo e(route('affiliate.login')); ?>" class="text-decoration-none">
                            <strong>Sign in here</strong>
                        </a>
                    </p>
                    <p class="text-muted small mb-0">
                        <a href="<?php echo e(route('organization.login')); ?>" class="text-decoration-none">
                            <i class="fas fa-arrow-left me-1"></i>
                            Back to organization login
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('styles'); ?>
<style>
    /* Custom styles for intl-tel-input to match Bootstrap form design */
    .iti {
        width: 100%;
    }
    .iti__flag-container {
        display: flex;
    }
    .iti__selected-flag {
        border-radius: 0.375rem 0 0 0.375rem;
        background-color: #fff;
        border: 1px solid #ced4da;
        border-right: none;
        height: calc(1.5em + 0.75rem + 2px);
    }
    .iti--allow-dropdown input {
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
    }
    .iti__country-list {
        z-index: 1050;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize international telephone input
    const phoneInputField = document.querySelector("#phone");
    const phoneInput = window.intlTelInput(phoneInputField, {
        utilsScript: "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/js/utils.js",
        initialCountry: "auto",
        geoIpLookup: function(callback) {
            fetch("https://ipapi.co/json")
              .then(function(res) { return res.json(); })
              .then(function(data) { callback(data.country_code); })
              .catch(function() { callback("us"); });
        },
        preferredCountries: ["us", "gb", "ca", "ng"],
        separateDialCode: true,
        formatOnDisplay: true,
    });

    // Show bank fields since bank transfer is the only payment method
    const bankFields = document.getElementById('bank_fields');
    if (bankFields) {
        bankFields.classList.remove('d-none');
    }

    // Store the full number with country code when submitting the form
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function() {
            const fullNumber = phoneInput.getNumber();
            document.getElementById('phone_full').value = fullNumber;
        });
    }
});

// Password toggle functionality
function togglePasswordVisibility(inputId) {
    const passwordInput = document.getElementById(inputId);
    const toggleIcon = document.getElementById(inputId + '-toggle-icon');

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    } else {
        passwordInput.type = 'password';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    }
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('affiliate.layouts.auth', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\SalesManagementSystem\resources\views/affiliate/register.blade.php ENDPATH**/ ?>