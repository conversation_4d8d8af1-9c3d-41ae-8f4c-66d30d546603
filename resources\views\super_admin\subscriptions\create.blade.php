@extends('super_admin.layouts.app')

@section('title', 'Create Subscription')
@section('page-title', 'Create Subscription')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Create Subscription</h1>
            <p class="text-muted">Add a new subscription for an organization</p>
        </div>
        <a href="{{ route('super.subscriptions.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Subscriptions
        </a>
    </div>

    <!-- Create Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Subscription Details</h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('super.subscriptions.store') }}">
                        @csrf

                        <!-- Organization and Plan -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="organization_id" class="form-label">Organization *</label>
                                <select class="form-select @error('organization_id') is-invalid @enderror" 
                                        id="organization_id" name="organization_id" required>
                                    <option value="">Select Organization</option>
                                    @foreach($organizations as $organization)
                                        <option value="{{ $organization->id }}" {{ old('organization_id') == $organization->id ? 'selected' : '' }}>
                                            {{ $organization->name }} ({{ $organization->email }})
                                        </option>
                                    @endforeach
                                </select>
                                @error('organization_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="plan_id" class="form-label">Plan *</label>
                                <select class="form-select @error('plan_id') is-invalid @enderror" 
                                        id="plan_id" name="plan_id" required>
                                    <option value="">Select Plan</option>
                                    @foreach($plans as $plan)
                                        <option value="{{ $plan->id }}" 
                                                data-price="{{ $plan->price }}"
                                                {{ old('plan_id') == $plan->id ? 'selected' : '' }}>
                                            {{ $plan->name }} - ${{ number_format($plan->price, 2) }}/month
                                        </option>
                                    @endforeach
                                </select>
                                @error('plan_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Status and Dates -->
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <label for="status" class="form-label">Status *</label>
                                <select class="form-select @error('status') is-invalid @enderror" 
                                        id="status" name="status" required>
                                    <option value="active" {{ old('status', 'active') === 'active' ? 'selected' : '' }}>Active</option>
                                    <option value="trial" {{ old('status') === 'trial' ? 'selected' : '' }}>Trial</option>
                                    <option value="canceled" {{ old('status') === 'canceled' ? 'selected' : '' }}>Canceled</option>
                                    <option value="expired" {{ old('status') === 'expired' ? 'selected' : '' }}>Expired</option>
                                    <option value="past_due" {{ old('status') === 'past_due' ? 'selected' : '' }}>Past Due</option>
                                </select>
                                @error('status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-4">
                                <label for="start_date" class="form-label">Start Date *</label>
                                <input type="date" class="form-control @error('start_date') is-invalid @enderror" 
                                       id="start_date" name="start_date" value="{{ old('start_date', now()->format('Y-m-d')) }}" required>
                                @error('start_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-4">
                                <label for="end_date" class="form-label">End Date *</label>
                                <input type="date" class="form-control @error('end_date') is-invalid @enderror" 
                                       id="end_date" name="end_date" value="{{ old('end_date', now()->addMonth()->format('Y-m-d')) }}" required>
                                @error('end_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Trial and Payment -->
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <label for="trial_ends_at" class="form-label">Trial Ends At</label>
                                <input type="date" class="form-control @error('trial_ends_at') is-invalid @enderror" 
                                       id="trial_ends_at" name="trial_ends_at" value="{{ old('trial_ends_at') }}">
                                <small class="text-muted">Leave empty if not a trial</small>
                                @error('trial_ends_at')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-4">
                                <label for="payment_method" class="form-label">Payment Method</label>
                                <select class="form-select @error('payment_method') is-invalid @enderror" 
                                        id="payment_method" name="payment_method">
                                    <option value="">Select Payment Method</option>
                                    <option value="credit_card" {{ old('payment_method') === 'credit_card' ? 'selected' : '' }}>Credit Card</option>
                                    <option value="bank_transfer" {{ old('payment_method') === 'bank_transfer' ? 'selected' : '' }}>Bank Transfer</option>
                                    <option value="paypal" {{ old('payment_method') === 'paypal' ? 'selected' : '' }}>PayPal</option>
                                    <option value="stripe" {{ old('payment_method') === 'stripe' ? 'selected' : '' }}>Stripe</option>
                                    <option value="manual" {{ old('payment_method') === 'manual' ? 'selected' : '' }}>Manual</option>
                                </select>
                                @error('payment_method')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-4">
                                <label for="amount_paid" class="form-label">Amount Paid *</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" step="0.01" min="0" 
                                           class="form-control @error('amount_paid') is-invalid @enderror" 
                                           id="amount_paid" name="amount_paid" value="{{ old('amount_paid') }}" required>
                                </div>
                                @error('amount_paid')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Auto Renew -->
                        <div class="mb-4">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="auto_renew" 
                                       name="auto_renew" value="1" {{ old('auto_renew', true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="auto_renew">
                                    <strong>Auto Renew</strong>
                                    <br><small class="text-muted">Automatically renew this subscription when it expires</small>
                                </label>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('super.subscriptions.index') }}" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Create Subscription
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Preview Card -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Subscription Preview</h6>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <h5 id="preview-organization">Select Organization</h5>
                        <div class="h4 text-primary" id="preview-plan">Select Plan</div>
                        <div class="h2 text-success" id="preview-amount">$0.00</div>
                        <small class="text-muted">Amount to be paid</small>
                        <hr>
                        <div class="text-start">
                            <small class="text-muted">
                                <strong>Period:</strong><br>
                                <span id="preview-period">Select dates</span><br><br>
                                <strong>Status:</strong> <span id="preview-status">Active</span><br>
                                <strong>Auto Renew:</strong> <span id="preview-auto-renew">Yes</span><br>
                                <strong>Payment:</strong> <span id="preview-payment">Not set</span>
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="setTrialPeriod()">
                            <i class="fas fa-clock me-2"></i>Set 30-day Trial
                        </button>
                        <button type="button" class="btn btn-outline-success btn-sm" onclick="setMonthlyPeriod()">
                            <i class="fas fa-calendar me-2"></i>Set Monthly Period
                        </button>
                        <button type="button" class="btn btn-outline-warning btn-sm" onclick="setYearlyPeriod()">
                            <i class="fas fa-calendar-alt me-2"></i>Set Yearly Period
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const organizationSelect = document.getElementById('organization_id');
    const planSelect = document.getElementById('plan_id');
    const statusSelect = document.getElementById('status');
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');
    const amountInput = document.getElementById('amount_paid');
    const autoRenewInput = document.getElementById('auto_renew');
    const paymentMethodSelect = document.getElementById('payment_method');

    function updatePreview() {
        // Update organization
        const orgText = organizationSelect.options[organizationSelect.selectedIndex]?.text || 'Select Organization';
        document.getElementById('preview-organization').textContent = orgText.split(' (')[0];

        // Update plan and amount
        const planOption = planSelect.options[planSelect.selectedIndex];
        const planText = planOption?.text || 'Select Plan';
        const planPrice = planOption?.dataset.price || '0';
        
        document.getElementById('preview-plan').textContent = planText.split(' - ')[0];
        
        // Update amount (use input value or plan price)
        const amount = amountInput.value || planPrice;
        document.getElementById('preview-amount').textContent = '$' + parseFloat(amount || 0).toFixed(2);
        
        // Auto-fill amount if not set
        if (!amountInput.value && planPrice) {
            amountInput.value = planPrice;
        }

        // Update period
        const startDate = startDateInput.value;
        const endDate = endDateInput.value;
        if (startDate && endDate) {
            const start = new Date(startDate).toLocaleDateString();
            const end = new Date(endDate).toLocaleDateString();
            document.getElementById('preview-period').textContent = `${start} - ${end}`;
        }

        // Update status
        document.getElementById('preview-status').textContent = statusSelect.value ? statusSelect.options[statusSelect.selectedIndex].text : 'Active';

        // Update auto renew
        document.getElementById('preview-auto-renew').textContent = autoRenewInput.checked ? 'Yes' : 'No';

        // Update payment method
        const paymentText = paymentMethodSelect.value ? paymentMethodSelect.options[paymentMethodSelect.selectedIndex].text : 'Not set';
        document.getElementById('preview-payment').textContent = paymentText;
    }

    // Add event listeners
    [organizationSelect, planSelect, statusSelect, startDateInput, endDateInput, amountInput, autoRenewInput, paymentMethodSelect].forEach(element => {
        if (element) {
            element.addEventListener('change', updatePreview);
            element.addEventListener('input', updatePreview);
        }
    });

    // Initial update
    updatePreview();
});

// Quick action functions
function setTrialPeriod() {
    const today = new Date();
    const trialEnd = new Date(today);
    trialEnd.setDate(today.getDate() + 30);
    
    document.getElementById('start_date').value = today.toISOString().split('T')[0];
    document.getElementById('end_date').value = trialEnd.toISOString().split('T')[0];
    document.getElementById('trial_ends_at').value = trialEnd.toISOString().split('T')[0];
    document.getElementById('status').value = 'trial';
    document.getElementById('amount_paid').value = '0.00';
    
    // Trigger preview update
    document.getElementById('start_date').dispatchEvent(new Event('change'));
}

function setMonthlyPeriod() {
    const today = new Date();
    const nextMonth = new Date(today);
    nextMonth.setMonth(today.getMonth() + 1);
    
    document.getElementById('start_date').value = today.toISOString().split('T')[0];
    document.getElementById('end_date').value = nextMonth.toISOString().split('T')[0];
    document.getElementById('status').value = 'active';
    
    // Trigger preview update
    document.getElementById('start_date').dispatchEvent(new Event('change'));
}

function setYearlyPeriod() {
    const today = new Date();
    const nextYear = new Date(today);
    nextYear.setFullYear(today.getFullYear() + 1);
    
    document.getElementById('start_date').value = today.toISOString().split('T')[0];
    document.getElementById('end_date').value = nextYear.toISOString().split('T')[0];
    document.getElementById('status').value = 'active';
    
    // Trigger preview update
    document.getElementById('start_date').dispatchEvent(new Event('change'));
}
</script>
@endsection
