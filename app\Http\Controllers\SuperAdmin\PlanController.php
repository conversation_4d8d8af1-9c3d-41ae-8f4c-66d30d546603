<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\Plan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class PlanController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Plan::withCount(['organizations', 'subscriptions']);

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->has('status') && $request->status !== '') {
            $query->where('is_active', $request->status === 'active');
        }

        $plans = $query->orderBy('price')->paginate(15);

        return view('super_admin.plans.index', compact('plans'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('super_admin.plans.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'price' => 'required|numeric|min:0|max:999999.99',
            'annual_price' => 'nullable|numeric|min:0|max:999999.99',
            'billing_period' => 'required|string|in:monthly,annual,both',
            'annual_discount_percentage' => 'nullable|integer|min:0|max:50',
            'branch_limit' => 'required|integer|min:1|max:999',
            'user_limit' => 'required|integer|min:1|max:999',
            'order_limit' => 'nullable|integer|min:1',
            'data_retention_days' => 'required|integer|min:1|max:999',
            'thermal_printing' => 'boolean',
            'advanced_reporting' => 'boolean',
            'api_access' => 'boolean',
            'white_label' => 'boolean',
            'custom_branding' => 'boolean',
            'additional_features' => 'nullable|array',
            'is_featured' => 'boolean',
        ]);

        // Generate slug from name
        $validated['slug'] = Str::slug($validated['name']);

        // Ensure slug is unique
        $originalSlug = $validated['slug'];
        $counter = 1;
        while (Plan::where('slug', $validated['slug'])->exists()) {
            $validated['slug'] = $originalSlug . '-' . $counter;
            $counter++;
        }

        $validated['is_active'] = true;

        DB::beginTransaction();
        try {
            $plan = Plan::create($validated);

            DB::commit();
            return redirect()->route('super.plans.index')
                ->with('success', 'Plan created successfully.');
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'Failed to create plan: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Plan $plan)
    {
        $plan->load(['organizations', 'subscriptions']);

        // Get plan statistics
        $stats = [
            'total_organizations' => $plan->organizations()->count(),
            'active_organizations' => $plan->organizations()->where('is_active', true)->count(),
            'total_subscriptions' => $plan->subscriptions()->count(),
            'active_subscriptions' => $plan->subscriptions()->where('status', 'active')->count(),
            'monthly_revenue' => $plan->subscriptions()
                ->where('status', 'active')
                ->whereMonth('created_at', now()->month)
                ->sum('amount_paid'),
            'total_revenue' => $plan->subscriptions()->sum('amount_paid'),
        ];

        return view('super_admin.plans.show', compact('plan', 'stats'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Plan $plan)
    {
        return view('super_admin.plans.edit', compact('plan'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Plan $plan)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'price' => 'required|numeric|min:0|max:999999.99',
            'annual_price' => 'nullable|numeric|min:0|max:999999.99',
            'billing_period' => 'required|string|in:monthly,annual,both',
            'annual_discount_percentage' => 'nullable|integer|min:0|max:50',
            'branch_limit' => 'required|integer|min:1|max:999',
            'user_limit' => 'required|integer|min:1|max:999',
            'order_limit' => 'nullable|integer|min:1',
            'data_retention_days' => 'required|integer|min:1|max:999',
            'thermal_printing' => 'boolean',
            'advanced_reporting' => 'boolean',
            'api_access' => 'boolean',
            'white_label' => 'boolean',
            'custom_branding' => 'boolean',
            'additional_features' => 'nullable|array',
            'is_featured' => 'boolean',
        ]);

        // Update slug if name changed
        if ($validated['name'] !== $plan->name) {
            $newSlug = Str::slug($validated['name']);

            // Ensure slug is unique (excluding current plan)
            $originalSlug = $newSlug;
            $counter = 1;
            while (Plan::where('slug', $newSlug)->where('id', '!=', $plan->id)->exists()) {
                $newSlug = $originalSlug . '-' . $counter;
                $counter++;
            }

            $validated['slug'] = $newSlug;
        }

        DB::beginTransaction();
        try {
            $plan->update($validated);

            DB::commit();
            return redirect()->route('super.plans.show', $plan)
                ->with('success', 'Plan updated successfully.');
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'Failed to update plan: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Plan $plan)
    {
        // Check if plan has active subscriptions
        if ($plan->subscriptions()->where('status', 'active')->exists()) {
            return back()->withErrors(['error' => 'Cannot delete plan with active subscriptions.']);
        }

        DB::beginTransaction();
        try {
            $plan->delete();

            DB::commit();
            return redirect()->route('super.plans.index')
                ->with('success', 'Plan deleted successfully.');
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'Failed to delete plan: ' . $e->getMessage()]);
        }
    }

    /**
     * Activate a plan
     */
    public function activate(Plan $plan)
    {
        $plan->update(['is_active' => true]);
        return back()->with('success', 'Plan activated successfully.');
    }

    /**
     * Deactivate a plan
     */
    public function deactivate(Plan $plan)
    {
        $plan->update(['is_active' => false]);
        return back()->with('success', 'Plan deactivated successfully.');
    }
}
