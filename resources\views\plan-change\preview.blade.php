@extends('layouts.app')

@section('title', 'Plan Change Preview')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Plan Change Preview</h1>
            <p class="text-muted">Review your plan change before confirming</p>
        </div>
        <a href="{{ route('plan-change.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Plans
        </a>
    </div>

    <div class="row">
        <!-- Plan Comparison -->
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Plan Comparison</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Current Plan -->
                        <div class="col-md-6">
                            <div class="text-center">
                                <h5 class="text-muted">Current Plan</h5>
                                @if($currentPlan)
                                    <h4>{{ $currentPlan->name }}</h4>
                                    <div class="h3 text-muted">${{ number_format($currentPlan->price, 2) }}</div>
                                    <small class="text-muted">per month</small>

                                    <hr>
                                    <div class="text-start">
                                        <small>
                                            <strong>Current Features:</strong><br>
                                            • {{ $currentPlan->branch_limit == 999 ? 'Unlimited' : $currentPlan->branch_limit }} Branches<br>
                                            • {{ $currentPlan->user_limit == 999 ? 'Unlimited' : $currentPlan->user_limit }} Users<br>
                                            • {{ $currentPlan->order_limit === null ? 'Unlimited' : number_format($currentPlan->order_limit) }} Orders/month<br>
                                            • {{ $currentPlan->data_retention_days == 999 ? 'Forever' : $currentPlan->data_retention_days . ' Days' }} Data<br>
                                            @if($currentPlan->thermal_printing) • Thermal Printing<br> @endif
                                            @if($currentPlan->advanced_reporting) • Advanced Reporting<br> @endif
                                            @if($currentPlan->api_access) • API Access<br> @endif
                                            @if($currentPlan->white_label) • White Label<br> @endif
                                            @if($currentPlan->custom_branding) • Custom Branding<br> @endif
                                        </small>
                                    </div>
                                @else
                                    <h4 class="text-muted">No Current Plan</h4>
                                    <p class="text-muted">You don't have an active plan</p>
                                @endif
                            </div>
                        </div>

                        <!-- Arrow -->
                        <div class="col-md-12 d-md-none"><hr></div>
                        <div class="d-none d-md-flex col-md-12 justify-content-center align-items-center py-3">
                            <i class="fas fa-arrow-right fa-2x text-primary"></i>
                        </div>

                        <!-- New Plan -->
                        <div class="col-md-6">
                            <div class="text-center">
                                <h5 class="text-primary">New Plan</h5>
                                <h4>{{ $plan->name }}</h4>
                                @if($plan->is_featured)
                                    <span class="badge bg-warning text-dark mb-2">Featured</span>
                                @endif
                                <div class="h3 text-primary">${{ number_format($plan->price, 2) }}</div>
                                <small class="text-muted">per month</small>

                                <hr>
                                <div class="text-start">
                                    <small>
                                        <strong>New Features:</strong><br>
                                        • {{ $plan->branch_limit == 999 ? 'Unlimited' : $plan->branch_limit }} Branches<br>
                                        • {{ $plan->user_limit == 999 ? 'Unlimited' : $plan->user_limit }} Users<br>
                                        • {{ $plan->order_limit === null ? 'Unlimited' : number_format($plan->order_limit) }} Orders/month<br>
                                        • {{ $plan->data_retention_days == 999 ? 'Forever' : $plan->data_retention_days . ' Days' }} Data<br>
                                        @if($plan->thermal_printing) • Thermal Printing<br> @endif
                                        @if($plan->advanced_reporting) • Advanced Reporting<br> @endif
                                        @if($plan->api_access) • API Access<br> @endif
                                        @if($plan->white_label) • White Label<br> @endif
                                        @if($plan->custom_branding) • Custom Branding<br> @endif
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Billing Details -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Billing Details</h6>
                </div>
                <div class="card-body">
                    @if($proration['type'] === 'new_subscription')
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>New Subscription</h6>
                            <p class="mb-0">{{ $proration['proration_details'] }}</p>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <strong>Immediate Charge:</strong>
                            </div>
                            <div class="col-md-6 text-end">
                                <strong class="text-primary">${{ number_format($plan->price, 2) }}</strong>
                            </div>
                        </div>
                    @else
                        <div class="alert alert-info">
                            <h6><i class="fas fa-calculator me-2"></i>Proration Calculation</h6>
                            <p class="mb-0">{{ $proration['proration_details'] }}</p>
                        </div>

                        <div class="row mb-2">
                            <div class="col-md-8">
                                Credit for unused {{ $currentPlan->name }} ({{ $proration['remaining_days'] }} days):
                            </div>
                            <div class="col-md-4 text-end">
                                <span class="text-success">-${{ number_format($proration['current_plan_credit'], 2) }}</span>
                            </div>
                        </div>

                        <div class="row mb-2">
                            <div class="col-md-8">
                                Charge for {{ $plan->name }} ({{ $proration['remaining_days'] }} days):
                            </div>
                            <div class="col-md-4 text-end">
                                <span class="text-primary">+${{ number_format($proration['new_plan_charge'], 2) }}</span>
                            </div>
                        </div>

                        <hr>

                        <div class="row">
                            <div class="col-md-8">
                                <strong>
                                    @if($proration['net_amount'] > 0)
                                        Additional Charge:
                                    @elseif($proration['net_amount'] < 0)
                                        Credit Applied:
                                    @else
                                        Net Amount:
                                    @endif
                                </strong>
                            </div>
                            <div class="col-md-4 text-end">
                                <strong class="{{ $proration['net_amount'] > 0 ? 'text-primary' : ($proration['net_amount'] < 0 ? 'text-success' : 'text-muted') }}">
                                    @if($proration['net_amount'] > 0)
                                        ${{ number_format($proration['net_amount'], 2) }}
                                    @elseif($proration['net_amount'] < 0)
                                        ${{ number_format(abs($proration['net_amount']), 2) }}
                                    @else
                                        $0.00
                                    @endif
                                </strong>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Billing Period Selection -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Select Billing Period</h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('plan-change.change', $plan) }}" id="planChangeForm">
                        @csrf

                        <!-- Billing Period Dropdown -->
                        <div class="mb-4">
                            <label for="billing_period_months" class="form-label fw-bold">Billing Period *</label>
                            <select class="form-select form-select-lg" id="billing_period_months" name="billing_period_months" required onchange="updateOrderSummary()">
                                @foreach($plan->getAvailableBillingPeriods() as $months => $periodData)
                                    <option value="{{ $months }}"
                                            data-price="{{ $periodData['price'] }}"
                                            data-monthly-equivalent="{{ $periodData['monthly_equivalent'] }}"
                                            data-discount="{{ $periodData['discount_percentage'] }}"
                                            data-savings="{{ $periodData['savings'] }}"
                                            {{ $months == 1 ? 'selected' : '' }}>
                                        {{ $periodData['label'] }}
                                        @if($periodData['discount_percentage'] > 0)
                                            - Save {{ number_format($periodData['discount_percentage'], 1) }}%
                                        @endif
                                    </option>
                                @endforeach
                            </select>
                            <div class="form-text">Choose your preferred billing cycle. Longer periods offer better discounts.</div>
                        </div>

                        <!-- Order Summary -->
                        <div class="card bg-light border-0 mb-4">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0"><i class="fas fa-receipt me-2"></i>Order Summary</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label text-muted">Plan</label>
                                            <div class="fw-bold">{{ $plan->name }}</div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label text-muted">Billing Period</label>
                                            <div class="fw-bold" id="summary-period">1 Month</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label text-muted">Monthly Rate</label>
                                            <div class="fw-bold" id="summary-monthly-rate">${{ number_format($plan->price, 2) }}/month</div>
                                        </div>
                                        <div class="mb-3" id="discount-section" style="display: none;">
                                            <label class="form-label text-muted">Discount</label>
                                            <div class="fw-bold text-success" id="summary-discount">0%</div>
                                        </div>
                                    </div>
                                </div>

                                <hr>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-2">
                                            <span class="text-muted">Subtotal:</span>
                                            <span class="float-end" id="summary-subtotal">${{ number_format($plan->price, 2) }}</span>
                                        </div>
                                        <div class="mb-2" id="savings-section" style="display: none;">
                                            <span class="text-success">Savings:</span>
                                            <span class="float-end text-success" id="summary-savings">$0.00</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="border-top pt-2">
                                            <div class="h5 mb-0">
                                                <span class="text-muted">Total:</span>
                                                <span class="float-end fw-bold text-primary" id="summary-total">${{ number_format($plan->price, 2) }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>



                        <!-- Change Type Selection -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">When should this change take effect?</label>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="radio" name="change_type" id="immediate" value="immediate" checked>
                                <label class="form-check-label" for="immediate">
                                    <strong>Immediately</strong>
                                    <br><small class="text-muted">Change plan now with proration applied</small>
                                </label>
                            </div>
                            @if($activeSubscription)
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="change_type" id="end_of_cycle" value="end_of_cycle">
                                <label class="form-check-label" for="end_of_cycle">
                                    <strong>At end of current billing cycle</strong>
                                    <br><small class="text-muted">Change plan on {{ $activeSubscription->end_date->format('M j, Y') }} - no additional charge</small>
                                </label>
                            </div>
                            @endif
                        </div>

                        <hr>

                        <div class="alert alert-info">
                            <i class="fas fa-credit-card me-2"></i>
                            <strong>Payment Required:</strong> You will be redirected to the payment page to complete your plan change.
                            Your new plan will be activated once payment is approved by admin.
                        </div>

                        <div class="form-check mb-4">
                            <input type="checkbox" class="form-check-input" id="confirm" name="confirm" value="1" required>
                            <label class="form-check-label" for="confirm">
                                I understand the billing implications and want to proceed to payment
                            </label>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('plan-change.index') }}" class="btn btn-secondary btn-lg">
                                <i class="fas fa-arrow-left me-2"></i>Back to Plans
                            </a>
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-arrow-right me-2"></i>Proceed to Payment
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Summary Sidebar -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Change Summary</h6>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <h5>{{ $plan->name }}</h5>
                        @if($plan->description)
                            <p class="text-muted">{{ $plan->description }}</p>
                        @endif

                        <div class="h3 text-primary">${{ number_format($plan->price, 2) }}</div>
                        <small class="text-muted">per month</small>

                        <hr>

                        @if($proration['type'] === 'upgrade')
                            <div class="alert alert-success">
                                <i class="fas fa-arrow-up me-2"></i>
                                <strong>Upgrade</strong>
                                <br>You're getting more features!
                            </div>
                        @elseif($proration['type'] === 'downgrade')
                            <div class="alert alert-warning">
                                <i class="fas fa-arrow-down me-2"></i>
                                <strong>Downgrade</strong>
                                <br>Some features may be limited
                            </div>
                        @else
                            <div class="alert alert-info">
                                <i class="fas fa-exchange-alt me-2"></i>
                                <strong>Plan Change</strong>
                                <br>Different features at same price
                            </div>
                        @endif

                        @if($proration['net_amount'] > 0)
                            <div class="alert alert-primary">
                                <strong>Today's Charge:</strong><br>
                                ${{ number_format($proration['net_amount'], 2) }}
                            </div>
                        @elseif($proration['net_amount'] < 0)
                            <div class="alert alert-success">
                                <strong>Credit Applied:</strong><br>
                                ${{ number_format(abs($proration['net_amount']), 2) }}
                            </div>
                        @else
                            <div class="alert alert-info">
                                <strong>No Additional Charge</strong>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Support -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Need Help?</h6>
                </div>
                <div class="card-body">
                    <p class="text-muted">Have questions about this plan change?</p>
                    <div class="d-grid">
                        <button type="button" class="btn btn-outline-primary" onclick="alert('Support contact coming soon!')">
                            <i class="fas fa-headset me-2"></i>Contact Support
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function updateOrderSummary() {
    const select = document.getElementById('billing_period_months');
    const selectedOption = select.options[select.selectedIndex];

    const months = parseInt(selectedOption.value);
    const price = parseFloat(selectedOption.dataset.price);
    const monthlyEquivalent = parseFloat(selectedOption.dataset.monthlyEquivalent);
    const discount = parseFloat(selectedOption.dataset.discount);
    const savings = parseFloat(selectedOption.dataset.savings);

    // Update order summary
    document.getElementById('summary-period').textContent = selectedOption.text.split(' - ')[0];
    document.getElementById('summary-monthly-rate').textContent = `$${monthlyEquivalent.toFixed(2)}/month`;
    document.getElementById('summary-subtotal').textContent = `$${(monthlyEquivalent * months).toFixed(2)}`;
    document.getElementById('summary-total').textContent = `$${price.toFixed(2)}`;

    // Show/hide discount section
    const discountSection = document.getElementById('discount-section');
    const savingsSection = document.getElementById('savings-section');

    if (discount > 0) {
        discountSection.style.display = 'block';
        savingsSection.style.display = 'block';
        document.getElementById('summary-discount').textContent = `${discount.toFixed(1)}%`;
        document.getElementById('summary-savings').textContent = `$${savings.toFixed(2)}`;
    } else {
        discountSection.style.display = 'none';
        savingsSection.style.display = 'none';
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    updateOrderSummary();
});
</script>
@endsection
