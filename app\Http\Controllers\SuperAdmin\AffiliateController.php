<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\Affiliate;
use App\Models\AffiliateEarning;
use App\Models\AffiliateReferral;
use App\Models\AffiliateSetting;
use App\Services\CommissionCalculationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class AffiliateController extends Controller
{
    protected $commissionCalculationService;

    public function __construct(CommissionCalculationService $commissionCalculationService)
    {
        $this->commissionCalculationService = $commissionCalculationService;
        $this->middleware('super_admin');
    }

    /**
     * Display affiliate list
     */
    public function index(Request $request)
    {
        $query = Affiliate::with(['user', 'approvedBy']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Search by name or email
        if ($request->filled('search')) {
            $query->whereHas('user', function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%');
            });
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->where('joined_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->where('joined_at', '<=', $request->date_to);
        }

        $affiliates = $query->latest('joined_at')->paginate(20);

        // Get summary statistics
        $stats = [
            'total_affiliates' => Affiliate::count(),
            'active_affiliates' => Affiliate::where('status', Affiliate::STATUS_ACTIVE)->count(),
            'pending_affiliates' => Affiliate::where('status', Affiliate::STATUS_PENDING)->count(),
            'total_referrals' => AffiliateReferral::count(),
            'converted_referrals' => AffiliateReferral::where('status', AffiliateReferral::STATUS_CONVERTED)->count(),
            'total_earnings' => AffiliateEarning::sum('amount'),
            'pending_earnings' => AffiliateEarning::where('status', AffiliateEarning::STATUS_PENDING)->sum('amount'),
        ];

        return view('super_admin.affiliates.index', compact('affiliates', 'stats'));
    }

    /**
     * Show affiliate details
     */
    public function show(Affiliate $affiliate)
    {
        $affiliate->load([
            'user',
            'referrals.organization.activeSubscription.plan',
            'referrals.organization.plan',
            'referrals.organization.users',
            'earnings',
            'withdrawals',
            'approvedBy'
        ]);

        // Get affiliate statistics
        $stats = [
            'total_referrals' => $affiliate->referrals()->count(),
            'converted_referrals' => $affiliate->referrals()->where('status', AffiliateReferral::STATUS_CONVERTED)->count(),
            'pending_referrals' => $affiliate->referrals()->where('status', AffiliateReferral::STATUS_PENDING)->count(),
            'total_earnings' => $affiliate->total_earnings,
            'pending_earnings' => $affiliate->pending_balance,
            'available_balance' => $affiliate->available_balance,
            'withdrawn_amount' => $affiliate->withdrawn_amount,
            'conversion_rate' => $affiliate->conversion_rate,
        ];

        // Get recent activity
        $recentReferrals = $affiliate->referrals()
            ->with(['organization'])
            ->latest('registration_date')
            ->take(10)
            ->get();

        $recentEarnings = $affiliate->earnings()
            ->with(['organization'])
            ->latest('earned_at')
            ->take(10)
            ->get();

        return view('super_admin.affiliates.show', compact('affiliate', 'stats', 'recentReferrals', 'recentEarnings'));
    }

    /**
     * Show affiliate edit form
     */
    public function edit(Affiliate $affiliate)
    {
        return view('super_admin.affiliates.edit', compact('affiliate'));
    }

    /**
     * Update affiliate
     */
    public function update(Request $request, Affiliate $affiliate)
    {
        $request->validate([
            'status' => ['required', 'in:pending,active,inactive,suspended'],
            'commission_rate' => ['required', 'numeric', 'min:0', 'max:100'],
            'bio' => ['nullable', 'string', 'max:1000'],
            'website' => ['nullable', 'url', 'max:255'],
            'social_media' => ['nullable', 'string', 'max:255'],
        ]);

        try {
            $affiliate->update([
                'status' => $request->status,
                'commission_rate' => $request->commission_rate,
                'bio' => $request->bio,
                'website' => $request->website,
                'social_media' => $request->social_media,
            ]);

            return redirect()->route('super.affiliates.show', $affiliate)
                ->with('success', 'Affiliate updated successfully.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to update affiliate: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * Approve affiliate
     */
    public function approve(Affiliate $affiliate)
    {
        if ($affiliate->status !== Affiliate::STATUS_PENDING) {
            return back()->withErrors(['error' => 'Only pending affiliates can be approved.']);
        }

        try {
            $affiliate->approve(Auth::guard('super_admin')->id());

            // TODO: Send approval email to affiliate

            return back()->with('success', 'Affiliate approved successfully.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to approve affiliate: ' . $e->getMessage()]);
        }
    }

    /**
     * Reject affiliate
     */
    public function reject(Request $request, Affiliate $affiliate)
    {
        if ($affiliate->status !== Affiliate::STATUS_PENDING) {
            return back()->withErrors(['error' => 'Only pending affiliates can be rejected.']);
        }

        $request->validate([
            'rejection_reason' => ['required', 'string', 'max:500'],
        ]);

        try {
            $affiliate->update([
                'status' => Affiliate::STATUS_INACTIVE,
                'approved_by' => Auth::guard('super_admin')->id(),
                'approved_at' => now(),
            ]);

            // TODO: Send rejection email to affiliate with reason

            return back()->with('success', 'Affiliate rejected.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to reject affiliate: ' . $e->getMessage()]);
        }
    }

    /**
     * Suspend affiliate
     */
    public function suspend(Request $request, Affiliate $affiliate)
    {
        if ($affiliate->status !== Affiliate::STATUS_ACTIVE) {
            return back()->withErrors(['error' => 'Only active affiliates can be suspended.']);
        }

        $request->validate([
            'suspension_reason' => ['required', 'string', 'max:500'],
        ]);

        try {
            $affiliate->update([
                'status' => Affiliate::STATUS_SUSPENDED,
            ]);

            // TODO: Send suspension email to affiliate

            return back()->with('success', 'Affiliate suspended.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to suspend affiliate: ' . $e->getMessage()]);
        }
    }

    /**
     * Reactivate affiliate
     */
    public function reactivate(Affiliate $affiliate)
    {
        if (!in_array($affiliate->status, [Affiliate::STATUS_INACTIVE, Affiliate::STATUS_SUSPENDED])) {
            return back()->withErrors(['error' => 'Only inactive or suspended affiliates can be reactivated.']);
        }

        try {
            $affiliate->update([
                'status' => Affiliate::STATUS_ACTIVE,
            ]);

            // TODO: Send reactivation email to affiliate

            return back()->with('success', 'Affiliate reactivated.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to reactivate affiliate: ' . $e->getMessage()]);
        }
    }

    /**
     * Add bonus to affiliate
     */
    public function addBonus(Request $request, Affiliate $affiliate)
    {
        $request->validate([
            'amount' => ['required', 'numeric', 'min:0.01'],
            'description' => ['required', 'string', 'max:255'],
        ]);

        try {
            $this->commissionCalculationService->createBonus(
                $affiliate,
                $request->amount,
                $request->description,
                true // Auto-approve admin bonuses
            );

            return back()->with('success', 'Bonus added successfully.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to add bonus: ' . $e->getMessage()]);
        }
    }

    /**
     * Add adjustment to affiliate
     */
    public function addAdjustment(Request $request, Affiliate $affiliate)
    {
        $request->validate([
            'amount' => ['required', 'numeric', 'not_in:0'],
            'description' => ['required', 'string', 'max:255'],
        ]);

        try {
            $this->commissionCalculationService->createAdjustment(
                $affiliate,
                $request->amount,
                $request->description,
                Auth::guard('super_admin')->id()
            );

            $type = $request->amount > 0 ? 'adjustment' : 'penalty';
            return back()->with('success', ucfirst($type) . ' added successfully.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to add adjustment: ' . $e->getMessage()]);
        }
    }

    /**
     * Bulk actions for affiliates
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => ['required', 'in:approve,reject,suspend,reactivate'],
            'affiliate_ids' => ['required', 'array'],
            'affiliate_ids.*' => ['exists:affiliates,id'],
        ]);

        $affiliateIds = $request->affiliate_ids;
        $action = $request->action;
        $results = [];

        foreach ($affiliateIds as $affiliateId) {
            try {
                $affiliate = Affiliate::find($affiliateId);

                switch ($action) {
                    case 'approve':
                        if ($affiliate->status === Affiliate::STATUS_PENDING) {
                            $affiliate->approve(Auth::guard('super_admin')->id());
                            $results['success'][] = $affiliate->user->name;
                        } else {
                            $results['skipped'][] = $affiliate->user->name . ' (not pending)';
                        }
                        break;

                    case 'suspend':
                        if ($affiliate->status === Affiliate::STATUS_ACTIVE) {
                            $affiliate->update(['status' => Affiliate::STATUS_SUSPENDED]);
                            $results['success'][] = $affiliate->user->name;
                        } else {
                            $results['skipped'][] = $affiliate->user->name . ' (not active)';
                        }
                        break;

                    case 'reactivate':
                        if (in_array($affiliate->status, [Affiliate::STATUS_INACTIVE, Affiliate::STATUS_SUSPENDED])) {
                            $affiliate->update(['status' => Affiliate::STATUS_ACTIVE]);
                            $results['success'][] = $affiliate->user->name;
                        } else {
                            $results['skipped'][] = $affiliate->user->name . ' (already active)';
                        }
                        break;
                }
            } catch (\Exception $e) {
                $results['failed'][] = $affiliate->user->name . ' (' . $e->getMessage() . ')';
            }
        }

        $message = '';
        if (!empty($results['success'])) {
            $message .= 'Successfully processed: ' . implode(', ', $results['success']) . '. ';
        }
        if (!empty($results['skipped'])) {
            $message .= 'Skipped: ' . implode(', ', $results['skipped']) . '. ';
        }
        if (!empty($results['failed'])) {
            $message .= 'Failed: ' . implode(', ', $results['failed']) . '. ';
        }

        return back()->with('success', $message);
    }

    /**
     * Show affiliate settings page
     */
    public function settings()
    {
        $settings = AffiliateSetting::getInstance();

        // Get comprehensive statistics
        $stats = $settings->getSystemStats();

        // Add additional context for commission rate changes
        $stats['affected_affiliates_count'] = $settings->getAffectedAffiliatesCount($settings->default_commission_rate);

        return view('super_admin.affiliate_settings.index', compact('settings', 'stats'));
    }

    /**
     * Update affiliate settings
     */
    public function updateSettings(Request $request)
    {
        $request->validate([
            'default_commission_rate' => ['required', 'numeric', 'min:0', 'max:100'],
            'minimum_withdrawal' => ['required', 'numeric', 'min:0'],
            'payment_methods' => ['required', 'array', 'min:1'],
            'payment_methods.*' => ['string', 'in:bank_transfer,paypal,stripe,manual'],
            'auto_approve_earnings' => ['boolean'],
            'auto_approve_affiliates' => ['boolean'],
            'cookie_duration_days' => ['required', 'integer', 'min:1', 'max:365'],
            'withdrawal_fee_percentage' => ['required', 'numeric', 'min:0', 'max:100'],
            'withdrawal_fee_fixed' => ['required', 'numeric', 'min:0'],
            'program_active' => ['boolean'],
            'welcome_message' => ['nullable', 'string', 'max:1000'],
            'recurring_commissions' => ['boolean'],
            'max_referrals_per_affiliate' => ['nullable', 'integer', 'min:1'],
            'terms_and_conditions' => ['nullable', 'string'],
            'apply_rate_to_existing' => ['boolean'], // New field for applying to existing affiliates
        ]);

        try {
            DB::beginTransaction();

            $settings = AffiliateSetting::getInstance();
            $oldCommissionRate = $settings->default_commission_rate;
            $newCommissionRate = $request->default_commission_rate;

            // Update settings
            $settings->update([
                'default_commission_rate' => $newCommissionRate,
                'minimum_withdrawal' => $request->minimum_withdrawal,
                'payment_methods' => $request->payment_methods,
                'auto_approve_earnings' => $request->boolean('auto_approve_earnings'),
                'auto_approve_affiliates' => $request->boolean('auto_approve_affiliates'),
                'cookie_duration_days' => $request->cookie_duration_days,
                'withdrawal_fee_percentage' => $request->withdrawal_fee_percentage,
                'withdrawal_fee_fixed' => $request->withdrawal_fee_fixed,
                'program_active' => $request->boolean('program_active'),
                'welcome_message' => $request->welcome_message,
                'recurring_commissions' => $request->boolean('recurring_commissions'),
                'max_referrals_per_affiliate' => $request->max_referrals_per_affiliate,
                'terms_and_conditions' => $request->terms_and_conditions,
            ]);

            // Apply commission rate to existing affiliates if requested
            if ($request->boolean('apply_rate_to_existing') && $oldCommissionRate != $newCommissionRate) {
                $updatedCount = $settings->updateAffiliateCommissionRates($oldCommissionRate, $newCommissionRate);

                $message = "Settings updated successfully. Commission rate applied to {$updatedCount} existing affiliates.";
            } else {
                $message = "Settings updated successfully. New commission rate will apply to new affiliates only.";
            }

            DB::commit();

            return redirect()->route('super.affiliate_settings.index')
                ->with('success', $message);

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'Failed to update settings: ' . $e->getMessage()])->withInput();
        }
    }
}


