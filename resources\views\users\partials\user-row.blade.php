<td class="px-6 py-4 whitespace-nowrap">{{ $user->name }}</td>
<td class="px-6 py-4 whitespace-nowrap">{{ $user->email }}</td>
<td class="px-6 py-4 whitespace-nowrap">
    @foreach($user->roles as $role)
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mr-1 role-badge" data-role-id="{{ $role->id }}">
            {{ $role->name }}
        </span>
    @endforeach
</td>
<td class="px-6 py-4 whitespace-nowrap">
    @if($user->branch)
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            {{ $user->branch->name }}
        </span>
    @else
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            No Branch
        </span>
    @endif
</td>
<td class="px-6 py-4 whitespace-nowrap">
    @if($user->email !== '<EMAIL>')
        @if($user->status === 'active')
            <span class="inline-flex items-center px-2.5 py-1.5 border rounded-full text-xs font-medium status-badge border-green-500 text-green-600 bg-green-50" data-status="{{ $user->status }}">
                <span class="w-2 h-2 mr-1.5 rounded-full bg-green-500"></span>
                Active
            </span>
        @elseif($user->status === 'inactive')
            <span class="inline-flex items-center px-2.5 py-1.5 border rounded-full text-xs font-medium status-badge border-yellow-500 text-yellow-600 bg-yellow-50" data-status="{{ $user->status }}">
                <span class="w-2 h-2 mr-1.5 rounded-full bg-yellow-500"></span>
                Inactive
            </span>
        @elseif($user->status === 'archived')
            <span class="inline-flex items-center px-2.5 py-1.5 border rounded-full text-xs font-medium status-badge border-gray-500 text-gray-600 bg-gray-50" data-status="{{ $user->status }}">
                <span class="w-2 h-2 mr-1.5 rounded-full bg-gray-500"></span>
                Archived
            </span>
        @endif
    @else
        <span class="inline-flex items-center px-2.5 py-1.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            Always Active
        </span>
    @endif
</td>
<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
    <div class="flex space-x-2">
        <a href="{{ route('users.edit', $user->id) }}" class="text-blue-600 hover:text-blue-900 transition-colors duration-200">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
        </a>

        <a href="{{ route('profile.view.user', $user->id) }}" class="text-indigo-600 hover:text-indigo-900 transition-colors duration-200" title="View Profile">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
        </a>

        @if($user->email !== '<EMAIL>')
        <form action="{{ route('users.destroy', $user->id) }}" method="POST" class="inline" onsubmit="return confirmDelete('{{ $user->name }}')">
            @csrf
            @method('DELETE')
            <button type="submit" class="text-red-600 hover:text-red-900 transition-colors duration-200">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
            </button>
        </form>
        @endif
    </div>
</td>
