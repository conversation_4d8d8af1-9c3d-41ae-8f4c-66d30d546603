<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ExpenditureController;
use App\Http\Controllers\FinancialController;
use App\Http\Controllers\AccountController;
use App\Http\Controllers\SettingsController;
use App\Http\Controllers\LoginController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\DatabaseBackupController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\BranchController;
use App\Http\Controllers\OrganizationController;
use App\Http\Controllers\BillingController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\Auth\PasswordResetLinkController;
use App\Http\Controllers\Auth\NewPasswordController;
use App\Http\Controllers\Auth\EmailVerificationPromptController;
use App\Http\Controllers\Auth\VerifyEmailController;
use App\Http\Controllers\Auth\EmailVerificationNotificationController;
use App\Http\Controllers\PlanChangeController;

// Landing page routes (public)
Route::get('/', [App\Http\Controllers\LandingPageController::class, 'index'])->name('landing.home');
Route::get('/features', [App\Http\Controllers\LandingPageController::class, 'features'])->name('landing.features');
Route::get('/pricing', [App\Http\Controllers\LandingPageController::class, 'pricing'])->name('landing.pricing');
Route::get('/affiliate-program', [App\Http\Controllers\LandingPageController::class, 'affiliateProgram'])->name('landing.affiliate-program');
Route::get('/about', [App\Http\Controllers\LandingPageController::class, 'about'])->name('landing.about');
Route::get('/contact', [App\Http\Controllers\LandingPageController::class, 'contact'])->name('landing.contact');
Route::post('/contact', [App\Http\Controllers\LandingPageController::class, 'submitContact'])->name('landing.contact.submit');

// Organization authentication routes
Route::middleware('guest')->group(function () {
    Route::get('/organization/login', [App\Http\Controllers\OrganizationLoginController::class, 'showLoginForm'])->name('organization.login');
    Route::post('/organization/login', [LoginController::class, 'login'])->name('login.submit');

    // Registration routes
    Route::get('/register', [RegisterController::class, 'showRegistrationForm'])->name('register');
    Route::post('/register', [RegisterController::class, 'register'])->name('register.submit');

    // Password reset routes
    Route::get('/forgot-password', [PasswordResetLinkController::class, 'create'])->name('password.request');
    Route::post('/forgot-password', [PasswordResetLinkController::class, 'store'])->name('password.email');
    Route::get('/reset-password/{token}', [NewPasswordController::class, 'create'])->name('password.reset');
    Route::post('/reset-password', [NewPasswordController::class, 'store'])->name('password.store');

    // Legacy login route redirect
    Route::get('/login', function () {
        return redirect()->route('organization.login');
    })->name('login');
});

// Email verification routes
Route::middleware('auth')->group(function () {
    Route::get('/email/verify', [EmailVerificationPromptController::class, 'create'])
        ->name('verification.notice');
    Route::get('/email/verify/{id}/{hash}', [VerifyEmailController::class, 'verify'])
        ->middleware(['signed', 'throttle:6,1'])
        ->name('verification.verify');
    Route::post('/email/verification-notification', [EmailVerificationNotificationController::class, 'store'])
        ->middleware('throttle:6,1')
        ->name('verification.send');
});

// Protected routes
Route::middleware(['auth'])->group(function () {
    Route::post('/logout', [LoginController::class, 'logout'])->name('logout');
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Organization routes - Organization Owner only
    Route::middleware(['role:Organization Owner'])->group(function () {
        Route::get('/organization', [OrganizationController::class, 'show'])->name('organization.show');
        Route::get('/organization/edit', [OrganizationController::class, 'edit'])->name('organization.edit');
        Route::put('/organization', [OrganizationController::class, 'update'])->name('organization.update');
    });

    // Branch routes - Organization Owner only
    Route::middleware(['role:Organization Owner', 'organization'])->group(function () {
        Route::get('branches', [BranchController::class, 'index'])->name('branches.index');
        Route::get('branches/create', [BranchController::class, 'create'])->name('branches.create');
        Route::post('branches', [BranchController::class, 'store'])->middleware('plan_limit:branch')->name('branches.store');
        Route::get('branches/{branch}', [BranchController::class, 'show'])->name('branches.show');
        Route::get('branches/{branch}/edit', [BranchController::class, 'edit'])->name('branches.edit');
        Route::put('branches/{branch}', [BranchController::class, 'update'])->name('branches.update');
        Route::delete('branches/{branch}', [BranchController::class, 'destroy'])->name('branches.destroy');
        Route::post('/branches/switch', [BranchController::class, 'switchBranch'])->name('branches.switch');
    });

    // Organization Owner only routes
    Route::group(['middleware' => ['role:Organization Owner', 'web', 'organization']], function () {
        // User Management
        Route::get('/settings', [SettingsController::class, 'index'])->name('settings.index');
        Route::patch('/settings', [SettingsController::class, 'update'])->name('settings.update');

        // New user management endpoints for better control
        Route::post('/users/{user}/deactivate', [UserController::class, 'deactivate'])->name('users.deactivate');
        Route::post('/users/{user}/archive', [UserController::class, 'archive'])->name('users.archive');

        Route::delete('/users/{user}', [UserController::class, 'destroy'])->name('users.destroy'); // Add explicit delete route

        // User creation requires checking user limits
        Route::get('/users/create', [UserController::class, 'create'])
            ->name('users.create');
        Route::post('/users', [UserController::class, 'store'])
            ->name('users.store')
            ->middleware('plan_limit:user');

        Route::resource('users', UserController::class)->except(['show', 'destroy', 'create', 'store']); // Remove destroy, create, store from resource
        Route::patch('/users/{user}/toggle-status', [UserController::class, 'toggleStatus'])->name('users.toggle-status');

        // Database backup routes
        Route::post('/backup', [DatabaseBackupController::class, 'backup'])->name('database.backup');
        Route::get('/backups', [DatabaseBackupController::class, 'index'])->name('database.backups');
        Route::get('/backup/{filename}/download', [DatabaseBackupController::class, 'download'])->name('database.backup.download');
        Route::delete('/backup/{filename}', [DatabaseBackupController::class, 'delete'])->name('database.backup.delete');
    });

    // Financial routes - Organization Owner, Manager, Account roles
    Route::group(['middleware' => ['role:Organization Owner|Manager|Account', 'organization']], function () {
        Route::get('/financial/overview', [FinancialController::class, 'overview'])->name('financial.overview');
        Route::get('/account/summary', [AccountController::class, 'summary'])->name('account.summary');
    });

    // Expenditure routes with role-based access
    Route::middleware(['role:Organization Owner|Manager|Account', 'organization'])->group(function () {
        Route::get('/expenditures', [ExpenditureController::class, 'index'])->name('expenditures.index');
    });

    // Plan change routes - Organization Owner and Manager only
    Route::middleware(['role:Organization Owner|Manager', 'organization'])->group(function () {
        Route::get('/plan-change', [PlanChangeController::class, 'index'])->name('plan-change.index');
        Route::get('/plan-change/preview/{plan}', [PlanChangeController::class, 'preview'])->name('plan-change.preview');
        Route::post('/plan-change/{plan}', [PlanChangeController::class, 'change'])->name('plan-change.change');
        Route::get('/plan-change/payment/{plan}', [PlanChangeController::class, 'payment'])->name('plan-change.payment');
        Route::post('/plan-change/payment/{plan}', [PlanChangeController::class, 'processPayment'])->name('plan-change.process-payment');
        Route::post('/subscription/cancel', [PlanChangeController::class, 'cancel'])->name('subscription.cancel');
        Route::post('/subscription/reactivate', [PlanChangeController::class, 'reactivate'])->name('subscription.reactivate');
    });

    // Account-only routes
    Route::middleware(['role:Account', 'organization'])->group(function () {
        Route::get('/expenditures/create', [ExpenditureController::class, 'create'])->name('expenditures.create');
        Route::post('/expenditures', [ExpenditureController::class, 'store'])->name('expenditures.store');
    });

    // Manager and Organization Owner only routes
    Route::middleware(['role:Organization Owner|Manager', 'organization'])->group(function () {
        Route::patch('/expenditures/{expenditure}/update-status', [ExpenditureController::class, 'updateStatus'])
            ->name('expenditures.updateStatus');
    });

    // Order routes
    Route::middleware(['auth', 'organization'])->group(function () {
        // Create/View routes - Only Organization Owner and Staff for creation
        Route::middleware(['role:Organization Owner|Staff'])->group(function () {
            Route::get('orders/create', [OrderController::class, 'create'])->name('orders.create');
            Route::post('orders', [OrderController::class, 'store'])->middleware('plan_limit:order')->name('orders.store');
            Route::post('orders/preview', [OrderController::class, 'preview'])->name('orders.preview');
        });

        // Delivery update routes - Organization Owner, Staff and Delivery roles only
        Route::middleware(['role:Organization Owner|Staff|Delivery'])->group(function () {
            Route::patch('orders/{order}/delivery', [OrderController::class, 'updateDelivery'])->name('orders.updateDelivery');
        });

        // Payment update routes - Organization Owner, Staff and Account roles
        Route::middleware(['role:Organization Owner|Staff|Account'])->group(function () {
            Route::patch('orders/{order}/update-payment', [OrderController::class, 'updatePayment'])->name('orders.updatePayment');
        });

        // Status update routes - Organization Owner, Staff, Operator and Production roles
        Route::middleware(['role:Organization Owner|Staff|Operator|Production'])->group(function () {
            Route::patch('orders/{order}/status', [OrderController::class, 'updateStatus'])->name('orders.updateStatus');
        });

        // View-only routes for all authenticated roles
        Route::middleware(['role:Organization Owner|Manager|Account|Staff|Production|Delivery'])->group(function () {
            Route::get('orders', [OrderController::class, 'index'])->name('orders.index');
            Route::get('orders/pending', [OrderController::class, 'pendingOrders'])->name('orders.pending');
            Route::get('orders/overdue', [OrderController::class, 'overdue'])->name('orders.overdue');
            Route::get('orders/{order}', [OrderController::class, 'show'])->name('orders.show');
        });

        // Payment updates - accessible by all authenticated users
        Route::patch('orders/{order}/update-payment', [OrderController::class, 'updatePayment'])->name('orders.updatePayment');

        // Receipt routes
        Route::middleware(['auth'])->group(function () {
            Route::post('orders/receipt/multiple', [OrderController::class, 'generateReceiptForOrders'])
                ->name('orders.receipt.multiple');
            Route::get('orders/{order}/receipt/{format?}', [OrderController::class, 'generateReceipt'])
                ->name('orders.receipt');

            // Thermal printing requires plan feature
            Route::post('orders/{order}/print-thermal', [OrderController::class, 'printThermal'])
                ->name('orders.print-thermal')
                ->middleware('plan_feature:thermal_printing');
        });

        // Customer Directory routes
        Route::middleware(['auth'])->group(function () {
            Route::get('customers', [\App\Http\Controllers\CustomersController::class, 'index'])->name('customers.index');
            Route::get('customers/pdf', [\App\Http\Controllers\CustomersController::class, 'generatePDF'])->name('customers.pdf');
        });
    });

    // Notification routes - accessible by authenticated users
    Route::middleware('auth')->group(function () {
        Route::post('/notifications/{id}/mark-as-read', function ($id) {
            auth()->user()->notifications()->findOrFail($id)->markAsRead();
            return back();
        })->name('notifications.mark-as-read');

        Route::post('/notifications/mark-all-as-read', function () {
            auth()->user()->unreadNotifications->markAsRead();
            return back();
        })->name('notifications.mark-all-as-read');
    });

    // Settings routes
    Route::middleware(['auth', 'role:Organization Owner|Manager', 'organization'])->group(function () {
        // All settings in one page with tabs (default)
        Route::get('settings', [\App\Http\Controllers\SettingsController::class, 'allSettings'])->name('settings.index');
        Route::get('settings/all', [\App\Http\Controllers\SettingsController::class, 'allSettings'])->name('settings.all');

        // Individual settings pages (still available but not directly linked)
        Route::get('settings/app', [\App\Http\Controllers\SettingsController::class, 'index'])->name('settings.app');
        Route::match(['put', 'patch'], 'settings', [\App\Http\Controllers\SettingsController::class, 'update'])->name('settings.update');

        // Receipt settings
        Route::get('settings/receipt', [\App\Http\Controllers\SettingsController::class, 'receiptSettings'])->name('settings.receipt');
        Route::match(['put', 'patch'], 'settings/receipt', [\App\Http\Controllers\SettingsController::class, 'updateReceiptSettings'])->name('settings.receipt.update');

        // Business settings
        Route::get('settings/business', [\App\Http\Controllers\SettingsController::class, 'businessSettings'])->name('settings.business');
        Route::match(['put', 'patch'], 'settings/business', [\App\Http\Controllers\SettingsController::class, 'updateBusinessSettings'])->name('settings.business.update');

        // Notification settings
        Route::get('settings/notifications', [\App\Http\Controllers\SettingsController::class, 'notificationSettings'])->name('settings.notifications');
        Route::match(['put', 'patch'], 'settings/notifications', [\App\Http\Controllers\SettingsController::class, 'updateNotificationSettings'])->name('settings.notifications.update');

        // Printer settings
        Route::get('settings/printer', [\App\Http\Controllers\SettingsController::class, 'printerSettings'])->name('settings.printer');
        Route::match(['put', 'patch'], 'settings/printer', [\App\Http\Controllers\SettingsController::class, 'updatePrinterSettings'])->name('settings.printer.update');
        Route::post('settings/printer/test', [\App\Http\Controllers\SettingsController::class, 'testPrinter'])->name('settings.printer.test');
    });

    // Billing Routes
    Route::middleware(['auth', 'role:Organization Owner|Manager', 'organization'])->prefix('billing')->name('billing.')->group(function () {
        Route::get('/', [BillingController::class, 'index'])->name('index');
        Route::get('/create', [BillingController::class, 'create'])->name('create');
        Route::post('/', [BillingController::class, 'store'])->name('store');
        Route::get('/payment/{payment}', [BillingController::class, 'show'])->name('show');
        Route::get('/payment-accounts', [BillingController::class, 'paymentAccounts'])->name('payment-accounts');
        Route::get('/invoice/{payment}', [BillingController::class, 'generateInvoice'])->name('invoice');
        Route::get('/invoice/{payment}/download', [BillingController::class, 'downloadInvoice'])->name('invoice.download');
    });

    // Profile Routes
    Route::middleware('auth')->group(function () {
        Route::get('/profile', [ProfileController::class, 'show'])->name('profile.show');
        Route::get('/profile/user/{id}', [ProfileController::class, 'viewUser'])->name('profile.view.user');
        Route::put('/profile', [ProfileController::class, 'update'])->name('profile.update');
        Route::put('/profile/password', [ProfileController::class, 'updatePassword'])->name('profile.password.update');
        Route::put('/profile/photo', [ProfileController::class, 'updatePhoto'])->name('profile.photo.update');
        Route::delete('/profile/photo', [ProfileController::class, 'removePhoto'])->name('profile.photo.remove');
    });

    // User Support Routes
    Route::middleware('auth')->prefix('support')->name('user.support.')->group(function () {
        Route::get('/', [App\Http\Controllers\User\SupportController::class, 'index'])->name('index');
        Route::get('/create', [App\Http\Controllers\User\SupportController::class, 'create'])->name('create');
        Route::post('/', [App\Http\Controllers\User\SupportController::class, 'store'])->name('store');
        Route::get('/tickets/{ticket}', [App\Http\Controllers\User\SupportController::class, 'show'])->name('show');
        Route::post('/tickets/{ticket}/reply', [App\Http\Controllers\User\SupportController::class, 'reply'])->name('reply');

        // Knowledge Base Routes
        Route::get('/knowledge-base', [App\Http\Controllers\User\SupportController::class, 'knowledgeBase'])->name('knowledge-base');
        Route::get('/knowledge-base/search', [App\Http\Controllers\User\SupportController::class, 'search'])->name('search');
        Route::get('/knowledge-base/category/{category}', [App\Http\Controllers\User\SupportController::class, 'categoryArticles'])->name('category');
        Route::get('/knowledge-base/article/{article}', [App\Http\Controllers\User\SupportController::class, 'article'])->name('article');
        Route::post('/knowledge-base/article/{article}/rate', [App\Http\Controllers\User\SupportController::class, 'rateArticle'])->name('rate-article');
    });

    // Organization Support Routes (for Organization Admins)
    Route::middleware(['auth', 'role:Organization Owner|Manager'])->prefix('organization/support')->name('organization.support.')->group(function () {
        Route::get('/', [App\Http\Controllers\Organization\SupportController::class, 'index'])->name('index');
        Route::get('/create', [App\Http\Controllers\Organization\SupportController::class, 'create'])->name('create');
        Route::post('/', [App\Http\Controllers\Organization\SupportController::class, 'store'])->name('store');
        Route::get('/tickets/{ticket}', [App\Http\Controllers\Organization\SupportController::class, 'show'])->name('show');
        Route::post('/tickets/{ticket}/reply', [App\Http\Controllers\Organization\SupportController::class, 'reply'])->name('reply');
        Route::get('/communications', [App\Http\Controllers\Organization\SupportController::class, 'communications'])->name('communications');
        Route::get('/communications/{communication}', [App\Http\Controllers\Organization\SupportController::class, 'showCommunication'])->name('communication');
        Route::get('/analytics', [App\Http\Controllers\Organization\SupportController::class, 'analytics'])->name('analytics');
        Route::get('/export', [App\Http\Controllers\Organization\SupportController::class, 'export'])->name('export');
    });

    // User Announcements API routes
    Route::middleware('auth')->prefix('api/announcements')->name('api.announcements.')->group(function () {
        Route::get('/', [App\Http\Controllers\User\AnnouncementController::class, 'index'])->name('index');
        Route::get('/login', [App\Http\Controllers\User\AnnouncementController::class, 'forLogin'])->name('login');
        Route::post('/{announcement}/read', [App\Http\Controllers\User\AnnouncementController::class, 'markAsRead'])->name('read');
        Route::post('/{announcement}/dismiss', [App\Http\Controllers\User\AnnouncementController::class, 'dismiss'])->name('dismiss');
        Route::get('/maintenance-status', [App\Http\Controllers\User\AnnouncementController::class, 'maintenanceStatus'])->name('maintenance-status');
    });

    // Simple test route for announcements API
    Route::middleware('auth')->get('api/announcements-test', function () {
        return response()->json([
            'status' => 'working',
            'message' => 'Announcements API test route is working',
            'user' => auth()->user()->name ?? 'Unknown',
            'timestamp' => now()
        ]);
    });

    // Direct announcements route (alternative)
    Route::middleware('auth')->get('api/announcements-direct', [App\Http\Controllers\User\AnnouncementController::class, 'index']);

    // Test route for debugging announcements
    Route::get('/test-announcements', function () {
        $user = Auth::user();
        if (!$user) {
            return response()->json(['error' => 'Not authenticated']);
        }

        $audience = $user->organization_id ? 'organizations' : 'customers';

        $announcements = \App\Models\Announcement::active()
            ->published()
            ->current()
            ->forAudience($audience)
            ->forDashboard()
            ->get();

        return response()->json([
            'user_id' => $user->id,
            'user_organization_id' => $user->organization_id,
            'audience' => $audience,
            'announcements_count' => $announcements->count(),
            'announcements' => $announcements->toArray()
        ]);
    });
});

// Test route for user deletion
Route::post('/test-delete-user/{user}', function(\App\Models\User $user) {
    if ($user->email === '<EMAIL>') {
        return redirect()->back()->with('error', 'Cannot delete the organization owner user.');
    }

    try {
        \Illuminate\Support\Facades\DB::beginTransaction();
        $user->roles()->detach();
        $user->delete();
        \Illuminate\Support\Facades\DB::commit();

        return redirect()->route('users.index')->with('success', 'User deleted successfully.');
    } catch (\Exception $e) {
        \Illuminate\Support\Facades\DB::rollBack();
        return redirect()->back()->with('error', 'Failed to delete user: ' . $e->getMessage());
    }
})->name('test.delete.user')->middleware(['auth', 'role:Organization Owner']);

// Super Admin Routes
Route::prefix('super-admin')->name('super.')->group(function () {
    // Guest routes
    Route::middleware('guest:super_admin')->group(function () {
        Route::get('login', [App\Http\Controllers\Auth\SuperAdminLoginController::class, 'showLoginForm'])->name('login');
        Route::post('login', [App\Http\Controllers\Auth\SuperAdminLoginController::class, 'login'])->name('login.submit');
    });

    // Protected routes
    Route::middleware('super_admin')->group(function () {
        Route::get('dashboard', [App\Http\Controllers\SuperAdmin\DashboardController::class, 'index'])->name('dashboard');
        Route::post('logout', [App\Http\Controllers\Auth\SuperAdminLoginController::class, 'logout'])->name('logout');

        // Organizations management
        Route::resource('organizations', App\Http\Controllers\SuperAdmin\OrganizationController::class);

        // Subscription plans management
        Route::resource('plans', App\Http\Controllers\SuperAdmin\PlanController::class);

        // Subscriptions management
        Route::resource('subscriptions', App\Http\Controllers\SuperAdmin\SubscriptionController::class);

        // Organization management additional routes
        Route::post('organizations/{organization}/activate', [App\Http\Controllers\SuperAdmin\OrganizationController::class, 'activate'])->name('organizations.activate');
        Route::post('organizations/{organization}/deactivate', [App\Http\Controllers\SuperAdmin\OrganizationController::class, 'deactivate'])->name('organizations.deactivate');

        // Plan management additional routes
        Route::post('plans/{plan}/activate', [App\Http\Controllers\SuperAdmin\PlanController::class, 'activate'])->name('plans.activate');
        Route::post('plans/{plan}/deactivate', [App\Http\Controllers\SuperAdmin\PlanController::class, 'deactivate'])->name('plans.deactivate');

        // Subscription management additional routes
        Route::post('subscriptions/{subscription}/cancel', [App\Http\Controllers\SuperAdmin\SubscriptionController::class, 'cancel'])->name('subscriptions.cancel');
        Route::post('subscriptions/{subscription}/reactivate', [App\Http\Controllers\SuperAdmin\SubscriptionController::class, 'reactivate'])->name('subscriptions.reactivate');
        Route::post('subscriptions/{subscription}/extend', [App\Http\Controllers\SuperAdmin\SubscriptionController::class, 'extend'])->name('subscriptions.extend');

        // Payment Accounts management
        Route::resource('payment-accounts', App\Http\Controllers\SuperAdmin\PaymentAccountController::class);
        Route::post('payment-accounts/{paymentAccount}/toggle-status', [App\Http\Controllers\SuperAdmin\PaymentAccountController::class, 'toggleStatus'])->name('payment-accounts.toggle-status');
        Route::post('payment-accounts/{paymentAccount}/set-primary', [App\Http\Controllers\SuperAdmin\PaymentAccountController::class, 'setPrimary'])->name('payment-accounts.set-primary');

        // Subscription Payments management
        Route::resource('subscription-payments', App\Http\Controllers\SuperAdmin\SubscriptionPaymentController::class);
        Route::post('subscription-payments/{subscriptionPayment}/approve', [App\Http\Controllers\SuperAdmin\SubscriptionPaymentController::class, 'approve'])->name('subscription-payments.approve');
        Route::post('subscription-payments/{subscriptionPayment}/reject', [App\Http\Controllers\SuperAdmin\SubscriptionPaymentController::class, 'reject'])->name('subscription-payments.reject');

        // Affiliate Management
        Route::resource('affiliates', App\Http\Controllers\SuperAdmin\AffiliateController::class);
        Route::post('affiliates/{affiliate}/approve', [App\Http\Controllers\SuperAdmin\AffiliateController::class, 'approve'])->name('affiliates.approve');
        Route::post('affiliates/{affiliate}/reject', [App\Http\Controllers\SuperAdmin\AffiliateController::class, 'reject'])->name('affiliates.reject');
        Route::post('affiliates/{affiliate}/suspend', [App\Http\Controllers\SuperAdmin\AffiliateController::class, 'suspend'])->name('affiliates.suspend');
        Route::post('affiliates/{affiliate}/reactivate', [App\Http\Controllers\SuperAdmin\AffiliateController::class, 'reactivate'])->name('affiliates.reactivate');
        Route::post('affiliates/{affiliate}/add-bonus', [App\Http\Controllers\SuperAdmin\AffiliateController::class, 'addBonus'])->name('affiliates.add-bonus');
        Route::post('affiliates/{affiliate}/add-adjustment', [App\Http\Controllers\SuperAdmin\AffiliateController::class, 'addAdjustment'])->name('affiliates.add-adjustment');
        Route::post('affiliates/bulk-action', [App\Http\Controllers\SuperAdmin\AffiliateController::class, 'bulkAction'])->name('affiliates.bulk-action');

        // Affiliate Earnings
        Route::resource('affiliate-earnings', App\Http\Controllers\SuperAdmin\AffiliateEarningController::class);
        Route::post('affiliate-earnings/{affiliateEarning}/approve', [App\Http\Controllers\SuperAdmin\AffiliateEarningController::class, 'approve'])->name('affiliate-earnings.approve');
        Route::post('affiliate-earnings/{affiliateEarning}/reject', [App\Http\Controllers\SuperAdmin\AffiliateEarningController::class, 'reject'])->name('affiliate-earnings.reject');
        Route::post('affiliate-earnings/bulk-approve', [App\Http\Controllers\SuperAdmin\AffiliateEarningController::class, 'bulkApprove'])->name('affiliate-earnings.bulk-approve');
        Route::post('affiliate-earnings/bulk-reject', [App\Http\Controllers\SuperAdmin\AffiliateEarningController::class, 'bulkReject'])->name('affiliate-earnings.bulk-reject');

        // Affiliate Withdrawals
        Route::resource('affiliate-withdrawals', App\Http\Controllers\SuperAdmin\AffiliateWithdrawalController::class);
        Route::post('affiliate-withdrawals/{withdrawal}/approve', [App\Http\Controllers\SuperAdmin\AffiliateWithdrawalController::class, 'approve'])->name('affiliate-withdrawals.approve');
        Route::post('affiliate-withdrawals/{withdrawal}/reject', [App\Http\Controllers\SuperAdmin\AffiliateWithdrawalController::class, 'reject'])->name('affiliate-withdrawals.reject');
        Route::post('affiliate-withdrawals/{withdrawal}/mark-as-paid', [App\Http\Controllers\SuperAdmin\AffiliateWithdrawalController::class, 'markAsPaid'])->name('affiliate-withdrawals.mark-as-paid');
        Route::post('affiliate-withdrawals/bulk-approve', [App\Http\Controllers\SuperAdmin\AffiliateWithdrawalController::class, 'bulkApprove'])->name('affiliate-withdrawals.bulk-approve');
        Route::post('affiliate-withdrawals/bulk-reject', [App\Http\Controllers\SuperAdmin\AffiliateWithdrawalController::class, 'bulkReject'])->name('affiliate-withdrawals.bulk-reject');
        Route::get('affiliate-withdrawals/export', [App\Http\Controllers\SuperAdmin\AffiliateWithdrawalController::class, 'export'])->name('affiliate-withdrawals.export');

        // Affiliate Settings
        Route::get('affiliate-settings', [App\Http\Controllers\SuperAdmin\AffiliateController::class, 'settings'])->name('affiliate_settings.index');
        Route::put('affiliate-settings', [App\Http\Controllers\SuperAdmin\AffiliateController::class, 'updateSettings'])->name('affiliate_settings.update');

        // User Impersonation
        Route::prefix('impersonation')->name('impersonation.')->group(function () {
            Route::get('/', [App\Http\Controllers\SuperAdmin\ImpersonationController::class, 'index'])->name('index');
            Route::get('/users/{user}', [App\Http\Controllers\SuperAdmin\ImpersonationController::class, 'show'])->name('show');
            Route::post('/start/{user}', [App\Http\Controllers\SuperAdmin\ImpersonationController::class, 'start'])->name('start');
            Route::get('/status', [App\Http\Controllers\SuperAdmin\ImpersonationController::class, 'status'])->name('status');
        });

        // System Logs
        Route::prefix('system-logs')->name('system-logs.')->group(function () {
            Route::get('/', [App\Http\Controllers\SuperAdmin\SystemLogController::class, 'index'])->name('index');
            Route::get('/{systemLog}', [App\Http\Controllers\SuperAdmin\SystemLogController::class, 'show'])->name('show');
            Route::get('/export/csv', [App\Http\Controllers\SuperAdmin\SystemLogController::class, 'export'])->name('export');
            Route::post('/cleanup', [App\Http\Controllers\SuperAdmin\SystemLogController::class, 'cleanup'])->name('cleanup');
            Route::get('/api/stats', [App\Http\Controllers\SuperAdmin\SystemLogController::class, 'stats'])->name('stats');
        });

        // Support Dashboard
        Route::prefix('support')->name('support.')->group(function () {
            Route::get('/', [App\Http\Controllers\SuperAdmin\SupportDashboardController::class, 'index'])->name('dashboard');
            Route::get('/metrics', [App\Http\Controllers\SuperAdmin\SupportDashboardController::class, 'metrics'])->name('metrics');
            Route::post('/quick-action', [App\Http\Controllers\SuperAdmin\SupportDashboardController::class, 'quickAction'])->name('dashboard.quick-action');

            // Support Tickets
            Route::prefix('tickets')->name('tickets.')->group(function () {
                Route::get('/', [App\Http\Controllers\SuperAdmin\SupportTicketController::class, 'index'])->name('index');
                Route::get('/create', [App\Http\Controllers\SuperAdmin\SupportTicketController::class, 'create'])->name('create');
                Route::post('/', [App\Http\Controllers\SuperAdmin\SupportTicketController::class, 'store'])->name('store');
                Route::get('/{ticket}', [App\Http\Controllers\SuperAdmin\SupportTicketController::class, 'show'])->name('show');
                Route::get('/{ticket}/edit', [App\Http\Controllers\SuperAdmin\SupportTicketController::class, 'edit'])->name('edit');
                Route::put('/{ticket}', [App\Http\Controllers\SuperAdmin\SupportTicketController::class, 'update'])->name('update');
                Route::post('/{ticket}/reply', [App\Http\Controllers\SuperAdmin\SupportTicketController::class, 'reply'])->name('reply');
                Route::post('/{ticket}/assign', [App\Http\Controllers\SuperAdmin\SupportTicketController::class, 'assign'])->name('assign');
                Route::post('/{ticket}/resolve', [App\Http\Controllers\SuperAdmin\SupportTicketController::class, 'resolve'])->name('resolve');
                Route::post('/{ticket}/close', [App\Http\Controllers\SuperAdmin\SupportTicketController::class, 'close'])->name('close');
                Route::post('/{ticket}/reopen', [App\Http\Controllers\SuperAdmin\SupportTicketController::class, 'reopen'])->name('reopen');
            });
        });

        // API endpoint for loading organization subscriptions
        Route::get('organizations/{organization}/subscriptions', function(App\Models\Organization $organization) {
            return response()->json([
                'organization' => $organization,
                'subscriptions' => $organization->subscriptions()->with('plan')->get()
            ]);
        })->name('organizations.subscriptions');

        // Knowledge Base Management routes
        Route::prefix('knowledge-base')->name('knowledge-base.')->group(function () {
            Route::get('/', [App\Http\Controllers\SuperAdmin\KnowledgeBaseController::class, 'index'])->name('index');
            Route::get('/create', [App\Http\Controllers\SuperAdmin\KnowledgeBaseController::class, 'create'])->name('create');
            Route::post('/', [App\Http\Controllers\SuperAdmin\KnowledgeBaseController::class, 'store'])->name('store');
            Route::get('/{article}', [App\Http\Controllers\SuperAdmin\KnowledgeBaseController::class, 'show'])->name('show');
            Route::get('/{article}/edit', [App\Http\Controllers\SuperAdmin\KnowledgeBaseController::class, 'edit'])->name('edit');
            Route::put('/{article}', [App\Http\Controllers\SuperAdmin\KnowledgeBaseController::class, 'update'])->name('update');
            Route::delete('/{article}', [App\Http\Controllers\SuperAdmin\KnowledgeBaseController::class, 'destroy'])->name('destroy');
            Route::post('/bulk-action', [App\Http\Controllers\SuperAdmin\KnowledgeBaseController::class, 'bulkAction'])->name('bulk-action');
            Route::get('/analytics', [App\Http\Controllers\SuperAdmin\KnowledgeBaseController::class, 'analytics'])->name('analytics');

            // Category management
            Route::prefix('categories')->name('categories.')->group(function () {
                Route::get('/', [App\Http\Controllers\SuperAdmin\KnowledgeBaseCategoryController::class, 'index'])->name('index');
                Route::get('/create', [App\Http\Controllers\SuperAdmin\KnowledgeBaseCategoryController::class, 'create'])->name('create');
                Route::post('/', [App\Http\Controllers\SuperAdmin\KnowledgeBaseCategoryController::class, 'store'])->name('store');
                Route::get('/{category}/edit', [App\Http\Controllers\SuperAdmin\KnowledgeBaseCategoryController::class, 'edit'])->name('edit');
                Route::put('/{category}', [App\Http\Controllers\SuperAdmin\KnowledgeBaseCategoryController::class, 'update'])->name('update');
                Route::delete('/{category}', [App\Http\Controllers\SuperAdmin\KnowledgeBaseCategoryController::class, 'destroy'])->name('destroy');
                Route::post('/reorder', [App\Http\Controllers\SuperAdmin\KnowledgeBaseCategoryController::class, 'reorder'])->name('reorder');
                Route::post('/{category}/toggle-active', [App\Http\Controllers\SuperAdmin\KnowledgeBaseCategoryController::class, 'toggleActive'])->name('toggle-active');
            });
        });

        // Announcements Management routes
        Route::prefix('announcements')->name('announcements.')->group(function () {
            Route::get('/', [App\Http\Controllers\SuperAdmin\AnnouncementController::class, 'index'])->name('index');
            Route::get('/create', [App\Http\Controllers\SuperAdmin\AnnouncementController::class, 'create'])->name('create');
            Route::post('/', [App\Http\Controllers\SuperAdmin\AnnouncementController::class, 'store'])->name('store');
            Route::get('/{announcement}', [App\Http\Controllers\SuperAdmin\AnnouncementController::class, 'show'])->name('show');
            Route::get('/{announcement}/edit', [App\Http\Controllers\SuperAdmin\AnnouncementController::class, 'edit'])->name('edit');
            Route::put('/{announcement}', [App\Http\Controllers\SuperAdmin\AnnouncementController::class, 'update'])->name('update');
            Route::delete('/{announcement}', [App\Http\Controllers\SuperAdmin\AnnouncementController::class, 'destroy'])->name('destroy');
            Route::post('/{announcement}/publish', [App\Http\Controllers\SuperAdmin\AnnouncementController::class, 'publish'])->name('publish');
            Route::post('/{announcement}/unpublish', [App\Http\Controllers\SuperAdmin\AnnouncementController::class, 'unpublish'])->name('unpublish');
        });

        // Test routes for support notifications
        Route::prefix('test')->name('test.')->group(function () {
            Route::get('/support-notifications', [App\Http\Controllers\Test\SupportNotificationTestController::class, 'index'])->name('support.notifications');
            Route::post('/support/admin-reply', [App\Http\Controllers\Test\SupportNotificationTestController::class, 'testAdminReply'])->name('support.admin-reply');
            Route::post('/support/user-reply', [App\Http\Controllers\Test\SupportNotificationTestController::class, 'testUserReply'])->name('support.user-reply');
            Route::post('/support/status-change', [App\Http\Controllers\Test\SupportNotificationTestController::class, 'testStatusChange'])->name('support.status-change');
            Route::get('/support/check-logs', [App\Http\Controllers\Test\SupportNotificationTestController::class, 'checkLogs'])->name('support.check-logs');
            Route::get('/support/check-database', [App\Http\Controllers\Test\SupportNotificationTestController::class, 'checkDatabase'])->name('support.check-database');
            Route::post('/support/clear-notifications', [App\Http\Controllers\Test\SupportNotificationTestController::class, 'clearNotifications'])->name('support.clear-notifications');
        });
    });
});

// Impersonation stop route (accessible during impersonation)
Route::post('/impersonation/stop', [App\Http\Controllers\SuperAdmin\ImpersonationController::class, 'stop'])
    ->name('impersonation.stop')
    ->middleware('auth');

// CSRF token refresh route
Route::get('/csrf-token', function () {
    return response()->json(['token' => csrf_token()]);
})->middleware('web');

// Quick fix route for affiliate earnings (temporary)
Route::get('/fix-affiliate-earnings', function () {
    if (!Auth::guard('super_admin')->check()) {
        return redirect()->route('super.login')->withErrors(['error' => 'Super admin access required.']);
    }

    try {
        $pendingEarnings = \App\Models\AffiliateEarning::where('status', 'pending')->get();
        $approved = 0;

        foreach ($pendingEarnings as $earning) {
            $earning->approve(Auth::guard('super_admin')->id(), 'Auto-approved via fix route');
            $approved++;
        }

        // Fix affiliate balances
        \Artisan::call('affiliate:fix-balances');

        return response()->json([
            'success' => true,
            'message' => "Approved {$approved} pending earnings and fixed affiliate balances.",
            'approved_count' => $approved
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage()
        ], 500);
    }
})->name('fix.affiliate.earnings');

// Quick fix route for affiliate withdrawn amounts (temporary)
Route::get('/fix-affiliate-withdrawn-amounts', function () {
    if (!Auth::guard('super_admin')->check()) {
        return redirect()->route('super.login')->withErrors(['error' => 'Super admin access required.']);
    }

    try {
        $affiliates = \App\Models\Affiliate::all();
        $fixedCount = 0;

        foreach ($affiliates as $affiliate) {
            // Calculate total paid withdrawals for this affiliate
            $totalPaidWithdrawals = \App\Models\AffiliateWithdrawal::where('affiliate_id', $affiliate->id)
                ->where('status', \App\Models\AffiliateWithdrawal::STATUS_PAID)
                ->sum('amount');

            // Check if the affiliate's withdrawn_amount matches
            if ($affiliate->withdrawn_amount != $totalPaidWithdrawals) {
                $oldAmount = $affiliate->withdrawn_amount;
                $affiliate->withdrawn_amount = $totalPaidWithdrawals;
                $affiliate->save();
                $fixedCount++;
            }
        }

        return response()->json([
            'success' => true,
            'message' => "Fixed {$fixedCount} affiliate withdrawn amounts.",
            'fixed_count' => $fixedCount
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage()
        ], 500);
    }
})->name('fix.affiliate.withdrawn.amounts');

// Alternative payment approval route (fallback for CSRF issues)
Route::post('/super-admin/payment-approve/{id}', function($id) {
    if (!Auth::guard('super_admin')->check()) {
        return redirect()->route('super.login')->withErrors(['error' => 'Please login first.']);
    }

    $payment = \App\Models\SubscriptionPayment::findOrFail($id);

    if ($payment->isApproved()) {
        return back()->withErrors(['error' => 'Payment is already approved.']);
    }

    try {
        DB::beginTransaction();

        $superAdmin = Auth::guard('super_admin')->user();
        $payment->approve($superAdmin);

        // Generate invoice number automatically when payment is approved
        if (!$payment->invoice_number) {
            $payment->update([
                'invoice_number' => $payment->generateInvoiceNumber(),
                'invoice_generated_at' => now(),
            ]);
        }

        // Update subscription status
        $subscription = $payment->subscription;
        if ($subscription) {
            $subscription->status = 'active';
            $subscription->save();
        }

        DB::commit();
        return back()->with('success', 'Payment approved successfully via alternative route.');
    } catch (\Exception $e) {
        DB::rollback();
        return back()->withErrors(['error' => 'Failed to approve payment: ' . $e->getMessage()]);
    }
})->name('super.payment.approve.alt');

// Include affiliate routes
require __DIR__.'/affiliate.php';













// Database fix route (temporary - for fixing announcement system)
Route::get('/fix-database', function () {
    try {
        // Database configuration from .env
        $host = config('database.connections.mysql.host');
        $port = config('database.connections.mysql.port');
        $database = config('database.connections.mysql.database');
        $username = config('database.connections.mysql.username');
        $password = config('database.connections.mysql.password');

        $dsn = "mysql:host={$host};port={$port};dbname={$database};charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]);

        $results = [];
        $results[] = "✓ Connected to database: {$database}";

        // Check and create sessions table
        $stmt = $pdo->query("SHOW TABLES LIKE 'sessions'");
        if ($stmt->rowCount() == 0) {
            $createSessionsTable = "
            CREATE TABLE `sessions` (
                `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
                `user_id` bigint unsigned DEFAULT NULL,
                `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                `user_agent` text COLLATE utf8mb4_unicode_ci,
                `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
                `last_activity` int NOT NULL,
                PRIMARY KEY (`id`),
                KEY `sessions_user_id_index` (`user_id`),
                KEY `sessions_last_activity_index` (`last_activity`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            ";
            $pdo->exec($createSessionsTable);
            $results[] = "✓ Sessions table created";
        } else {
            $results[] = "✓ Sessions table already exists";
        }

        // Check super_admins table and create default admin
        $stmt = $pdo->query("SHOW TABLES LIKE 'super_admins'");
        if ($stmt->rowCount() > 0) {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM super_admins");
            $result = $stmt->fetch();

            if ($result['count'] == 0) {
                $stmt = $pdo->prepare("
                    INSERT INTO super_admins (name, email, password, role, is_active, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, NOW(), NOW())
                ");

                $hashedPassword = password_hash('password', PASSWORD_DEFAULT);
                $stmt->execute([
                    'Super Admin',
                    '<EMAIL>',
                    $hashedPassword,
                    'super_admin',
                    1
                ]);
                $results[] = "✓ Default super admin created (<EMAIL> / password)";
            } else {
                $results[] = "✓ Super admin(s) already exist";
            }
        }

        // Check announcements and create test data
        $stmt = $pdo->query("SHOW TABLES LIKE 'announcements'");
        if ($stmt->rowCount() > 0) {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM announcements");
            $result = $stmt->fetch();

            if ($result['count'] == 0) {
                // Get super admin ID
                $stmt = $pdo->query("SELECT id FROM super_admins LIMIT 1");
                $superAdmin = $stmt->fetch();

                if ($superAdmin) {
                    $superAdminId = $superAdmin['id'];

                    $stmt = $pdo->prepare("
                        INSERT INTO announcements
                        (title, content, type, priority, target_audience, is_active, is_dismissible, show_on_login, show_on_dashboard, send_email, published_at, created_by, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), ?, NOW(), NOW())
                    ");

                    // Test announcement for organizations
                    $stmt->execute([
                        'Welcome to the System!',
                        'This is a test announcement for organization users. The announcement system is now working correctly.',
                        'info', 'normal', 'organizations',
                        1, 1, 0, 1, 0, $superAdminId
                    ]);

                    // Test announcement for all users
                    $stmt->execute([
                        'System Maintenance Notice',
                        'Please be aware that we may perform system maintenance during off-peak hours.',
                        'warning', 'high', 'all',
                        1, 1, 0, 1, 0, $superAdminId
                    ]);

                    $results[] = "✓ Test announcements created";
                }
            } else {
                $results[] = "✓ Announcements already exist (count: {$result['count']})";
            }
        }

        return response()->json([
            'success' => true,
            'message' => 'Database fix completed successfully!',
            'results' => $results,
            'next_steps' => [
                'Login to super admin: /super-admin/login',
                'Email: <EMAIL>',
                'Password: password',
                'Manage announcements: /super-admin/announcements',
                'Test with organization users on dashboard'
            ]
        ]);

    } catch (Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage()
        ], 500);
    }
})->name('fix.database');

// Simple test routes outside any middleware groups
Route::get('test-route', function () {
    return response()->json(['message' => 'Basic route working', 'timestamp' => now()]);
});

Route::get('test-auth-route', function () {
    if (!auth()->check()) {
        return response()->json(['error' => 'Not authenticated'], 401);
    }
    return response()->json([
        'message' => 'Auth route working',
        'user' => auth()->user()->name,
        'timestamp' => now()
    ]);
})->middleware('auth');

// Direct announcement route outside groups
Route::get('announcements-api', function () {
    if (!auth()->check()) {
        return response()->json(['error' => 'Not authenticated'], 401);
    }

    $user = auth()->user();
    $audience = $user->organization_id ? 'organizations' : 'customers';

    $announcements = \App\Models\Announcement::active()
        ->published()
        ->current()
        ->forAudience($audience)
        ->forDashboard()
        ->get();

    return response()->json([
        'announcements' => $announcements->map(function($announcement) {
            return [
                'id' => $announcement->id,
                'title' => $announcement->title,
                'content' => $announcement->content,
                'type' => $announcement->type,
                'priority' => $announcement->priority,
                'is_dismissible' => $announcement->is_dismissible,
                'alert_class' => $announcement->alert_class,
                'icon' => $announcement->icon,
                'affected_features' => $announcement->affected_features,
                'starts_at' => $announcement->starts_at,
                'ends_at' => $announcement->ends_at,
            ];
        })
    ]);
})->middleware('auth');

// Simple dismiss route
Route::post('announcements-dismiss/{announcement}', function(\App\Models\Announcement $announcement) {
    if (!auth()->check()) {
        return response()->json(['error' => 'Not authenticated'], 401);
    }

    $user = auth()->user();

    if (!$announcement->is_dismissible) {
        return response()->json(['error' => 'This announcement cannot be dismissed'], 400);
    }

    $announcement->markAsDismissedByUser($user->id);

    return response()->json(['success' => true, 'message' => 'Announcement dismissed']);
})->middleware('auth');

