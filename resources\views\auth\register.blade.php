@extends('layouts.organization-auth')

@section('title', 'Organization Registration - Sales Management System')

@section('content')
<div class="container-fluid vh-100">
    <div class="row h-100">
        <!-- Left Side - Branding -->
        <div class="col-lg-6 d-none d-lg-flex align-items-center justify-content-center bg-primary text-white">
            <div class="text-center">
                <div class="mb-4">
                    <i class="fas fa-rocket" style="font-size: 4rem;"></i>
                </div>
                <h2 class="display-4 fw-bold mb-3">Start Your Journey!</h2>
                <p class="lead mb-4">
                    Create your organization account and unlock powerful tools to manage orders,
                    track performance, and grow your business.
                </p>
                <div class="row text-center">
                    <div class="col-4">
                        <div class="mb-2">
                            <i class="fas fa-building fa-2x"></i>
                        </div>
                        <small>Multi-Branch</small>
                    </div>
                    <div class="col-4">
                        <div class="mb-2">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                        <small>Analytics</small>
                    </div>
                    <div class="col-4">
                        <div class="mb-2">
                            <i class="fas fa-shield-alt fa-2x"></i>
                        </div>
                        <small>Secure</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Side - Registration Form -->
        <div class="col-lg-6 d-flex align-items-center justify-content-center auth-form-side">
            <div class="w-100" style="max-width: 500px;">
                <div class="text-center mb-4">
                    <h3 class="fw-bold text-dark">Create Organization</h3>
                    <p class="text-muted">Fill in the details to get started</p>
                </div>
                @if ($errors->any())
                <div class="alert alert-danger border-0 rounded-3 mb-4">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <div>
                            <strong>Please fix the following errors:</strong>
                            <ul class="mb-0 mt-2">
                                @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                </div>
                @endif

                <form method="POST" action="{{ route('register.submit') }}" id="registration-form">
                    @csrf

                    <!-- Step indicator -->
                    <div class="d-flex justify-content-center mb-4">
                        <div class="step-indicator">
                            <div class="step active" id="step-1-indicator">
                                <div class="step-number">1</div>
                                <div class="step-text">Organization</div>
                            </div>
                            <div class="step-line"></div>
                            <div class="step" id="step-2-indicator">
                                <div class="step-number">2</div>
                                <div class="step-text">Branch</div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 1: Organization Details -->
                    <div id="step-1">
                        <div class="form-floating mb-4">
                            <input class="form-control @error('organization_name') is-invalid @enderror"
                                id="organization_name" type="text" name="organization_name"
                                value="{{ old('organization_name') }}" required autofocus />
                            <label for="organization_name"><i class="fas fa-building me-2"></i>Organization Name</label>
                            @error('organization_name')
                            <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-floating mb-4">
                            <input class="form-control @error('name') is-invalid @enderror"
                                id="name" type="text" name="name"
                                value="{{ old('name') }}" required />
                            <label for="name"><i class="fas fa-user me-2"></i>Your Name</label>
                            @error('name')
                            <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-floating mb-4">
                            <input class="form-control @error('email') is-invalid @enderror"
                                id="email" type="email" name="email"
                                value="{{ old('email') }}" required />
                            <label for="email"><i class="fas fa-envelope me-2"></i>Email Address</label>
                            @error('email')
                            <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="form-floating mb-3 mb-md-0 position-relative">
                                    <input class="form-control @error('password') is-invalid @enderror"
                                        id="password" type="password" name="password" required />
                                    <label for="password"><i class="fas fa-lock me-2"></i>Password</label>
                                    <button type="button" class="btn btn-link position-absolute end-0 top-50 translate-middle-y text-decoration-none" style="z-index: 10;" onclick="togglePasswordVisibility('password')">
                                        <i class="fas fa-eye-slash toggle-password" data-target="password"></i>
                                    </button>
                                    @error('password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating position-relative">
                                    <input class="form-control" id="password-confirm"
                                        type="password" name="password_confirmation" required />
                                    <label for="password-confirm"><i class="fas fa-check-circle me-2"></i>Confirm Password</label>
                                    <button type="button" class="btn btn-link position-absolute end-0 top-50 translate-middle-y text-decoration-none" style="z-index: 10;" onclick="togglePasswordVisibility('password-confirm')">
                                        <i class="fas fa-eye-slash toggle-password" data-target="password-confirm"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex align-items-center justify-content-between mt-4 mb-2">
                            <a class="small text-info" href="{{ route('login') }}">
                                <i class="fas fa-arrow-left me-1"></i>Already have an account?
                            </a>
                            <button type="button" class="btn btn-primary px-4" id="next-step-btn">
                                <i class="fas fa-arrow-right me-2"></i>Next
                            </button>
                        </div>
                    </div>

                    <!-- Step 2: Branch Details -->
                    <div id="step-2" style="display: none;">
                        <div class="mb-3">
                            <label for="branch_name" class="form-label">Branch Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('branch_name') is-invalid @enderror"
                                   id="branch_name" name="branch_name" value="{{ old('branch_name') }}" required>
                            @error('branch_name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="branch_email" class="form-label">Branch Email</label>
                            <input type="email" class="form-control @error('branch_email') is-invalid @enderror"
                                   id="branch_email" name="branch_email" value="{{ old('branch_email') }}">
                            @error('branch_email')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="branch_phone" class="form-label">Branch Phone</label>
                            <input type="tel" class="form-control @error('branch_phone') is-invalid @enderror"
                                   id="branch_phone" name="branch_phone" value="{{ old('branch_phone') }}">
                            <input type="hidden" name="branch_phone_full" id="branch_phone_full">
                            @error('branch_phone')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="branch_address" class="form-label">Branch Address</label>
                            <textarea class="form-control @error('branch_address') is-invalid @enderror"
                                      id="branch_address" name="branch_address" rows="3">{{ old('branch_address') }}</textarea>
                            @error('branch_address')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="branch_description" class="form-label">Description</label>
                            <textarea class="form-control @error('branch_description') is-invalid @enderror"
                                      id="branch_description" name="branch_description" rows="3">{{ old('branch_description') }}</textarea>
                            @error('branch_description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <input type="hidden" name="switch_to_branch" value="1">
                        <input type="hidden" name="redirect_to_dashboard" value="1">

                        <div class="d-flex align-items-center justify-content-between mt-4 mb-2">
                            <button type="button" class="btn btn-secondary px-4" id="prev-step-btn">
                                <i class="fas fa-arrow-left me-2"></i>Back
                            </button>
                            <button type="submit" class="btn btn-primary px-4">
                                <i class="fas fa-user-plus me-2"></i>Register
                            </button>
                        </div>
                    </div>
                </form>

                <!-- Additional Info -->
                <div class="text-center mt-4 pt-4 border-top">
                    <p class="text-muted mb-2">
                        <i class="fas fa-shield-alt me-1"></i>
                        By registering, you'll become the Organization Owner
                    </p>
                    <p class="text-muted small mb-0">
                        Already have an account?
                        <a href="{{ route('organization.login') }}" class="text-decoration-none">
                            <strong>Sign in here</strong>
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .step-indicator {
        display: flex;
        align-items: center;
        margin-bottom: 30px;
        justify-content: center;
    }

    .step {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
    }

    .step-number {
        background-color: #e9ecef;
        color: #6c757d;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .step.active .step-number {
        background-color: var(--primary-color);
        color: white;
        box-shadow: 0 0 0 4px rgba(var(--primary-color), 0.2);
    }

    .step-text {
        font-size: 0.875rem;
        color: #6c757d;
        font-weight: 500;
    }

    .step.active .step-text {
        color: var(--primary-color);
        font-weight: 600;
    }

    .step-line {
        width: 60px;
        height: 3px;
        background-color: #e9ecef;
        margin: 0 15px;
        border-radius: 2px;
        transition: all 0.3s ease;
        margin-bottom: 15px;
    }

    .step.active + .step-line {
        background-color: var(--primary-color);
    }

    .password-toggle {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
        color: #6c757d;
        transition: color 0.3s ease;
    }

    .password-toggle:hover {
        color: var(--primary-color);
    }

    .form-floating > .form-control {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .form-floating > .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(var(--primary-color), 0.25);
    }

    .btn {
        border-radius: 10px;
        padding: 12px 24px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-primary {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(var(--primary-color), 0.3);
    }

    /* Custom styles for intl-tel-input to match Bootstrap form design */
    .iti {
        width: 100%;
    }
    .iti__flag-container {
        display: flex;
    }
    .iti__selected-flag {
        border-radius: 0.375rem 0 0 0.375rem;
        background-color: #fff;
        border: 1px solid #ced4da;
        border-right: none;
        height: calc(3.5rem + 2px);
    }
    .iti--allow-dropdown input {
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
        height: calc(3.5rem + 2px);
    }
    .iti__country-list {
        z-index: 1050;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const step1 = document.getElementById('step-1');
        const step2 = document.getElementById('step-2');
        const step1Indicator = document.getElementById('step-1-indicator');
        const step2Indicator = document.getElementById('step-2-indicator');
        const nextStepBtn = document.getElementById('next-step-btn');
        const prevStepBtn = document.getElementById('prev-step-btn');

        nextStepBtn.addEventListener('click', function() {
            // Validate step 1 fields
            const orgName = document.getElementById('organization_name').value;
            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const passwordConfirm = document.getElementById('password-confirm').value;

            if (!orgName || !name || !email || !password || !passwordConfirm) {
                alert('Please fill in all required fields.');
                return;
            }

            if (password !== passwordConfirm) {
                alert('Passwords do not match.');
                return;
            }

            // Move to step 2
            step1.style.display = 'none';
            step2.style.display = 'block';
            step1Indicator.classList.remove('active');
            step2Indicator.classList.add('active');
        });

        prevStepBtn.addEventListener('click', function() {
            // Go back to step 1
            step2.style.display = 'none';
            step1.style.display = 'block';
            step2Indicator.classList.remove('active');
            step1Indicator.classList.add('active');
        });

        // Initialize international telephone input for branch phone
        const branchPhoneInputField = document.querySelector("#branch_phone");
        const branchPhoneInput = window.intlTelInput(branchPhoneInputField, {
            utilsScript: "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/js/utils.js",
            initialCountry: "auto",
            geoIpLookup: function(callback) {
                fetch("https://ipapi.co/json")
                  .then(function(res) { return res.json(); })
                  .then(function(data) { callback(data.country_code); })
                  .catch(function() { callback("us"); });
            },
            preferredCountries: ["ng", "us", "gb", "ca"],
            separateDialCode: true,
            formatOnDisplay: true,
        });

        // Store the full number with country code when submitting the form
        document.getElementById('registration-form').addEventListener('submit', function() {
            const fullNumber = branchPhoneInput.getNumber();
            document.getElementById('branch_phone_full').value = fullNumber;
        });
    });

    // Password toggle functionality
    function togglePasswordVisibility(inputId) {
        const passwordInput = document.getElementById(inputId);
        const toggleIcon = passwordInput.parentElement.querySelector('.toggle-password');

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.classList.remove('fa-eye-slash');
            toggleIcon.classList.add('fa-eye');
        } else {
            passwordInput.type = 'password';
            toggleIcon.classList.remove('fa-eye');
            toggleIcon.classList.add('fa-eye-slash');
        }
    }
</script>
@endsection
