@extends('super_admin.layouts.app')

@section('title', 'Edit Knowledge Base Category')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Edit Knowledge Base Category</h1>
                    <p class="text-muted mb-0">Update category settings and organization</p>
                </div>
                <div>
                    <a href="{{ route('super.knowledge-base.categories.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Categories
                    </a>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Category Details</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="{{ route('super.knowledge-base.categories.update', $category) }}">
                                @csrf
                                @method('PUT')
                                <div class="mb-3">
                                    <label for="name" class="form-label">Category Name <span class="text-danger">*</span></label>
                                    <input type="text" name="name" id="name" 
                                           class="form-control @error('name') is-invalid @enderror" 
                                           value="{{ old('name', $category->name) }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="description" class="form-label">Description</label>
                                    <textarea name="description" id="description" 
                                              class="form-control @error('description') is-invalid @enderror" 
                                              rows="3" placeholder="Brief description of this category">{{ old('description', $category->description) }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="icon" class="form-label">Icon</label>
                                            <div class="input-group">
                                                <span class="input-group-text">
                                                    <i id="iconPreview" class="{{ $category->icon ?: 'fas fa-folder' }}"></i>
                                                </span>
                                                <input type="text" name="icon" id="icon" 
                                                       class="form-control @error('icon') is-invalid @enderror" 
                                                       value="{{ old('icon', $category->icon ?: 'fas fa-folder') }}" 
                                                       placeholder="fas fa-folder">
                                            </div>
                                            @error('icon')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <div class="form-text">
                                                Use Font Awesome classes (e.g., fas fa-folder, fas fa-cog, fas fa-question-circle)
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="color" class="form-label">Color</label>
                                            <div class="input-group">
                                                <input type="color" name="color" id="color" 
                                                       class="form-control form-control-color @error('color') is-invalid @enderror" 
                                                       value="{{ old('color', $category->color ?: '#007bff') }}">
                                                <input type="text" class="form-control" id="colorText" 
                                                       value="{{ old('color', $category->color ?: '#007bff') }}" readonly>
                                            </div>
                                            @error('color')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="sort_order" class="form-label">Sort Order</label>
                                            <input type="number" name="sort_order" id="sort_order" 
                                                   class="form-control @error('sort_order') is-invalid @enderror" 
                                                   value="{{ old('sort_order', $category->sort_order) }}" min="0">
                                            @error('sort_order')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <div class="form-text">Lower numbers appear first</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Status</label>
                                            <div class="form-check">
                                                <input type="checkbox" name="is_active" id="is_active" 
                                                       class="form-check-input" value="1" 
                                                       {{ old('is_active', $category->is_active) ? 'checked' : '' }}>
                                                <label for="is_active" class="form-check-label">
                                                    Active
                                                </label>
                                                <div class="form-text">Only active categories are visible to users</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-end">
                                    <a href="{{ route('super.knowledge-base.categories.index') }}" class="btn btn-outline-secondary me-2">
                                        Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> Update Category
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- Preview -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">Preview</h6>
                        </div>
                        <div class="card-body text-center">
                            <div id="categoryPreview">
                                <i id="previewIcon" class="{{ $category->icon ?: 'fas fa-folder' }} fa-3x mb-3" 
                                   style="color: {{ $category->color ?: '#007bff' }};"></i>
                                <h5 id="previewName">{{ $category->name }}</h5>
                                <p id="previewDescription" class="text-muted">{{ $category->description ?: 'Category description will appear here' }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Category Statistics -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">Category Statistics</h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border-end">
                                        <h4 class="text-primary mb-0">{{ $category->articles_count ?? 0 }}</h4>
                                        <small class="text-muted">Total Articles</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <h4 class="text-success mb-0">{{ $category->published_articles_count ?? 0 }}</h4>
                                    <small class="text-muted">Published</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Category Info -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">Category Information</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-2">
                                <strong>Created:</strong><br>
                                <small class="text-muted">{{ $category->created_at->format('M d, Y \a\t H:i') }}</small>
                            </div>
                            <div class="mb-2">
                                <strong>Last Updated:</strong><br>
                                <small class="text-muted">{{ $category->updated_at->format('M d, Y \a\t H:i') }}</small>
                            </div>
                            <div class="mb-2">
                                <strong>Slug:</strong><br>
                                <code class="small">{{ $category->slug }}</code>
                            </div>
                            <div class="mb-2">
                                <strong>Status:</strong><br>
                                <span class="badge {{ $category->is_active ? 'bg-success' : 'bg-secondary' }}">
                                    {{ $category->is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Icon Examples -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">Popular Icons</h6>
                        </div>
                        <div class="card-body">
                            <div class="row g-2">
                                <div class="col-4 text-center">
                                    <button type="button" class="btn btn-outline-secondary btn-sm w-100" onclick="setIcon('fas fa-folder')">
                                        <i class="fas fa-folder"></i><br><small>Folder</small>
                                    </button>
                                </div>
                                <div class="col-4 text-center">
                                    <button type="button" class="btn btn-outline-secondary btn-sm w-100" onclick="setIcon('fas fa-cog')">
                                        <i class="fas fa-cog"></i><br><small>Settings</small>
                                    </button>
                                </div>
                                <div class="col-4 text-center">
                                    <button type="button" class="btn btn-outline-secondary btn-sm w-100" onclick="setIcon('fas fa-question-circle')">
                                        <i class="fas fa-question-circle"></i><br><small>FAQ</small>
                                    </button>
                                </div>
                                <div class="col-4 text-center">
                                    <button type="button" class="btn btn-outline-secondary btn-sm w-100" onclick="setIcon('fas fa-book')">
                                        <i class="fas fa-book"></i><br><small>Guide</small>
                                    </button>
                                </div>
                                <div class="col-4 text-center">
                                    <button type="button" class="btn btn-outline-secondary btn-sm w-100" onclick="setIcon('fas fa-tools')">
                                        <i class="fas fa-tools"></i><br><small>Tools</small>
                                    </button>
                                </div>
                                <div class="col-4 text-center">
                                    <button type="button" class="btn btn-outline-secondary btn-sm w-100" onclick="setIcon('fas fa-lightbulb')">
                                        <i class="fas fa-lightbulb"></i><br><small>Tips</small>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const nameInput = document.getElementById('name');
    const descriptionInput = document.getElementById('description');
    const iconInput = document.getElementById('icon');
    const colorInput = document.getElementById('color');
    const colorText = document.getElementById('colorText');
    
    const previewName = document.getElementById('previewName');
    const previewDescription = document.getElementById('previewDescription');
    const previewIcon = document.getElementById('previewIcon');
    const iconPreview = document.getElementById('iconPreview');

    // Update preview when inputs change
    nameInput.addEventListener('input', function() {
        previewName.textContent = this.value || 'Category Name';
    });

    descriptionInput.addEventListener('input', function() {
        previewDescription.textContent = this.value || 'Category description will appear here';
    });

    iconInput.addEventListener('input', function() {
        updateIconPreview(this.value);
    });

    colorInput.addEventListener('input', function() {
        colorText.value = this.value;
        previewIcon.style.color = this.value;
    });

    function updateIconPreview(iconClass) {
        // Remove all existing classes and add new ones
        iconPreview.className = iconClass || 'fas fa-folder';
        previewIcon.className = (iconClass || 'fas fa-folder') + ' fa-3x mb-3';
    }
});

function setIcon(iconClass) {
    document.getElementById('icon').value = iconClass;
    document.getElementById('iconPreview').className = iconClass;
    document.getElementById('previewIcon').className = iconClass + ' fa-3x mb-3';
}
</script>
@endsection
