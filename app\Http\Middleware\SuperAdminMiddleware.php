<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class SuperAdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string|null  $requiredRole
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handle(Request $request, Closure $next, $requiredRole = null): Response
    {
        // Check if user is authenticated as a super admin
        if (!Auth::guard('super_admin')->check()) {
            return redirect()->route('super.login')->with('error', 'You must be logged in as a Super Admin to access this page.');
        }

        // Check for specific role if provided
        if ($requiredRole && !Auth::guard('super_admin')->user()->hasRole($requiredRole)) {
            return redirect()->route('super.dashboard')->with('error', 'You do not have permission to access this resource.');
        }

        return $next($request);
    }
}
