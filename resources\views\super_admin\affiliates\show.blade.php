@extends('super_admin.layouts.app')

@section('title', 'Affiliate Details')
@section('page-title', 'Affiliate Details')

@section('content')
<!-- Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h2 mb-1">{{ $affiliate->user->name }}</h1>
        <p class="text-muted">Affiliate ID: {{ $affiliate->affiliate_code }}</p>
    </div>
    <div>
        <a href="{{ route('super.affiliates.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>
            Back to Affiliates
        </a>
        <a href="{{ route('super.affiliates.edit', $affiliate) }}" class="btn btn-primary">
            <i class="fas fa-edit me-2"></i>
            Edit Affiliate
        </a>
    </div>
</div>

<!-- Status Badge -->
<div class="mb-4">
    @if($affiliate->status === 'active')
        <span class="badge bg-success fs-6">
            <i class="fas fa-check-circle me-1"></i>
            Active
        </span>
    @elseif($affiliate->status === 'pending')
        <span class="badge bg-warning fs-6">
            <i class="fas fa-clock me-1"></i>
            Pending Approval
        </span>
    @elseif($affiliate->status === 'suspended')
        <span class="badge bg-danger fs-6">
            <i class="fas fa-ban me-1"></i>
            Suspended
        </span>
    @else
        <span class="badge bg-secondary fs-6">
            <i class="fas fa-times-circle me-1"></i>
            Inactive
        </span>
    @endif
</div>

<!-- Stats Cards -->
<div class="row mb-4">
    <!-- Total Referrals -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Referrals
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total_referrals'] }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Converted Referrals -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Converted Referrals
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['converted_referrals'] }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-handshake fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Total Earnings -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Total Earnings
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">${{ number_format($stats['total_earnings'], 2) }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Available Balance -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Available Balance
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">${{ number_format($stats['available_balance'], 2) }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-wallet fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Referred Organizations -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Referred Organizations</h6>
    </div>
    <div class="card-body">
        @if($affiliate->referrals->count() > 0)
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Organization</th>
                            <th>Status</th>
                            <th>Joined</th>
                            <th>Subscription</th>
                            <th>Earnings</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($affiliate->referrals as $referral)
                            @if($referral->organization)
                                <tr>
                                    <td>
                                        <strong>{{ $referral->organization->name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ $referral->organization->email ?? 'No email' }}</small>
                                    </td>
                                    <td>
                                        @if($referral->organization->is_active)
                                            <span class="badge bg-success">Active</span>
                                        @else
                                            <span class="badge bg-secondary">Inactive</span>
                                        @endif
                                    </td>
                                    <td>
                                        {{ $referral->registration_date->format('M j, Y') }}
                                    </td>
                                    <td>
                                        @if($referral->organization->activeSubscription)
                                            {{ $referral->organization->activeSubscription->plan->name ?? 'No plan' }}
                                            <br>
                                            <small class="text-muted">${{ number_format($referral->organization->activeSubscription->amount_paid ?? 0, 2) }}/month</small>
                                        @elseif($referral->organization->plan)
                                            {{ $referral->organization->plan->name }}
                                            <br>
                                            <small class="text-muted">${{ number_format($referral->organization->plan->price ?? 0, 2) }}/month</small>
                                        @else
                                            <span class="text-muted">No subscription</span>
                                        @endif
                                    </td>
                                    <td>
                                        ${{ number_format($referral->commission_earned ?? 0, 2) }}
                                    </td>
                                    <td>
                                        <a href="{{ route('super.organizations.show', $referral->organization) }}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                            @endif
                        @endforeach
                    </tbody>
                </table>
            </div>
            @if($affiliate->referrals->count() > 10)
                <div class="text-center mt-3">
                    <a href="#" class="btn btn-sm btn-outline-primary">View All Referrals</a>
                </div>
            @endif
        @else
            <p class="text-muted">No organizations have been referred by this affiliate yet.</p>
        @endif
    </div>
</div>

<!-- Main Content -->
<div class="row">
    <!-- Affiliate Information -->
    <div class="col-lg-8">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Affiliate Information</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-gray-800">Personal Details</h6>
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Name:</strong></td>
                                <td>{{ $affiliate->user->name }}</td>
                            </tr>
                            <tr>
                                <td><strong>Email:</strong></td>
                                <td>{{ $affiliate->user->email }}</td>
                            </tr>
                            <tr>
                                <td><strong>Phone:</strong></td>
                                <td>{{ $affiliate->user->phone ?? 'Not provided' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Joined:</strong></td>
                                <td>{{ $affiliate->created_at->format('M d, Y') }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-gray-800">Affiliate Details</h6>
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Affiliate Code:</strong></td>
                                <td><code>{{ $affiliate->affiliate_code }}</code></td>
                            </tr>
                            <tr>
                                <td><strong>Commission Rate:</strong></td>
                                <td>{{ number_format($affiliate->commission_rate, 1) }}%</td>
                            </tr>
                            <tr>
                                <td><strong>Status:</strong></td>
                                <td>
                                    @if($affiliate->status === 'active')
                                        <span class="badge bg-success">Active</span>
                                    @elseif($affiliate->status === 'pending')
                                        <span class="badge bg-warning">Pending</span>
                                    @elseif($affiliate->status === 'suspended')
                                        <span class="badge bg-danger">Suspended</span>
                                    @else
                                        <span class="badge bg-secondary">Inactive</span>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Website:</strong></td>
                                <td>
                                    @if($affiliate->website)
                                        <a href="{{ $affiliate->website }}" target="_blank">{{ $affiliate->website }}</a>
                                    @else
                                        Not provided
                                    @endif
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                @if($affiliate->bio)
                    <div class="mt-3">
                        <h6 class="text-gray-800">Bio</h6>
                        <p class="text-muted">{{ $affiliate->bio }}</p>
                    </div>
                @endif

                @if($affiliate->payment_details)
                    <div class="mt-3">
                        <h6 class="text-gray-800">Payment Details</h6>
                        @if($affiliate->payment_details['method'] === 'paypal')
                            <p><strong>PayPal Email:</strong> {{ $affiliate->payment_details['paypal_email'] }}</p>
                        @elseif($affiliate->payment_details['method'] === 'bank_transfer')
                            <p><strong>Bank:</strong> {{ $affiliate->payment_details['bank_name'] }}</p>
                            <p><strong>Account:</strong> {{ $affiliate->payment_details['account_number'] }}</p>
                            <p><strong>Account Name:</strong> {{ $affiliate->payment_details['account_name'] }}</p>
                        @endif
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Actions -->
    <div class="col-lg-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Actions</h6>
            </div>
            <div class="card-body">
                @if($affiliate->status === 'pending')
                    <form action="{{ route('super.affiliates.approve', $affiliate) }}" method="POST" class="d-inline">
                        @csrf
                        <button type="submit" class="btn btn-success btn-sm mb-2 w-100">
                            <i class="fas fa-check me-2"></i>
                            Approve Affiliate
                        </button>
                    </form>
                    <form action="{{ route('super.affiliates.reject', $affiliate) }}" method="POST" class="d-inline">
                        @csrf
                        <button type="submit" class="btn btn-danger btn-sm mb-2 w-100"
                                onclick="return confirm('Are you sure you want to reject this affiliate?')">
                            <i class="fas fa-times me-2"></i>
                            Reject Affiliate
                        </button>
                    </form>
                @elseif($affiliate->status === 'active')
                    <form action="{{ route('super.affiliates.suspend', $affiliate) }}" method="POST" class="d-inline">
                        @csrf
                        <button type="submit" class="btn btn-warning btn-sm mb-2 w-100"
                                onclick="return confirm('Are you sure you want to suspend this affiliate?')">
                            <i class="fas fa-ban me-2"></i>
                            Suspend Affiliate
                        </button>
                    </form>
                @elseif($affiliate->status === 'suspended' || $affiliate->status === 'inactive')
                    <form action="{{ route('super.affiliates.reactivate', $affiliate) }}" method="POST" class="d-inline">
                        @csrf
                        <button type="submit" class="btn btn-success btn-sm mb-2 w-100">
                            <i class="fas fa-play me-2"></i>
                            Reactivate Affiliate
                        </button>
                    </form>
                @endif

                <a href="{{ route('super.affiliates.edit', $affiliate) }}" class="btn btn-primary btn-sm mb-2 w-100">
                    <i class="fas fa-edit me-2"></i>
                    Edit Details
                </a>

                <button type="button" class="btn btn-info btn-sm mb-2 w-100" data-bs-toggle="modal" data-bs-target="#bonusModal">
                    <i class="fas fa-gift me-2"></i>
                    Add Bonus
                </button>

                <button type="button" class="btn btn-secondary btn-sm mb-2 w-100" data-bs-toggle="modal" data-bs-target="#adjustmentModal">
                    <i class="fas fa-calculator me-2"></i>
                    Add Adjustment
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Bonus Modal -->
<div class="modal fade" id="bonusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="{{ route('super.affiliates.add-bonus', $affiliate) }}" method="POST">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title">Add Bonus</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="bonus_amount" class="form-label">Bonus Amount ($)</label>
                        <input type="number" class="form-control" id="bonus_amount" name="amount" step="0.01" min="0" required>
                    </div>
                    <div class="mb-3">
                        <label for="bonus_description" class="form-label">Description</label>
                        <textarea class="form-control" id="bonus_description" name="description" rows="3" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Bonus</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Adjustment Modal -->
<div class="modal fade" id="adjustmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="{{ route('super.affiliates.add-adjustment', $affiliate) }}" method="POST">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title">Add Adjustment</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="adjustment_type" class="form-label">Type</label>
                        <select class="form-select" id="adjustment_type" name="type" required>
                            <option value="">Select type</option>
                            <option value="bonus">Bonus</option>
                            <option value="penalty">Penalty</option>
                            <option value="correction">Correction</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="adjustment_amount" class="form-label">Amount ($)</label>
                        <input type="number" class="form-control" id="adjustment_amount" name="amount" step="0.01" required>
                        <small class="form-text text-muted">Use negative values for deductions</small>
                    </div>
                    <div class="mb-3">
                        <label for="adjustment_description" class="form-label">Description</label>
                        <textarea class="form-control" id="adjustment_description" name="description" rows="3" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Adjustment</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

