@extends('super_admin.layouts.app')

@section('title', 'Support Tickets')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Support Tickets</h1>
                <div>
                    <a href="{{ route('super.support.dashboard') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                    </a>
                    <a href="{{ route('super.support.tickets.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create Ticket
                    </a>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card border-primary">
                        <div class="card-body text-center">
                            <h5 class="card-title text-primary">{{ $stats['total'] }}</h5>
                            <p class="card-text">Total Tickets</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-success">
                        <div class="card-body text-center">
                            <h5 class="card-title text-success">{{ $stats['open'] }}</h5>
                            <p class="card-text">Open Tickets</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-danger">
                        <div class="card-body text-center">
                            <h5 class="card-title text-danger">{{ $stats['urgent'] }}</h5>
                            <p class="card-text">Urgent Tickets</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-warning">
                        <div class="card-body text-center">
                            <h5 class="card-title text-warning">{{ $stats['unassigned'] }}</h5>
                            <p class="card-text">Unassigned</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-filter me-2"></i>
                        Filters
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ route('super.support.tickets.index') }}">
                        <div class="row">
                            <div class="col-md-2">
                                <label for="status" class="form-label">Status</label>
                                <select name="status" id="status" class="form-select">
                                    <option value="">All Statuses</option>
                                    @foreach($filterOptions['statuses'] as $value => $label)
                                        <option value="{{ $value }}" {{ request('status') == $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="priority" class="form-label">Priority</label>
                                <select name="priority" id="priority" class="form-select">
                                    <option value="">All Priorities</option>
                                    @foreach($filterOptions['priorities'] as $value => $label)
                                        <option value="{{ $value }}" {{ request('priority') == $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="category" class="form-label">Category</label>
                                <select name="category" id="category" class="form-select">
                                    <option value="">All Categories</option>
                                    @foreach($filterOptions['categories'] as $value => $label)
                                        <option value="{{ $value }}" {{ request('category') == $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="organization_id" class="form-label">Organization</label>
                                <select name="organization_id" id="organization_id" class="form-select">
                                    <option value="">All Organizations</option>
                                    @foreach($filterOptions['organizations'] as $id => $name)
                                        <option value="{{ $id }}" {{ request('organization_id') == $id ? 'selected' : '' }}>
                                            {{ $name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="assigned_to" class="form-label">Assigned To</label>
                                <select name="assigned_to" id="assigned_to" class="form-select">
                                    <option value="">All Admins</option>
                                    <option value="unassigned" {{ request('assigned_to') == 'unassigned' ? 'selected' : '' }}>
                                        Unassigned
                                    </option>
                                    @foreach($filterOptions['admins'] as $id => $name)
                                        <option value="{{ $id }}" {{ request('assigned_to') == $id ? 'selected' : '' }}>
                                            {{ $name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" name="search" id="search" class="form-control" 
                                       placeholder="Search tickets..." value="{{ request('search') }}">
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> Apply Filters
                                </button>
                                <a href="{{ route('super.support.tickets.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Clear Filters
                                </a>
                                <div class="form-check form-check-inline ms-3">
                                    <input class="form-check-input" type="checkbox" name="urgent_only" id="urgent_only" 
                                           value="1" {{ request('urgent_only') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="urgent_only">
                                        Urgent Only
                                    </label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Tickets Table -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-ticket-alt me-2"></i>
                        Support Tickets ({{ $tickets->total() }})
                    </h5>
                    <div>
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="window.location.reload()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    @if($tickets->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Ticket #</th>
                                        <th>Title</th>
                                        <th>Organization</th>
                                        <th>User</th>
                                        <th>Priority</th>
                                        <th>Status</th>
                                        <th>Category</th>
                                        <th>Assigned To</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($tickets as $ticket)
                                    <tr class="{{ $ticket->priority === 'urgent' || $ticket->priority === 'critical' ? 'table-danger' : '' }}">
                                        <td>
                                            <strong>{{ $ticket->ticket_number }}</strong>
                                        </td>
                                        <td>
                                            <div class="text-truncate" style="max-width: 200px;" title="{{ $ticket->title }}">
                                                {{ $ticket->title }}
                                            </div>
                                        </td>
                                        <td>{{ $ticket->organization->name ?? 'N/A' }}</td>
                                        <td>{{ $ticket->user->name ?? 'N/A' }}</td>
                                        <td>{!! $ticket->priority_badge !!}</td>
                                        <td>{!! $ticket->status_badge !!}</td>
                                        <td>
                                            <span class="badge bg-secondary">
                                                {{ ucfirst(str_replace('_', ' ', $ticket->category)) }}
                                            </span>
                                        </td>
                                        <td>
                                            @if($ticket->assignedAdmin)
                                                <span class="badge bg-info">{{ $ticket->assignedAdmin->name }}</span>
                                            @else
                                                <span class="badge bg-warning">Unassigned</span>
                                            @endif
                                        </td>
                                        <td>
                                            <small>
                                                {{ $ticket->created_at->format('M d, Y H:i') }}
                                                <br>
                                                <span class="text-muted">{{ $ticket->created_at->diffForHumans() }}</span>
                                            </small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('super.support.tickets.show', $ticket) }}" 
                                                   class="btn btn-sm btn-outline-info" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('super.support.tickets.edit', $ticket) }}" 
                                                   class="btn btn-sm btn-outline-primary" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                @if($ticket->status !== 'resolved' && $ticket->status !== 'closed')
                                                    <form method="POST" action="{{ route('super.support.tickets.resolve', $ticket) }}" 
                                                          class="d-inline" onsubmit="return confirm('Mark this ticket as resolved?')">
                                                        @csrf
                                                        <button type="submit" class="btn btn-sm btn-outline-success" title="Mark as Resolved">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                    </form>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="card-footer">
                            {{ $tickets->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-ticket-alt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No tickets found</h5>
                            <p class="text-muted">No support tickets match your current filters.</p>
                            <a href="{{ route('super.support.tickets.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Create First Ticket
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.table-danger {
    background-color: rgba(220, 53, 69, 0.1) !important;
}
</style>

<script>
// Auto-refresh every 2 minutes
setInterval(function() {
    if (!document.hidden) {
        window.location.reload();
    }
}, 120000);
</script>
@endsection
