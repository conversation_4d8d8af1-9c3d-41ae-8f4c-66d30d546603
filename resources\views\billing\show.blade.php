@extends('layouts.app')

@section('title', 'Payment Details')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Payment Details</h1>
                <div>
                    <a href="{{ route('billing.index') }}" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left"></i> Back to Billing
                    </a>
                    @if($payment->isApproved() && $payment->invoice_number)
                        <a href="{{ route('billing.invoice', $payment) }}" class="btn btn-outline-info me-2">
                            <i class="fas fa-file-invoice"></i> View Invoice
                        </a>
                        <a href="{{ route('billing.invoice.download', $payment) }}" class="btn btn-primary">
                            <i class="fas fa-download"></i> Download Invoice
                        </a>
                    @endif
                </div>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <!-- Payment Status Alert -->
            @if($payment->isPending())
                <div class="alert alert-warning">
                    <i class="fas fa-clock me-2"></i>
                    <strong>Payment Pending:</strong> Your payment is currently being reviewed by our team. You will be notified once it's processed.
                </div>
            @elseif($payment->isApproved())
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>Payment Approved:</strong> Your payment has been approved and processed successfully.
                    @if($payment->invoice_number)
                        <a href="{{ route('billing.invoice', $payment) }}" class="alert-link">View Invoice</a>
                    @endif
                </div>
            @elseif($payment->isRejected())
                <div class="alert alert-danger">
                    <i class="fas fa-times-circle me-2"></i>
                    <strong>Payment Rejected:</strong> Unfortunately, your payment was not approved. Please check the notes below for details.
                </div>
            @endif

            <div class="row">
                <div class="col-md-8">
                    <!-- Payment Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Payment Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Payment Reference:</strong></p>
                                    <p class="text-muted">{{ $payment->payment_reference ?: 'N/A' }}</p>

                                    <p><strong>Amount:</strong></p>
                                    <p class="text-muted">${{ number_format($payment->amount, 2) }}</p>

                                    <p><strong>Payment Method:</strong></p>
                                    <p class="text-muted">{{ ucfirst(str_replace('_', ' ', $payment->payment_method)) }}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Payment Date:</strong></p>
                                    <p class="text-muted">{{ $payment->payment_date->format('M d, Y') }}</p>

                                    <p><strong>Status:</strong></p>
                                    <p>
                                        <span class="badge bg-{{ $payment->status === 'approved' ? 'success' : ($payment->status === 'pending' ? 'warning' : 'danger') }} fs-6">
                                            {{ ucfirst($payment->status) }}
                                        </span>
                                    </p>

                                    <p><strong>Submitted:</strong></p>
                                    <p class="text-muted">{{ $payment->created_at->format('M d, Y H:i') }}</p>
                                </div>
                            </div>

                            @if($payment->notes)
                                <hr>
                                <p><strong>Notes:</strong></p>
                                <div class="bg-light p-3 rounded">
                                    @php
                                        // Try to parse JSON data for plan change details
                                        $notesData = null;
                                        if (str_contains($payment->notes, 'Plan Change Details:')) {
                                            $parts = explode('Plan Change Details:', $payment->notes);
                                            if (count($parts) > 1) {
                                                $jsonPart = trim($parts[1]);
                                                $notesData = json_decode($jsonPart, true);
                                            }
                                        }
                                    @endphp

                                    @if($notesData && is_array($notesData))
                                        {{-- Display formatted plan change details --}}
                                        @if(str_contains($payment->notes, 'Plan Change:'))
                                            <div class="mb-3">
                                                <strong class="text-primary">Plan Change Request</strong>
                                            </div>
                                        @endif

                                        <div class="row">
                                            <div class="col-md-6">
                                                <h6 class="text-info">Change Details</h6>
                                                <ul class="list-unstyled">
                                                    <li><strong>Type:</strong> {{ ucfirst(str_replace('_', ' ', $notesData['type'] ?? 'N/A')) }}</li>
                                                    <li><strong>Change Type:</strong> {{ ucfirst($notesData['change_type'] ?? 'N/A') }}</li>
                                                    @if(isset($notesData['current_plan_id']))
                                                        <li><strong>From Plan ID:</strong> {{ $notesData['current_plan_id'] ?? 'No Plan' }}</li>
                                                    @endif
                                                    @if(isset($notesData['requested_plan_id']))
                                                        <li><strong>To Plan ID:</strong> {{ $notesData['requested_plan_id'] }}</li>
                                                    @endif
                                                </ul>
                                            </div>

                                            @if(isset($notesData['proration']) && is_array($notesData['proration']))
                                                <div class="col-md-6">
                                                    <h6 class="text-success">Pricing Details</h6>
                                                    <ul class="list-unstyled">
                                                        <li><strong>Amount:</strong> ${{ number_format($notesData['proration']['net_amount'] ?? 0, 2) }}</li>
                                                        @if(isset($notesData['proration']['current_plan_credit']) && $notesData['proration']['current_plan_credit'] > 0)
                                                            <li><strong>Credit:</strong> ${{ number_format($notesData['proration']['current_plan_credit'], 2) }}</li>
                                                        @endif
                                                        @if(isset($notesData['proration']['new_plan_charge']))
                                                            <li><strong>New Plan Charge:</strong> ${{ number_format($notesData['proration']['new_plan_charge'], 2) }}</li>
                                                        @endif
                                                        @if(isset($notesData['proration']['remaining_days']))
                                                            <li><strong>Remaining Days:</strong> {{ $notesData['proration']['remaining_days'] }} days</li>
                                                        @endif
                                                    </ul>
                                                </div>
                                            @endif
                                        </div>

                                        @if(isset($notesData['proration']['proration_details']))
                                            <div class="mt-3">
                                                <small class="text-muted">
                                                    <strong>Details:</strong> {{ $notesData['proration']['proration_details'] }}
                                                </small>
                                            </div>
                                        @endif
                                    @else
                                        {{-- Display regular notes --}}
                                        <div style="white-space: pre-wrap;" class="text-muted">{{ $payment->notes }}</div>
                                    @endif
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Subscription Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Subscription Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Plan:</strong></p>
                                    <p class="text-muted">{{ $payment->subscription->plan->name }}</p>

                                    <p><strong>Monthly Cost:</strong></p>
                                    <p class="text-muted">${{ number_format($payment->subscription->plan->price, 2) }}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Subscription Period:</strong></p>
                                    <p class="text-muted">
                                        {{ $payment->subscription->start_date->format('M d, Y') }} -
                                        {{ $payment->subscription->end_date->format('M d, Y') }}
                                    </p>

                                    <p><strong>Subscription Status:</strong></p>
                                    <p>
                                        <span class="badge bg-{{ $payment->subscription->status === 'active' ? 'success' : 'warning' }}">
                                            {{ ucfirst($payment->subscription->status) }}
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    @if($payment->isApproved())
                        <!-- Approval Information -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Approval Information</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>Approved By:</strong></p>
                                        <p class="text-muted">{{ $payment->approver_name }}</p>

                                        @if($payment->invoice_number)
                                            <p><strong>Invoice Number:</strong></p>
                                            <p class="text-muted">{{ $payment->invoice_number }}</p>
                                        @endif
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>Approved Date:</strong></p>
                                        <p class="text-muted">{{ $payment->approved_at->format('M d, Y H:i') }}</p>

                                        @if($payment->invoice_generated_at)
                                            <p><strong>Invoice Generated:</strong></p>
                                            <p class="text-muted">{{ $payment->invoice_generated_at->format('M d, Y H:i') }}</p>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>

                <div class="col-md-4">
                    <!-- Quick Actions -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Quick Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="{{ route('billing.index') }}" class="btn btn-outline-primary">
                                    <i class="fas fa-list me-2"></i>
                                    View All Payments
                                </a>

                                @if($payment->isApproved() && $payment->invoice_number)
                                    <a href="{{ route('billing.invoice', $payment) }}" class="btn btn-outline-info">
                                        <i class="fas fa-file-invoice me-2"></i>
                                        View Invoice
                                    </a>

                                    <a href="{{ route('billing.invoice.download', $payment) }}" class="btn btn-success">
                                        <i class="fas fa-download me-2"></i>
                                        Download Invoice PDF
                                    </a>
                                @endif

                                <a href="{{ route('billing.payment-accounts') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-university me-2"></i>
                                    Payment Accounts
                                </a>

                                @if($payment->subscription->getOutstandingBalance() > 0)
                                    <a href="{{ route('billing.create') }}" class="btn btn-warning">
                                        <i class="fas fa-plus me-2"></i>
                                        Submit Another Payment
                                    </a>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Payment Timeline -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Payment Timeline</h5>
                        </div>
                        <div class="card-body">
                            <div class="timeline">
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-primary"></div>
                                    <div class="timeline-content">
                                        <h6 class="timeline-title">Payment Submitted</h6>
                                        <p class="timeline-text">{{ $payment->created_at->format('M d, Y H:i') }}</p>
                                    </div>
                                </div>

                                @if($payment->isApproved())
                                    <div class="timeline-item">
                                        <div class="timeline-marker bg-success"></div>
                                        <div class="timeline-content">
                                            <h6 class="timeline-title">Payment Approved</h6>
                                            <p class="timeline-text">{{ $payment->approved_at->format('M d, Y H:i') }}</p>
                                            <small class="text-muted">By {{ $payment->approver_name }}</small>
                                        </div>
                                    </div>

                                    @if($payment->invoice_generated_at)
                                        <div class="timeline-item">
                                            <div class="timeline-marker bg-info"></div>
                                            <div class="timeline-content">
                                                <h6 class="timeline-title">Invoice Generated</h6>
                                                <p class="timeline-text">{{ $payment->invoice_generated_at->format('M d, Y H:i') }}</p>
                                                <small class="text-muted">Invoice #{{ $payment->invoice_number }}</small>
                                            </div>
                                        </div>
                                    @endif
                                @elseif($payment->isRejected())
                                    <div class="timeline-item">
                                        <div class="timeline-marker bg-danger"></div>
                                        <div class="timeline-content">
                                            <h6 class="timeline-title">Payment Rejected</h6>
                                            <p class="timeline-text">{{ $payment->updated_at->format('M d, Y H:i') }}</p>
                                        </div>
                                    </div>
                                @else
                                    <div class="timeline-item">
                                        <div class="timeline-marker bg-warning"></div>
                                        <div class="timeline-content">
                                            <h6 class="timeline-title">Under Review</h6>
                                            <p class="timeline-text">Waiting for admin approval</p>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
}

.timeline-content {
    padding-left: 10px;
}

.timeline-title {
    margin-bottom: 5px;
    font-size: 14px;
    font-weight: 600;
}

.timeline-text {
    margin-bottom: 2px;
    font-size: 13px;
    color: #6c757d;
}
</style>
@endsection
