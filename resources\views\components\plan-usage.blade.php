@php
    $organization = auth()->user()->organization ?? null;
    $usage = $organization ? $organization->getPlanUsage() : null;
@endphp

@if($usage && $organization->plan)
<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
    <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900">
            {{ $usage['plan_name'] }} Plan Usage
        </h3>
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            ${{ number_format($organization->plan->price, 2) }}/month
        </span>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <!-- Users Usage -->
        <div class="bg-gray-50 rounded-lg p-4">
            <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-gray-700">Users</span>
                <span class="text-sm text-gray-600">
                    {{ $usage['users']['current'] }} /
                    {{ $usage['users']['limit'] === 'Unlimited' ? '∞' : $usage['users']['limit'] }}
                </span>
            </div>

            @if($usage['users']['limit'] !== 'Unlimited')
                <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
                    <div class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                         style="width: {{ min($usage['users']['percentage'], 100) }}%"></div>
                </div>

                @if($usage['users']['percentage'] >= 80)
                    <div class="flex items-center text-xs">
                        @if($usage['users']['percentage'] >= 100)
                            <svg class="w-4 h-4 text-red-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-red-600 font-medium">Limit reached!</span>
                        @else
                            <svg class="w-4 h-4 text-yellow-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-yellow-600">{{ $usage['users']['remaining'] }} remaining</span>
                        @endif
                    </div>
                @else
                    <span class="text-xs text-gray-500">{{ $usage['users']['remaining'] }} remaining</span>
                @endif
            @else
                <div class="text-xs text-green-600 font-medium">Unlimited users</div>
            @endif
        </div>

        <!-- Branches Usage -->
        <div class="bg-gray-50 rounded-lg p-4">
            <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-gray-700">Branches</span>
                <span class="text-sm text-gray-600">
                    {{ $usage['branches']['current'] }} /
                    {{ $usage['branches']['limit'] === 'Unlimited' ? '∞' : $usage['branches']['limit'] }}
                </span>
            </div>

            @if($usage['branches']['limit'] !== 'Unlimited')
                <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
                    <div class="bg-green-600 h-2 rounded-full transition-all duration-300"
                         style="width: {{ min($usage['branches']['percentage'], 100) }}%"></div>
                </div>

                @if($usage['branches']['percentage'] >= 80)
                    <div class="flex items-center text-xs">
                        @if($usage['branches']['percentage'] >= 100)
                            <svg class="w-4 h-4 text-red-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-red-600 font-medium">Limit reached!</span>
                        @else
                            <svg class="w-4 h-4 text-yellow-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-yellow-600">{{ $usage['branches']['remaining'] }} remaining</span>
                        @endif
                    </div>
                @else
                    <span class="text-xs text-gray-500">{{ $usage['branches']['remaining'] }} remaining</span>
                @endif
            @else
                <div class="text-xs text-green-600 font-medium">Unlimited branches</div>
            @endif
        </div>

        <!-- Orders Usage -->
        <div class="bg-gray-50 rounded-lg p-4">
            <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-gray-700">Orders</span>
                <span class="text-sm text-gray-600">
                    {{ $usage['orders']['current'] }} /
                    {{ $usage['orders']['limit'] === 'Unlimited' ? '∞' : $usage['orders']['limit'] }}
                </span>
            </div>
            <div class="text-xs text-gray-500 mb-2">{{ $usage['orders']['period'] ?? 'This month' }}</div>

            @if($usage['orders']['limit'] !== 'Unlimited')
                <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
                    <div class="bg-purple-600 h-2 rounded-full transition-all duration-300"
                         style="width: {{ min($usage['orders']['percentage'], 100) }}%"></div>
                </div>

                @if($usage['orders']['percentage'] >= 80)
                    <div class="flex items-center text-xs">
                        @if($usage['orders']['percentage'] >= 100)
                            <svg class="w-4 h-4 text-red-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-red-600 font-medium">Monthly limit reached!</span>
                        @else
                            <svg class="w-4 h-4 text-yellow-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-yellow-600">{{ $usage['orders']['remaining'] }} remaining this month</span>
                        @endif
                    </div>
                @else
                    <span class="text-xs text-gray-500">{{ $usage['orders']['remaining'] }} remaining this month</span>
                @endif
            @else
                <div class="text-xs text-green-600 font-medium">Unlimited orders</div>
            @endif
        </div>
    </div>

    <!-- Data Retention Info -->
    <div class="mt-4 pt-4 border-t border-gray-200">
        <div class="flex items-center justify-between">
            <span class="text-sm font-medium text-gray-700">Data Retention</span>
            <span class="text-sm text-gray-600">
                {{ $usage['data_retention']['days'] === 'Unlimited' ? 'Unlimited' : $usage['data_retention']['days'] . ' days' }}
            </span>
        </div>
        @if($usage['data_retention']['days'] !== 'Unlimited')
            <p class="text-xs text-gray-500 mt-1">
                Data older than {{ $usage['data_retention']['days'] }} days will be automatically removed
            </p>
        @endif
    </div>

    <!-- Upgrade Notice -->
    @if($usage['users']['percentage'] >= 80 || $usage['branches']['percentage'] >= 80 || $usage['orders']['percentage'] >= 80)
        <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div class="flex items-start">
                <svg class="w-5 h-5 text-yellow-400 mt-0.5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                <div>
                    <h4 class="text-sm font-medium text-yellow-800">Approaching Plan Limits</h4>
                    <p class="text-sm text-yellow-700 mt-1">
                        You're approaching your plan limits. Consider upgrading to avoid service interruption.
                        @if($usage['orders']['percentage'] >= 80)
                            <br><strong>Note:</strong> Order limits reset monthly.
                        @endif
                    </p>
                    <a href="{{ route('billing.index') }}" class="inline-flex items-center mt-2 text-sm font-medium text-yellow-800 hover:text-yellow-900">
                        View Billing & Upgrade Options
                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    @endif
</div>
@endif
