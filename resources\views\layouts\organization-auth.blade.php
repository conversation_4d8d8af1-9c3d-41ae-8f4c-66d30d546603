@php
    $settings = \App\Models\Setting::first() ?? new \App\Models\Setting;
@endphp
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="{{ $settings->theme_mode }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', $settings->site_title . ' - ' . $settings->app_name)</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ $settings->favicon_url }}">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Bootstrap and jQuery -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- SweetAlert2 -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    @production
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    @else
    <script src="https://cdn.tailwindcss.com"></script>
    @endproduction

    <style>
        :root {
            --primary-color: {{ $settings->primary_color }};
            --primary-color-80: {{ $settings->primary_color }}cc;
            --primary-color-60: {{ $settings->primary_color }}99;
        }

        /* Apply primary color to various elements */
        .bg-primary {
            background-color: var(--primary-color) !important;
        }

        .text-primary {
            color: var(--primary-color) !important;
        }

        .btn-primary {
            background-color: var(--primary-color) !important;
            border-color: var(--primary-color) !important;
        }

        .btn-primary:hover {
            background-color: var(--primary-color-80) !important;
            border-color: var(--primary-color-80) !important;
        }

        .border-primary {
            border-color: var(--primary-color) !important;
        }

        /* Custom styles for auth pages */
        .auth-container {
            min-height: 100vh;
        }

        .auth-brand-side {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-color-80));
        }

        .auth-form-side {
            background-color: #f8f9fa;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem var(--primary-color-60);
        }
    </style>
</head>
<body class="font-sans antialiased">
    <!-- Impersonation Banner -->
    @include('components.impersonation-banner')

    <!-- Main Content -->
    <main>
        @yield('content')
    </main>

    <!-- Scripts -->
    <script>
        // Show body after styles are loaded to prevent FOUC
        document.addEventListener('DOMContentLoaded', function() {
            document.body.style.display = 'block';
        });
    </script>

    @stack('scripts')
</body>
</html>
