<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\SubscriptionPayment;
use App\Models\Subscription;
use App\Models\Organization;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class SubscriptionPaymentController extends Controller
{
    /**
     * Display a listing of subscription payments.
     */
    public function index(Request $request)
    {
        $query = SubscriptionPayment::with(['subscription.plan', 'organization', 'approvedBy']);

        // Filter by status
        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }

        // Filter by organization
        if ($request->has('organization') && $request->organization) {
            $query->where('organization_id', $request->organization);
        }

        // Filter by date range
        if ($request->has('date_from') && $request->date_from) {
            $query->whereDate('payment_date', '>=', $request->date_from);
        }
        if ($request->has('date_to') && $request->date_to) {
            $query->whereDate('payment_date', '<=', $request->date_to);
        }

        $payments = $query->latest('payment_date')->paginate(20);
        $organizations = Organization::orderBy('name')->get();

        // Get statistics
        $stats = [
            'total_payments' => SubscriptionPayment::count(),
            'pending_payments' => SubscriptionPayment::pending()->count(),
            'approved_payments' => SubscriptionPayment::approved()->count(),
            'total_amount_pending' => SubscriptionPayment::pending()->sum('amount'),
            'total_amount_approved' => SubscriptionPayment::approved()->sum('amount'),
        ];

        return view('super_admin.subscription_payments.index', compact('payments', 'organizations', 'stats'));
    }

    /**
     * Show the form for creating a new payment record.
     */
    public function create(Request $request)
    {
        $organizations = Organization::with('subscriptions.plan')->orderBy('name')->get();
        $selectedOrganization = null;
        $selectedSubscription = null;

        if ($request->has('organization_id')) {
            $selectedOrganization = Organization::find($request->organization_id);
        }
        if ($request->has('subscription_id')) {
            $selectedSubscription = Subscription::find($request->subscription_id);
        }

        return view('super_admin.subscription_payments.create', compact(
            'organizations',
            'selectedOrganization',
            'selectedSubscription'
        ));
    }

    /**
     * Store a newly created payment record.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'subscription_id' => 'required|exists:subscriptions,id',
            'organization_id' => 'required|exists:organizations,id',
            'payment_reference' => 'nullable|string|max:255',
            'amount' => 'required|numeric|min:0.01',
            'payment_method' => 'required|string|in:manual,bank_transfer,cash,check,mobile_money',
            'payment_date' => 'required|date',
            'notes' => 'nullable|string|max:1000',
            'auto_approve' => 'boolean',
        ]);

        DB::beginTransaction();
        try {
            $payment = SubscriptionPayment::create($validated);

            // Auto-approve if requested
            if ($request->boolean('auto_approve')) {
                $payment->approve(Auth::user());

                // Generate invoice number automatically when payment is approved
                if (!$payment->invoice_number) {
                    $payment->update([
                        'invoice_number' => $payment->generateInvoiceNumber(),
                        'invoice_generated_at' => now(),
                    ]);
                }

                $this->updateSubscriptionStatus($payment);
            }

            DB::commit();
            return redirect()->route('super.subscription-payments.index')
                           ->with('success', 'Payment record created successfully.');
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'Failed to create payment record: ' . $e->getMessage()])
                        ->withInput();
        }
    }

    /**
     * Display the specified payment.
     */
    public function show(SubscriptionPayment $subscriptionPayment)
    {
        $subscriptionPayment->load(['subscription.plan', 'organization', 'approvedBy']);
        return view('super_admin.subscription_payments.show', compact('subscriptionPayment'));
    }

    /**
     * Show the form for editing the specified payment.
     */
    public function edit(SubscriptionPayment $subscriptionPayment)
    {
        if ($subscriptionPayment->isApproved()) {
            return back()->withErrors(['error' => 'Cannot edit approved payments.']);
        }

        $organizations = Organization::with('subscriptions.plan')->orderBy('name')->get();
        return view('super_admin.subscription_payments.edit', compact('subscriptionPayment', 'organizations'));
    }

    /**
     * Update the specified payment.
     */
    public function update(Request $request, SubscriptionPayment $subscriptionPayment)
    {
        if ($subscriptionPayment->isApproved()) {
            return back()->withErrors(['error' => 'Cannot edit approved payments.']);
        }

        $validated = $request->validate([
            'payment_reference' => 'nullable|string|max:255',
            'amount' => 'required|numeric|min:0.01',
            'payment_method' => 'required|string|in:manual,bank_transfer,cash,check,mobile_money',
            'payment_date' => 'required|date',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            $subscriptionPayment->update($validated);
            return redirect()->route('super.subscription-payments.index')
                           ->with('success', 'Payment updated successfully.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to update payment: ' . $e->getMessage()])
                        ->withInput();
        }
    }

    /**
     * Approve a payment.
     */
    public function approve(Request $request, SubscriptionPayment $subscriptionPayment)
    {
        // Debug logging
        \Log::info('Payment approval attempt', [
            'payment_id' => $subscriptionPayment->id,
            'user_id' => Auth::guard('super_admin')->id(),
            'session_id' => session()->getId(),
            'csrf_token' => $request->input('_token'),
            'session_token' => session()->token(),
            'request_method' => $request->method(),
            'request_url' => $request->fullUrl()
        ]);

        // Validate CSRF token manually if needed
        if (!$request->hasValidSignature() && !hash_equals(session()->token(), $request->input('_token', ''))) {
            \Log::warning('CSRF token mismatch in payment approval', [
                'payment_id' => $subscriptionPayment->id,
                'provided_token' => $request->input('_token'),
                'session_token' => session()->token()
            ]);

            // Regenerate session token and redirect back with error
            session()->regenerateToken();
            return back()->withErrors(['error' => 'Security token expired. Please try again.']);
        }

        if ($subscriptionPayment->isApproved()) {
            return back()->withErrors(['error' => 'Payment is already approved.']);
        }

        DB::beginTransaction();
        try {
            // Get the super admin user
            $superAdmin = Auth::guard('super_admin')->user();
            $subscriptionPayment->approve($superAdmin);

            // Generate invoice number automatically when payment is approved
            if (!$subscriptionPayment->invoice_number) {
                $subscriptionPayment->update([
                    'invoice_number' => $subscriptionPayment->generateInvoiceNumber(),
                    'invoice_generated_at' => now(),
                ]);
            }

            $this->updateSubscriptionStatus($subscriptionPayment);

            DB::commit();
            return back()->with('success', 'Payment approved successfully. Invoice generated.');
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'Failed to approve payment: ' . $e->getMessage()]);
        }
    }

    /**
     * Reject a payment.
     */
    public function reject(Request $request, SubscriptionPayment $subscriptionPayment)
    {
        if ($subscriptionPayment->isApproved()) {
            return back()->withErrors(['error' => 'Cannot reject approved payments.']);
        }

        $request->validate([
            'rejection_reason' => 'required|string|max:500',
        ]);

        try {
            // Get the super admin user
            $superAdmin = Auth::guard('super_admin')->user();
            $subscriptionPayment->reject($superAdmin, $request->rejection_reason);
            return back()->with('success', 'Payment rejected successfully.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to reject payment: ' . $e->getMessage()]);
        }
    }

    /**
     * Delete a payment record.
     */
    public function destroy(SubscriptionPayment $subscriptionPayment)
    {
        if ($subscriptionPayment->isApproved()) {
            return back()->withErrors(['error' => 'Cannot delete approved payments.']);
        }

        try {
            $subscriptionPayment->delete();
            return redirect()->route('super.subscription-payments.index')
                           ->with('success', 'Payment record deleted successfully.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to delete payment: ' . $e->getMessage()]);
        }
    }

    /**
     * Update subscription status based on payment approval.
     */
    private function updateSubscriptionStatus(SubscriptionPayment $payment)
    {
        $subscription = $payment->subscription;
        $organization = $payment->organization;
        $totalPaid = $subscription->calculateTotalPaid();

        // Check if this payment is for a plan change
        $isPlanChange = strpos($payment->notes, 'Plan Change Details:') !== false;

        if ($isPlanChange) {
            // Extract plan change details from payment notes
            $notesLines = explode("\n", $payment->notes);
            $planChangeDetailsLine = null;

            foreach ($notesLines as $line) {
                if (strpos($line, 'Plan Change Details:') !== false) {
                    $planChangeDetailsLine = $line;
                    break;
                }
            }

            if ($planChangeDetailsLine) {
                $jsonStart = strpos($planChangeDetailsLine, '{');
                if ($jsonStart !== false) {
                    $jsonData = substr($planChangeDetailsLine, $jsonStart);
                    $planChangeData = json_decode($jsonData, true);

                    if ($planChangeData && isset($planChangeData['requested_plan_id'])) {
                        // Update organization's plan
                        $organization->update(['plan_id' => $planChangeData['requested_plan_id']]);

                        // Update subscription plan
                        $subscription->update(['plan_id' => $planChangeData['requested_plan_id']]);

                        // Log the plan change activation
                        \Log::info('Plan change activated via payment approval', [
                            'organization_id' => $organization->id,
                            'payment_id' => $payment->id,
                            'subscription_id' => $subscription->id,
                            'old_plan_id' => $planChangeData['current_plan_id'],
                            'new_plan_id' => $planChangeData['requested_plan_id'],
                            'approved_by' => $payment->approved_by,
                        ]);
                    }
                }
            }
        }

        // Update subscription amount paid
        $subscription->update([
            'amount_paid' => $totalPaid,
            'last_payment_date' => $payment->payment_date,
            'payment_status' => $subscription->isFullyPaid() ? 'paid' : 'partial',
        ]);

        // If fully paid and subscription was pending or past_due, activate it
        if ($subscription->isFullyPaid() && in_array($subscription->status, ['pending', 'past_due'])) {
            $subscription->update(['status' => 'active']);
        }
    }
}
