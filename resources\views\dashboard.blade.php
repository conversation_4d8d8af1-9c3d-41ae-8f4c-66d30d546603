@extends('layouts.app')

@section('styles')
<style>
    .chart-wrapper {
        position: relative;
        width: 100%;
        min-height: 400px;
    }
    #userOrdersChart {
        width: 100% !important;
        height: 100% !important;
    }

    @media (max-width: 768px) {
        .filter-form {
            flex-direction: column;
            gap: 1rem;
        }

        .filter-form > div {
            width: 100%;
        }

        .filter-buttons {
            display: flex;
            gap: 0.5rem;
            width: 100%;
        }

        .filter-buttons button,
        .filter-buttons a {
            flex: 1;
        }
    }
</style>
@endsection

@section('content')
    <div class="py-6 md:py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Announcements Section -->
            @if($announcements && $announcements->count() > 0)
                <div class="mb-6">
                    @foreach($announcements as $announcement)
                        <div id="announcement-{{ $announcement->id }}" class="alert alert-{{ $announcement->type === 'maintenance' ? 'secondary' : $announcement->type }} {{ $announcement->priority === 'urgent' ? 'border-danger border-3' : '' }} d-flex align-items-start mb-3" role="alert">
                            <i class="{{ $announcement->icon }} fa-lg me-3 mt-1"></i>
                            <div class="flex-grow-1">
                                <h5 class="alert-heading mb-2">{{ $announcement->title }}</h5>
                                <div>{{ $announcement->content }}</div>
                                @if($announcement->affected_features && count($announcement->affected_features) > 0)
                                    <div class="mt-2">
                                        <strong>Affected Features:</strong>
                                        @foreach($announcement->affected_features as $feature)
                                            <span class="badge bg-dark me-1">{{ $feature }}</span>
                                        @endforeach
                                    </div>
                                @endif
                                @if($announcement->starts_at || $announcement->ends_at)
                                    <div class="mt-2 small">
                                        @if($announcement->starts_at)
                                            <div><strong>Start:</strong> {{ $announcement->starts_at->format('M j, Y g:i A') }}</div>
                                        @endif
                                        @if($announcement->ends_at)
                                            <div><strong>End:</strong> {{ $announcement->ends_at->format('M j, Y g:i A') }}</div>
                                        @endif
                                    </div>
                                @endif
                            </div>
                            @if($announcement->is_dismissible)
                                <button type="button" class="btn-close" onclick="event.preventDefault(); event.stopPropagation(); dismissAnnouncement({{ $announcement->id }});" aria-label="Close"></button>
                            @endif
                        </div>
                    @endforeach
                </div>
            @endif

            <!-- Date Range Filter Form -->
            <div class="mb-6 flex flex-col md:flex-row md:justify-between md:items-center space-y-4 md:space-y-0">
                <h2 class="text-xl md:text-2xl font-bold">Dashboard {{ $periodLabel }}</h2>
                <form action="{{ route('dashboard') }}" method="GET" class="filter-form flex">
                    <div>
                        <label for="start_date" class="block text-sm font-medium text-gray-700">Start Date</label>
                        <input type="date" id="start_date" name="start_date" value="{{ request('start_date', now()->format('Y-m-d')) }}"
                               class="mt-1 block w-full rounded-md border-gray-300">
                    </div>
                    <div>
                        <label for="end_date" class="block text-sm font-medium text-gray-700">End Date</label>
                        <input type="date" id="end_date" name="end_date" value="{{ request('end_date', now()->format('Y-m-d')) }}"
                               class="mt-1 block w-full rounded-md border-gray-300">
                    </div>
                    <div class="filter-buttons flex items-end space-x-2">
                        <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md">Filter</button>
                        <a href="{{ route('dashboard') }}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md text-center">Reset</a>
                    </div>
                </form>
            </div>

            <!-- Plan Status Card -->
            @if(auth()->user()->hasAnyRole(['Organization Owner', 'Manager']) && auth()->user()->organization)
                @php
                    $organization = auth()->user()->organization;
                    $currentPlan = $organization->plan;
                    $accessStatus = $organization->access_status;
                @endphp
                <div class="mb-6">
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
                        <div class="flex justify-between items-start">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">Subscription Status</h3>
                                @if($currentPlan)
                                    <div class="flex items-center space-x-2 mb-2">
                                        <span class="text-xl font-bold text-blue-600">{{ $currentPlan->name }}</span>
                                        @switch($accessStatus)
                                            @case('active')
                                                <span class="px-2 py-1 text-xs font-semibold bg-green-100 text-green-800 rounded-full">Active</span>
                                                @break
                                            @case('trial')
                                                <span class="px-2 py-1 text-xs font-semibold bg-blue-100 text-blue-800 rounded-full">Trial</span>
                                                @break
                                            @case('grace_period')
                                                <span class="px-2 py-1 text-xs font-semibold bg-yellow-100 text-yellow-800 rounded-full">Grace Period</span>
                                                @break
                                            @case('expired')
                                                <span class="px-2 py-1 text-xs font-semibold bg-red-100 text-red-800 rounded-full">Expired</span>
                                                @break
                                        @endswitch
                                    </div>
                                    <p class="text-sm text-gray-600">${{ number_format($currentPlan->price, 2) }}/month</p>

                                    @if($organization->activeSubscription)
                                        @php $subscription = $organization->activeSubscription; @endphp
                                        <p class="text-sm text-gray-500 mt-1">
                                            @if($subscription->end_date->isFuture())
                                                Renews {{ $subscription->end_date->diffForHumans() }}
                                            @else
                                                Expired {{ $subscription->end_date->diffForHumans() }}
                                            @endif
                                        </p>
                                    @endif
                                @else
                                    <div class="text-yellow-600">
                                        <span class="text-lg font-semibold">No Active Plan</span>
                                        <p class="text-sm">Choose a subscription plan to get started</p>
                                    </div>
                                @endif
                            </div>
                            <div class="text-right">
                                <a href="{{ route('plan-change.index') }}" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <i class="fas fa-credit-card mr-2"></i>
                                    @if($currentPlan) Manage Plan @else Choose Plan @endif
                                </a>
                            </div>
                        </div>

                        @if($accessStatus === 'grace_period')
                            <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-yellow-800">Grace Period Active</h3>
                                        <div class="mt-2 text-sm text-yellow-700">
                                            <p>Your subscription has expired. You have limited access until {{ $organization->grace_period_end->format('M j, Y') }}.</p>
                                        </div>
                                        <div class="mt-3">
                                            <a href="{{ route('plan-change.index') }}" class="text-sm font-medium text-yellow-800 underline hover:text-yellow-600">
                                                Renew now to restore full access →
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @elseif($organization->trial_ends_at && $organization->trial_ends_at->isFuture())
                            <div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-clock text-blue-400"></i>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-blue-800">Trial Period</h3>
                                        <div class="mt-2 text-sm text-blue-700">
                                            <p>Your trial expires {{ $organization->trial_ends_at->diffForHumans() }}.</p>
                                        </div>
                                        <div class="mt-3">
                                            <a href="{{ route('plan-change.index') }}" class="text-sm font-medium text-blue-800 underline hover:text-blue-600">
                                                Choose a plan to continue →
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            @endif

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
                <!-- Total Revenue Card -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
                    <div class="text-gray-900 text-xl font-semibold mb-2">Total Revenue</div>
                    <div class="text-3xl font-bold text-green-600">
                        {{ format_money($financialMetrics->total_revenue) }}
                    </div>
                </div>

                <!-- Total Orders Card -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
                    <div class="text-gray-900 text-xl font-semibold mb-2">Total Orders</div>
                    <div class="text-3xl font-bold text-blue-600">
                        {{ number_format($totalOrders) }}
                    </div>
                </div>

                <!-- Production Amount Card -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
                    <div class="text-gray-900 text-xl font-semibold mb-2">Production Amount</div>
                    <div class="text-3xl font-bold text-purple-600">
                        {{ format_money($financialMetrics->total_production) }}
                    </div>
                </div>

                <!-- Pending Orders Card -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
                    <div class="text-gray-900 text-xl font-semibold mb-2">Pending Orders</div>
                    <div class="text-3xl font-bold text-yellow-600">
                        {{ number_format($pendingOrders) }}
                    </div>
                </div>

                <!-- Processing Orders Card -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
                    <div class="text-gray-900 text-xl font-semibold mb-2">Processing Orders</div>
                    <div class="text-3xl font-bold text-orange-600">
                        {{ number_format($processingOrders) }}
                    </div>
                </div>

                <!-- Completed Orders Card -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
                    <div class="text-gray-900 text-xl font-semibold mb-2">Completed Orders</div>
                    <div class="text-3xl font-bold text-green-600">
                        {{ number_format($completedOrders) }}
                    </div>
                </div>
            </div>

            <!-- Financial Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="bg-white rounded-lg shadow p-4">
                    <h4 class="text-sm font-medium text-gray-500">Total Revenue</h4>
                    <p class="text-xl font-bold text-gray-900">
                        {{ format_money($financialMetrics->total_revenue) }}
                    </p>
                </div>

                <div class="bg-white rounded-lg shadow p-4">
                    <h4 class="text-sm font-medium text-gray-500">Total Orders</h4>
                    <p class="text-xl font-bold text-blue-600">{{ $totalOrders }}</p>
                </div>

                <div class="bg-white rounded-lg shadow p-4">
                    <h4 class="text-sm font-medium text-gray-500">Pending Payment</h4>
                    <p class="text-xl font-bold text-red-600">
                        {{ format_money($financialMetrics->total_pending_payment) }}
                    </p>
                </div>
            </div>

            <!-- User Order Statistics Chart -->
            <div class="mt-8">
                <div class="bg-white p-4 md:p-6 rounded-lg shadow-sm">
                    <div class="flex flex-col md:flex-row md:justify-between md:items-center mb-4 space-y-2 md:space-y-0">
                        <h3 class="text-lg font-medium">Staff Orders ({{ $periodLabel }})</h3>
                        <div class="flex items-center space-x-4">
                            <div class="text-sm">
                                <span class="inline-block w-3 h-3 bg-blue-500 rounded-full mr-1"></span>
                                Orders Created
                            </div>
                        </div>
                    </div>
                    <div class="relative h-64 md:h-80">
                        <canvas id="userOrdersChart"></canvas>
                    </div>
                    <!-- Stats Summary -->
                    <div class="mt-4 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div class="text-center">
                            <div class="text-sm text-gray-600">Active Staff</div>
                            <div class="text-xl font-semibold">{{ count($userOrderStats) }}</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-600">Total Orders</div>
                            <div class="text-xl font-semibold">{{ $userOrderStats->sum('order_count') }}</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-600">Most Active Staff</div>
                            <div class="text-xl font-semibold">
                                {{ $userOrderStats->sortByDesc('order_count')->first()['user_name'] ?? 'N/A' }}
                                <div class="text-sm text-gray-500">
                                    ({{ $userOrderStats->sortByDesc('order_count')->first()['order_count'] ?? 0 }} orders)
                                </div>
                            </div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-600">Average Orders/Staff</div>
                            <div class="text-xl font-semibold">
                                {{ number_format($userOrderStats->avg('order_count'), 1) }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // User Orders Chart with enhanced styling
    const userOrdersCtx = document.getElementById('userOrdersChart').getContext('2d');
    const userOrdersData = @json($userOrderStats);

    new Chart(userOrdersCtx, {
        type: 'bar',
        data: {
            labels: userOrdersData.map(stat => stat.user_name),
            datasets: [{
                label: 'Orders Created',
                data: userOrdersData.map(stat => stat.order_count),
                backgroundColor: userOrdersData.map(() => 'rgba(59, 130, 246, 0.5)'),
                borderColor: userOrdersData.map(() => 'rgb(59, 130, 246)'),
                borderWidth: 1,
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `${context.dataset.label}: ${context.raw} orders`;
                        }
                    }
                }
            }
        }
    });
});

// Announcement dismiss functionality
function dismissAnnouncement(announcementId) {
    console.log('🗑️ Dismissing announcement:', announcementId);

    // Prevent any default behavior
    if (event) {
        event.preventDefault();
        event.stopPropagation();
    }

    // Find the announcement element
    const element = document.getElementById(`announcement-${announcementId}`);
    console.log('📦 Found element:', element);

    if (element) {
        console.log('✅ Element found, applying fade effect');
        element.style.transition = 'opacity 0.3s ease';
        element.style.opacity = '0';

        // Send AJAX request to dismiss
        fetch(`/announcements-dismiss/${announcementId}`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                _token: document.querySelector('meta[name="csrf-token"]').content
            })
        })
        .then(response => {
            console.log('📤 Dismiss request sent, status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('✅ Dismiss request completed:', data);
            if (data.success) {
                console.log('✅ Announcement successfully dismissed on server');
            }
        })
        .catch(error => {
            console.error('❌ Error dismissing announcement:', error);
            // Still remove the element even if the request fails
        });

        // Remove element after fade
        setTimeout(() => {
            console.log('🗑️ Removing element from DOM');
            element.remove();
        }, 300);
    } else {
        console.error('❌ Element not found with ID: announcement-' + announcementId);
    }

    // Explicitly return false to prevent any form submission
    return false;
}
</script>
@endpush
