@extends('layouts.app')

@section('title', 'Submit Payment')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Submit Payment</h1>
                <a href="{{ route('billing.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Billing
                </a>
            </div>

            @if($errors->any())
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <ul class="mb-0">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Payment Information</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="{{ route('billing.store') }}">
                                @csrf
                                
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i>
                                    <strong>Outstanding Balance:</strong> ${{ number_format($outstandingBalance, 2) }}
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="payment_reference" class="form-label">Payment Reference <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('payment_reference') is-invalid @enderror" 
                                                   id="payment_reference" name="payment_reference" value="{{ old('payment_reference') }}" required
                                                   placeholder="Bank transfer reference, receipt number, etc.">
                                            <div class="form-text">Enter the reference number from your bank transfer or payment receipt</div>
                                            @error('payment_reference')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="amount" class="form-label">Amount <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <span class="input-group-text">$</span>
                                                <input type="number" class="form-control @error('amount') is-invalid @enderror" 
                                                       id="amount" name="amount" value="{{ old('amount', $outstandingBalance) }}" 
                                                       step="0.01" min="0.01" max="{{ $outstandingBalance }}" required>
                                            </div>
                                            <div class="form-text">Maximum: ${{ number_format($outstandingBalance, 2) }}</div>
                                            @error('amount')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="payment_method" class="form-label">Payment Method <span class="text-danger">*</span></label>
                                            <select class="form-select @error('payment_method') is-invalid @enderror" 
                                                    id="payment_method" name="payment_method" required>
                                                <option value="">Select Payment Method</option>
                                                <option value="bank_transfer" {{ old('payment_method') == 'bank_transfer' ? 'selected' : '' }}>Bank Transfer</option>
                                                <option value="cash" {{ old('payment_method') == 'cash' ? 'selected' : '' }}>Cash Deposit</option>
                                                <option value="check" {{ old('payment_method') == 'check' ? 'selected' : '' }}>Check</option>
                                                <option value="mobile_money" {{ old('payment_method') == 'mobile_money' ? 'selected' : '' }}>Mobile Money</option>
                                            </select>
                                            @error('payment_method')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="payment_date" class="form-label">Payment Date <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control @error('payment_date') is-invalid @enderror" 
                                                   id="payment_date" name="payment_date" value="{{ old('payment_date', date('Y-m-d')) }}" 
                                                   max="{{ date('Y-m-d') }}" required>
                                            @error('payment_date')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="notes" class="form-label">Additional Notes</label>
                                    <textarea class="form-control @error('notes') is-invalid @enderror" 
                                              id="notes" name="notes" rows="3" placeholder="Any additional information about this payment">{{ old('notes') }}</textarea>
                                    @error('notes')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{{ route('billing.index') }}" class="btn btn-secondary">Cancel</a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-paper-plane"></i> Submit Payment
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Payment Instructions</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>Important:</strong> Make your payment first, then submit this form with the payment details.
                            </div>

                            <h6>Available Payment Accounts:</h6>
                            @foreach($paymentAccounts as $account)
                                <div class="card mb-3 {{ $account->is_primary ? 'border-primary' : '' }}">
                                    <div class="card-body p-3">
                                        @if($account->is_primary)
                                            <span class="badge bg-primary mb-2">Primary Account</span>
                                        @endif
                                        <h6 class="card-title">{{ $account->bank_name }}</h6>
                                        <p class="card-text small mb-1">
                                            <strong>Account Name:</strong> {{ $account->account_name }}<br>
                                            <strong>Account Number:</strong> {{ $account->account_number }}<br>
                                            <strong>Account Type:</strong> {{ ucfirst($account->account_type) }}
                                            @if($account->swift_code)
                                                <br><strong>SWIFT Code:</strong> {{ $account->swift_code }}
                                            @endif
                                        </p>
                                        @if($account->additional_instructions)
                                            <div class="alert alert-info p-2 mt-2">
                                                <small>{{ $account->additional_instructions }}</small>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @endforeach

                            <div class="mt-3">
                                <h6>Next Steps:</h6>
                                <ol class="small">
                                    <li>Make payment to one of the accounts above</li>
                                    <li>Keep your payment receipt/reference</li>
                                    <li>Fill out this form with payment details</li>
                                    <li>Wait for admin approval</li>
                                    <li>Download your invoice once approved</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
