<?php $__env->startSection('title', 'Subscription Plans'); ?>
<?php $__env->startSection('page-title', 'Subscription Plans'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Subscription Plans</h1>
            <p class="text-muted">Manage subscription plans and pricing</p>
        </div>
        <a href="<?php echo e(route('super.plans.create')); ?>" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Add Plan
        </a>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="GET" action="<?php echo e(route('super.plans.index')); ?>" class="row g-3">
                <div class="col-md-6">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search"
                           value="<?php echo e(request('search')); ?>" placeholder="Search plans...">
                </div>
                <div class="col-md-4">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Statuses</option>
                        <option value="active" <?php echo e(request('status') === 'active' ? 'selected' : ''); ?>>Active</option>
                        <option value="inactive" <?php echo e(request('status') === 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-search"></i> Filter
                    </button>
                    <a href="<?php echo e(route('super.plans.index')); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> Clear
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Plans Table -->
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                Subscription Plans (<?php echo e($plans->total()); ?>)
            </h6>
        </div>
        <div class="card-body">
            <?php if($plans->count() > 0): ?>
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Plan</th>
                                <th>Price</th>
                                <th>Limits</th>
                                <th>Features</th>
                                <th>Organizations</th>
                                <th>Subscriptions</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $plans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <div>
                                            <div class="font-weight-bold"><?php echo e($plan->name); ?></div>
                                            <?php if($plan->is_featured): ?>
                                                <span class="badge bg-warning text-dark">Featured</span>
                                            <?php endif; ?>
                                            <?php if($plan->description): ?>
                                                <small class="text-muted d-block"><?php echo e(Str::limit($plan->description, 50)); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="h5 mb-0">$<?php echo e(number_format($plan->price, 2)); ?></div>
                                        <small class="text-muted">per month</small>
                                    </td>
                                    <td>
                                        <small>
                                            <strong>Branches:</strong> <?php echo e($plan->branch_limit == 999 ? 'Unlimited' : $plan->branch_limit); ?><br>
                                            <strong>Users:</strong> <?php echo e($plan->user_limit == 999 ? 'Unlimited' : $plan->user_limit); ?><br>
                                            <strong>Orders:</strong> <?php echo e($plan->order_limit === null ? 'Unlimited' : $plan->order_limit . '/month'); ?><br>
                                            <strong>Data:</strong> <?php echo e($plan->data_retention_days == 999 ? 'Forever' : $plan->data_retention_days . ' days'); ?>

                                        </small>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-wrap gap-1">
                                            <?php if($plan->thermal_printing): ?>
                                                <span class="badge bg-success">Thermal Printing</span>
                                            <?php endif; ?>
                                            <?php if($plan->advanced_reporting): ?>
                                                <span class="badge bg-info">Advanced Reports</span>
                                            <?php endif; ?>
                                            <?php if($plan->api_access): ?>
                                                <span class="badge bg-primary">API Access</span>
                                            <?php endif; ?>
                                            <?php if($plan->white_label): ?>
                                                <span class="badge bg-secondary">White Label</span>
                                            <?php endif; ?>
                                            <?php if($plan->custom_branding): ?>
                                                <span class="badge bg-warning">Custom Branding</span>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary"><?php echo e($plan->organizations_count ?? 0); ?></span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success"><?php echo e($plan->subscriptions_count ?? 0); ?></span>
                                    </td>
                                    <td>
                                        <?php if($plan->is_active): ?>
                                            <span class="badge bg-success">Active</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">Inactive</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('super.plans.show', $plan)); ?>"
                                               class="btn btn-sm btn-outline-primary" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?php echo e(route('super.plans.edit', $plan)); ?>"
                                               class="btn btn-sm btn-outline-secondary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <?php if($plan->is_active): ?>
                                                <form method="POST" action="<?php echo e(route('super.plans.deactivate', $plan)); ?>"
                                                      class="d-inline">
                                                    <?php echo csrf_field(); ?>
                                                    <button type="submit" class="btn btn-sm btn-outline-warning"
                                                            title="Deactivate" onclick="return confirm('Are you sure?')">
                                                        <i class="fas fa-pause"></i>
                                                    </button>
                                                </form>
                                            <?php else: ?>
                                                <form method="POST" action="<?php echo e(route('super.plans.activate', $plan)); ?>"
                                                      class="d-inline">
                                                    <?php echo csrf_field(); ?>
                                                    <button type="submit" class="btn btn-sm btn-outline-success"
                                                            title="Activate">
                                                        <i class="fas fa-play"></i>
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div>
                        Showing <?php echo e($plans->firstItem()); ?> to <?php echo e($plans->lastItem()); ?>

                        of <?php echo e($plans->total()); ?> results
                    </div>
                    <?php echo e($plans->links()); ?>

                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-layer-group fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No plans found</h5>
                    <p class="text-muted">Get started by creating your first subscription plan.</p>
                    <a href="<?php echo e(route('super.plans.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add Plan
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('super_admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\SalesManagementSystem\resources\views/super_admin/plans/index.blade.php ENDPATH**/ ?>