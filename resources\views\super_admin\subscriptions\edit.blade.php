@extends('super_admin.layouts.app')

@section('title', 'Edit Subscription')
@section('page-title', 'Edit Subscription')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Edit Subscription</h1>
            <p class="text-muted">{{ $subscription->organization->name }} - {{ $subscription->plan->name }}</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('super.subscriptions.show', $subscription) }}" class="btn btn-info">
                <i class="fas fa-eye me-2"></i>View Subscription
            </a>
            <a href="{{ route('super.subscriptions.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Subscriptions
            </a>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Subscription Details</h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('super.subscriptions.update', $subscription) }}">
                        @csrf
                        @method('PUT')

                        <!-- Organization and Plan -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="organization_id" class="form-label">Organization *</label>
                                <select class="form-select @error('organization_id') is-invalid @enderror" 
                                        id="organization_id" name="organization_id" required>
                                    <option value="">Select Organization</option>
                                    @foreach($organizations as $organization)
                                        <option value="{{ $organization->id }}" 
                                                {{ old('organization_id', $subscription->organization_id) == $organization->id ? 'selected' : '' }}>
                                            {{ $organization->name }} ({{ $organization->email }})
                                        </option>
                                    @endforeach
                                </select>
                                @error('organization_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="plan_id" class="form-label">Plan *</label>
                                <select class="form-select @error('plan_id') is-invalid @enderror" 
                                        id="plan_id" name="plan_id" required>
                                    <option value="">Select Plan</option>
                                    @foreach($plans as $plan)
                                        <option value="{{ $plan->id }}" 
                                                data-price="{{ $plan->price }}"
                                                {{ old('plan_id', $subscription->plan_id) == $plan->id ? 'selected' : '' }}>
                                            {{ $plan->name }} - ${{ number_format($plan->price, 2) }}/month
                                        </option>
                                    @endforeach
                                </select>
                                @error('plan_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Status and Dates -->
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <label for="status" class="form-label">Status *</label>
                                <select class="form-select @error('status') is-invalid @enderror" 
                                        id="status" name="status" required>
                                    <option value="active" {{ old('status', $subscription->status) === 'active' ? 'selected' : '' }}>Active</option>
                                    <option value="trial" {{ old('status', $subscription->status) === 'trial' ? 'selected' : '' }}>Trial</option>
                                    <option value="canceled" {{ old('status', $subscription->status) === 'canceled' ? 'selected' : '' }}>Canceled</option>
                                    <option value="expired" {{ old('status', $subscription->status) === 'expired' ? 'selected' : '' }}>Expired</option>
                                    <option value="past_due" {{ old('status', $subscription->status) === 'past_due' ? 'selected' : '' }}>Past Due</option>
                                </select>
                                @error('status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-4">
                                <label for="start_date" class="form-label">Start Date *</label>
                                <input type="date" class="form-control @error('start_date') is-invalid @enderror" 
                                       id="start_date" name="start_date" 
                                       value="{{ old('start_date', $subscription->start_date->format('Y-m-d')) }}" required>
                                @error('start_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-4">
                                <label for="end_date" class="form-label">End Date *</label>
                                <input type="date" class="form-control @error('end_date') is-invalid @enderror" 
                                       id="end_date" name="end_date" 
                                       value="{{ old('end_date', $subscription->end_date->format('Y-m-d')) }}" required>
                                @error('end_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Trial and Payment -->
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <label for="trial_ends_at" class="form-label">Trial Ends At</label>
                                <input type="date" class="form-control @error('trial_ends_at') is-invalid @enderror" 
                                       id="trial_ends_at" name="trial_ends_at" 
                                       value="{{ old('trial_ends_at', $subscription->trial_ends_at ? $subscription->trial_ends_at->format('Y-m-d') : '') }}">
                                <small class="text-muted">Leave empty if not a trial</small>
                                @error('trial_ends_at')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-4">
                                <label for="payment_method" class="form-label">Payment Method</label>
                                <select class="form-select @error('payment_method') is-invalid @enderror" 
                                        id="payment_method" name="payment_method">
                                    <option value="">Select Payment Method</option>
                                    <option value="credit_card" {{ old('payment_method', $subscription->payment_method) === 'credit_card' ? 'selected' : '' }}>Credit Card</option>
                                    <option value="bank_transfer" {{ old('payment_method', $subscription->payment_method) === 'bank_transfer' ? 'selected' : '' }}>Bank Transfer</option>
                                    <option value="paypal" {{ old('payment_method', $subscription->payment_method) === 'paypal' ? 'selected' : '' }}>PayPal</option>
                                    <option value="stripe" {{ old('payment_method', $subscription->payment_method) === 'stripe' ? 'selected' : '' }}>Stripe</option>
                                    <option value="manual" {{ old('payment_method', $subscription->payment_method) === 'manual' ? 'selected' : '' }}>Manual</option>
                                </select>
                                @error('payment_method')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-4">
                                <label for="amount_paid" class="form-label">Amount Paid *</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" step="0.01" min="0" 
                                           class="form-control @error('amount_paid') is-invalid @enderror" 
                                           id="amount_paid" name="amount_paid" 
                                           value="{{ old('amount_paid', $subscription->amount_paid ?? $subscription->plan->price) }}" required>
                                </div>
                                @error('amount_paid')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Auto Renew and Cancellation -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="auto_renew" 
                                           name="auto_renew" value="1" 
                                           {{ old('auto_renew', $subscription->auto_renew) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="auto_renew">
                                        <strong>Auto Renew</strong>
                                        <br><small class="text-muted">Automatically renew this subscription when it expires</small>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="cancellation_reason" class="form-label">Cancellation Reason</label>
                                <textarea class="form-control @error('cancellation_reason') is-invalid @enderror" 
                                          id="cancellation_reason" name="cancellation_reason" rows="2"
                                          placeholder="Only fill if subscription is canceled">{{ old('cancellation_reason', $subscription->cancellation_reason) }}</textarea>
                                @error('cancellation_reason')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('super.subscriptions.show', $subscription) }}" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Subscription
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Current Subscription Info -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Current Subscription</h6>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <h5>{{ $subscription->organization->name }}</h5>
                        <div class="mb-2">
                            <span class="badge bg-info">{{ $subscription->plan->name }}</span>
                        </div>
                        <div class="h3 text-primary">${{ number_format($subscription->amount_paid ?? $subscription->plan->price, 2) }}</div>
                        <small class="text-muted">Amount Paid</small>
                        <hr>
                        <div class="text-start">
                            <small class="text-muted">
                                <strong>Current Status:</strong> 
                                @switch($subscription->status)
                                    @case('active')
                                        <span class="badge bg-success">Active</span>
                                        @break
                                    @case('trial')
                                        <span class="badge bg-info">Trial</span>
                                        @break
                                    @case('canceled')
                                        <span class="badge bg-warning">Canceled</span>
                                        @break
                                    @case('expired')
                                        <span class="badge bg-danger">Expired</span>
                                        @break
                                    @case('past_due')
                                        <span class="badge bg-warning">Past Due</span>
                                        @break
                                    @default
                                        <span class="badge bg-secondary">{{ ucfirst($subscription->status) }}</span>
                                @endswitch
                                <br><br>
                                <strong>Period:</strong><br>
                                {{ $subscription->start_date->format('M j, Y') }} - {{ $subscription->end_date->format('M j, Y') }}<br><br>
                                <strong>Auto Renew:</strong> {{ $subscription->auto_renew ? 'Yes' : 'No' }}<br>
                                <strong>Payment:</strong> {{ $subscription->payment_method ? ucfirst(str_replace('_', ' ', $subscription->payment_method)) : 'Not set' }}
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="extendByMonth()">
                            <i class="fas fa-calendar-plus me-2"></i>Extend by 1 Month
                        </button>
                        <button type="button" class="btn btn-outline-success btn-sm" onclick="setActiveStatus()">
                            <i class="fas fa-check me-2"></i>Set as Active
                        </button>
                        <button type="button" class="btn btn-outline-warning btn-sm" onclick="setCanceledStatus()">
                            <i class="fas fa-ban me-2"></i>Set as Canceled
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const organizationSelect = document.getElementById('organization_id');
    const planSelect = document.getElementById('plan_id');
    const statusSelect = document.getElementById('status');
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');
    const amountInput = document.getElementById('amount_paid');
    const autoRenewInput = document.getElementById('auto_renew');
    const paymentMethodSelect = document.getElementById('payment_method');

    // Auto-fill amount when plan changes
    planSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        const planPrice = selectedOption.dataset.price;
        if (planPrice && !amountInput.value) {
            amountInput.value = planPrice;
        }
    });
});

// Quick action functions
function extendByMonth() {
    const endDateInput = document.getElementById('end_date');
    const currentEndDate = new Date(endDateInput.value);
    currentEndDate.setMonth(currentEndDate.getMonth() + 1);
    endDateInput.value = currentEndDate.toISOString().split('T')[0];
}

function setActiveStatus() {
    document.getElementById('status').value = 'active';
    document.getElementById('auto_renew').checked = true;
    document.getElementById('cancellation_reason').value = '';
}

function setCanceledStatus() {
    document.getElementById('status').value = 'canceled';
    document.getElementById('auto_renew').checked = false;
}
</script>
@endsection
