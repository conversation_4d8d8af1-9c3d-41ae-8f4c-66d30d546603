<?php

namespace App\Http\Controllers;

use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Requests\OrderRequest;
use Carbon\Carbon;
use Illuminate\Validation\ValidationException;
use Barryvdh\DomPDF\Facade\Pdf;
use Mike42\Escpos\PrintConnectors\WindowsPrintConnector;
use Mike42\Escpos\Printer;
use App\Notifications\OrderOverdueReminder;
use Illuminate\Support\Facades\Auth;

class OrderController extends Controller
{
    protected $allowedStatusTransitions = [
        'Pending' => ['Processing'],
        'Processing' => ['Completed'],
        'Completed' => ['Delivered'],
        'Delivered' => []
    ];

    public function __construct()
    {
        // Basic access for viewing orders
        $this->middleware('role:Organization Owner|Manager|Account|Staff|Operator|Production|Delivery')->only([
            'index', 'show', 'byStatus', 'delivered', 'overdue'
        ]);

        // Restrict create to Organization Owner and Staff only
        $this->middleware('role:Organization Owner|Staff')->only([
            'create', 'store', 'preview'
        ]);

        // Delivery updates - Organization Owner, Staff and Delivery roles
        $this->middleware('role:Organization Owner|Staff|Delivery')->only([
            'updateDelivery'
        ]);

        // Allow status updates for Organization Owner, Staff, Operator and Production
        $this->middleware('role:Organization Owner|Staff|Operator|Production')->only([
            'updateStatus'
        ]);

        // Payment updates - Organization Owner, Staff and Account
        $this->middleware('role:Organization Owner|Staff|Account')->only([
            'updatePayment'
        ]);
    }

    public function index(Request $request)
    {
        $query = Order::query()->with('user');

        // Filter by organization
        $query->where('organization_id', Auth::user()->organization_id);

        // Filter by branch if user is assigned to a branch
        if (Auth::user()->branch_id) {
            $query->where('branch_id', Auth::user()->branch_id);
        }

        // Apply search filter
        if ($request->has('search')) {
            $searchTerm = $request->search;
            $query->where(function($q) use ($searchTerm) {
                $q->where('order_number', 'like', "%{$searchTerm}%")
                  ->orWhere('customer_name', 'like', "%{$searchTerm}%")
                  ->orWhere('phone_number', 'like', "%{$searchTerm}%")
                  ->orWhere('order_title', 'like', "%{$searchTerm}%")
                  ->orWhere('job_description', 'like', "%{$searchTerm}%");
            });
        }

        // Get paginated results and group by customer
        $orders = $query->latest()->paginate(20);
        $customerOrders = $orders->groupBy('customer_name');

        return view('orders.index', compact('customerOrders', 'orders'));
    }

    public function create()
    {
        $organization = Auth::user()->organization;

        // Check if organization can create more orders
        $canCreateOrder = $organization->canCreateOrder();
        $remainingSlots = $organization->getRemainingOrderSlots();
        $planUsage = $organization->getPlanUsage();

        // If at limit, show warning but still allow access to form
        if (!$canCreateOrder) {
            session()->flash('warning',
                "You have reached your plan limit of {$organization->plan->order_limit} orders this month. " .
                "Please upgrade your plan to create more orders."
            );
        } elseif ($remainingSlots <= 10 && $remainingSlots !== PHP_INT_MAX) {
            session()->flash('info',
                "You have {$remainingSlots} order slots remaining this month in your {$organization->plan->name} plan."
            );
        }

        return view('orders.create', compact('canCreateOrder', 'remainingSlots', 'planUsage'));
    }

    public function store(OrderRequest $request)
    {
        $organization = Auth::user()->organization;

        // Double-check order limits before creating orders
        if (!$organization->canCreateOrder()) {
            return back()
                ->withErrors(['limit_exceeded' => "You have reached your {$organization->plan->name} plan limit of {$organization->plan->order_limit} orders this month. Please upgrade your plan to create more orders."])
                ->withInput()
                ->with('upgrade_required', true);
        }

        DB::beginTransaction();
        try {
            $customerData = $request->only(['customer_name', 'phone_number']);

            // Use the full phone number if provided
            if ($request->has('phone_number_full') && !empty($request->phone_number_full)) {
                $customerData['phone_number'] = $request->phone_number_full;
            }

            $createdOrders = [];

            if (!isset($request->orders) || !is_array($request->orders)) {
                throw new \Exception('Invalid order data submitted');
            }

            // Log initial request data for debugging
            Log::info('Creating multiple orders', [
                'customer' => $customerData,
                'order_count' => count($request->orders),
                'created_by' => auth()->user()->name
            ]);

            // Validate that at least one order exists
            if (empty($request->orders)) {
                throw new ValidationException(validator([], ['orders' => 'required']), [
                    'orders' => ['At least one order is required']
                ]);
            }

            foreach ($request->orders as $index => $orderData) {
                // Log each order data before processing
                Log::info("Processing order #{$index}", [
                    'order_data' => $orderData
                ]);

                // Validate individual order data
                if (!isset($orderData['order_title']) || !isset($orderData['quantity']) || !isset($orderData['unit_cost'])) {
                    throw new ValidationException(validator([], ['order' => 'required']), [
                        'order' => ["Order #{$index} is missing required fields"]
                    ]);
                }

                try {
                    // Format the expected delivery date
                    $orderData['expected_delivery_date'] = Carbon::parse($orderData['expected_delivery_date'])->format('Y-m-d');

                    // Generate unique order number
                    $orderData['order_number'] = Order::generateOrderNumber();

                    // Set initial status
                    $orderData['status'] = 'Pending';

                    // Calculate financial values
                    $quantity = floatval($orderData['quantity']);
                    $unitCost = floatval($orderData['unit_cost']);
                    $amountPaid = floatval($orderData['amount_paid'] ?? 0);
                    $totalAmount = $quantity * $unitCost;

                    // Ensure amount paid doesn't exceed total amount
                    if ($amountPaid > $totalAmount) {
                        throw new \Exception("Amount paid cannot exceed total amount for order #{$index}: {$orderData['order_title']}");
                    }

                    $pendingPayment = $totalAmount - $amountPaid;

                    // Create order with calculated values, customer data, user_id, organization_id and branch_id
                    $order = Order::create([
                        ...$customerData,
                        ...$orderData,
                        'total_amount' => $totalAmount,
                        'pending_payment' => $pendingPayment,
                        'amount_paid' => $amountPaid,
                        'user_id' => auth()->id(),
                        'organization_id' => Auth::user()->organization_id,
                        'branch_id' => Auth::user()->branch_id
                    ]);

                    // Log successful order creation
                    Log::info("Created order #{$index}", [
                        'order_id' => $order->id,
                        'order_number' => $order->order_number
                    ]);

                    $createdOrders[] = $order;

                } catch (\Exception $e) {
                    Log::error("Failed to create order #{$index}", [
                        'error' => $e->getMessage(),
                        'order_data' => $orderData
                    ]);
                    throw $e;
                }
            }

            DB::commit();

            // Log successful transaction completion
            Log::info('Successfully created all orders', [
                'count' => count($createdOrders),
                'order_numbers' => collect($createdOrders)->pluck('order_number')
            ]);

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => count($createdOrders) . ' order(s) created successfully!',
                    'redirect' => route('orders.index')
                ]);
            }

            return redirect()->route('orders.index')
                ->with('success', count($createdOrders) . ' order(s) created successfully!');

        } catch (\Exception $e) {
            DB::rollBack();

            // Log the error with detailed context
            Log::error('Order creation failed', [
                'error' => $e->getMessage(),
                'customer' => $customerData ?? null,
                'orders_count' => count($request->orders ?? [])
            ]);

            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error: ' . $e->getMessage()
                ], 422);
            }

            return back()->withInput()->withErrors(['error' => 'Error: ' . $e->getMessage()]);
        }
    }

    public function show(Order $order)
    {
        return view('orders.show', compact('order'));
    }

    public function updateStatus(Order $order, Request $request)
    {
        try {
            $newStatus = $request->status;

            // Validate status transition
            if (!in_array($newStatus, $this->allowedStatusTransitions[$order->status])) {
                throw new \Exception('Invalid status transition');
            }

            $oldStatus = $order->status;
            $order->status = $newStatus;
            $order->save();

            // Create status history
            $order->statusHistory()->create([
                'old_status' => $oldStatus,
                'new_status' => $newStatus,
                'changed_by' => auth()->user()->name,
                'notes' => $request->notes
            ]);

            return redirect()->route('orders.show', $order)
                ->with('success', 'Order status updated successfully.');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to update status: ' . $e->getMessage());
        }
    }

    public function updateDelivery(Order $order, Request $request)
    {
        try {
            // Check if order has pending payment
            if ($order->pending_payment > 0) {
                return back()->with('error', 'This order has a pending payment of ₦' . number_format($order->pending_payment, 2) . '. Payment must be completed before delivery.');
            }

            $validated = $request->validate([
                'receiver_name' => 'required|string|max:255',
                'receiver_phone' => 'required|string|max:20'
            ]);

            $order->update([
                ...$validated,
                'status' => 'Delivered',
                'date_delivered' => now()
            ]);

            // Create status history
            $order->statusHistory()->create([
                'old_status' => 'Completed',
                'new_status' => 'Delivered',
                'changed_by' => auth()->user()->name,
                'notes' => "Delivered to {$validated['receiver_name']}"
            ]);

            return redirect()->route('orders.show', $order)
                ->with('success', 'Order marked as delivered successfully.');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to update delivery status: ' . $e->getMessage());
        }
    }

    public function updatePayment(Order $order, Request $request)
    {
        try {
            $validated = $request->validate([
                'additional_payment' => "required|numeric|min:0|max:{$order->pending_payment}"
            ]);

            $order->amount_paid += $validated['additional_payment'];
            $order->pending_payment = $order->total_amount - $order->amount_paid;
            $order->save();

            return redirect()->back()->with('success', 'Payment updated successfully');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to update payment: ' . $e->getMessage());
        }
    }

    public function preview(Request $request)
    {
        try {
            $data = $request->validate([
                'customer_name' => 'required|string|max:255',
                'phone_number' => 'required|string|max:20',
                'order_title' => 'required|string|max:255',
                'job_description' => 'required|string',
                'department' => 'required|string',
                'quantity' => 'required|numeric|min:1',
                'unit_cost' => 'required|numeric|min:0',
                'amount_paid' => 'required|numeric|min:0',
                'expected_delivery_date' => 'required|date',
                'expected_delivery_time' => 'required|string'
            ]);

            // Calculate financial values
            $totalAmount = floatval($data['quantity']) * floatval($data['unit_cost']);
            $pendingPayment = $totalAmount - floatval($data['amount_paid']);

            $data['total_amount'] = $totalAmount;
            $data['pending_payment'] = $pendingPayment;

            return response()->json([
                'success' => true,
                'data' => $data
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        }
    }

    public function delivered()
    {
        $deliveredOrders = Order::where('status', 'Delivered')
            ->where('organization_id', Auth::user()->organization_id);

        // Filter by branch if user is assigned to a branch
        if (Auth::user()->branch_id) {
            $deliveredOrders->where('branch_id', Auth::user()->branch_id);
        }

        $deliveredOrders = $deliveredOrders->latest('date_delivered')
            ->paginate(10);

        return view('orders.delivered', compact('deliveredOrders'));
    }

    public function overdue(Request $request)
    {
        $query = Order::overdue()
            ->where('organization_id', Auth::user()->organization_id);

        // Filter by branch if user is assigned to a branch
        if (Auth::user()->branch_id) {
            $query->where('branch_id', Auth::user()->branch_id);
        }

        if ($request->has('search')) {
            $query->search($request->search);
        }

        if ($request->has('duration')) {
            $query->overdueByDuration($request->duration);
        }

        // Sort by most overdue first
        $query->orderByRaw('CONCAT(expected_delivery_date, " ", expected_delivery_time) ASC');

        $overdueOrders = $query->paginate(10)->withQueryString();

        // Get count of overdue orders for this organization
        $overdueCountQuery = Order::overdue()->where('organization_id', Auth::user()->organization_id);
        if (Auth::user()->branch_id) {
            $overdueCountQuery->where('branch_id', Auth::user()->branch_id);
        }
        $overdueCount = $overdueCountQuery->count();

        if ($request->has('export')) {
            return $this->exportOverdueOrders($overdueOrders, $request->export);
        }

        return view('orders.overdue', compact('overdueOrders', 'overdueCount'));
    }

    protected function exportOverdueOrders($orders, $format)
    {
        $fileName = 'overdue-orders-' . now()->format('Y-m-d') . '.' . $format;

        if ($format === 'pdf') {
            $pdf = PDF::loadView('orders.exports.overdue-pdf', compact('orders'));
            return $pdf->download($fileName);
        }

        if ($format === 'excel') {
            $headers = [
                'Order #',
                'Customer',
                'Expected Delivery',
                'Days Overdue',
                'Status',
                'Priority'
            ];

            $data = $orders->map(fn($order) => [
                $order->order_number,
                $order->customer_name,
                $order->expected_delivery_date . ' ' . $order->expected_delivery_time,
                $order->overdue_days,
                $order->status,
                $order->priority_level['level']
            ])->toArray();

            return response()->streamDownload(function() use ($headers, $data) {
                $file = fopen('php://output', 'w');
                fputcsv($file, $headers);
                foreach ($data as $row) {
                    fputcsv($file, $row);
                }
                fclose($file);
            }, $fileName, [
                'Content-Type' => 'text/csv',
            ]);
        }

        abort(400, 'Unsupported export format');
    }

    public function pendingOrders(Request $request)
    {
        // Initialize query with organization filter
        $query = Order::pending()
            ->where('organization_id', Auth::user()->organization_id);

        // Filter by branch if user is assigned to a branch
        if (Auth::user()->branch_id) {
            $query->where('branch_id', Auth::user()->branch_id);
        }

        // Apply search filter if provided
        if ($request->has('search')) {
            $query->search($request->search);
        }

        $pendingOrders = $query->latest()->paginate(10)->withQueryString();

        // Get count of pending orders for this organization
        $pendingCountQuery = Order::pending()->where('organization_id', Auth::user()->organization_id);
        if (Auth::user()->branch_id) {
            $pendingCountQuery->where('branch_id', Auth::user()->branch_id);
        }
        $pendingCount = $pendingCountQuery->count();

        return view('orders.pending', compact('pendingOrders', 'pendingCount'));
    }

    public function printThermal(Order $order)
    {
        try {
            // Get printer name from settings or use default
            $printerName = config('printing.thermal_printer', 'XP-58');
            $connector = new WindowsPrintConnector($printerName);
            $printer = new Printer($connector);

            // Get company settings
            $settings = \App\Models\Setting::where('organization_id', $order->organization_id)->first();
            $companyName = config('app.name', 'Kadmon Printing Company Ltd.');

            // Get branch info if order has a branch
            $branch = null;
            if ($order->branch_id) {
                $branch = \App\Models\Branch::find($order->branch_id);
            }

            // Use branch address if available, otherwise use settings
            $companyAddress = $branch && $branch->address ? $branch->address :
                             ($settings ? $settings->company_address :
                              '304 MELFORD OKILO RD, (OLD MBIAMA-YENAGOA RD.) BY NIKTON JUNCTION, KPANSIA, YENAGOA, BAYELSA STATE, NIGERIA');

            // Use branch phone if available, otherwise use settings
            $companyPhone = $branch && $branch->phone ? $branch->phone :
                           ($settings ? $settings->company_phone :
                            '08065521205, 08147271386');

            // Print header
            $printer->setJustification(Printer::JUSTIFY_CENTER);
            $printer->text($companyName . "\n");
            $printer->text($companyAddress . "\n");
            if ($branch) {
                $printer->text($branch->name . " Branch\n");
            }
            $printer->text("NIGERIA\n\n");
            $printer->text("Sales Receipt\n");
            $printer->text(str_repeat("-", 32) . "\n");

            // Order details
            $printer->setJustification(Printer::JUSTIFY_LEFT);
            $printer->text("Order #: " . $order->order_number . "\n");
            $printer->text("Date: " . $order->created_at->format('M d, Y h:i A') . "\n");
            $printer->text("Customer: " . $order->customer_name . "\n");
            $printer->text(str_repeat("-", 32) . "\n");

            // Order items
            $printer->text("Description:\n");
            $printer->text($order->job_description . "\n");
            $printer->text("Qty: " . $order->quantity . "\n");
            $printer->text("Unit Price: \x{20A6}" . number_format($order->unit_cost, 2) . "\n");
            $printer->text(str_repeat("-", 32) . "\n");

            // Totals
            $printer->setJustification(Printer::JUSTIFY_RIGHT);
            $printer->text("Total Amount: \x{20A6}" . number_format($order->total_amount, 2) . "\n");
            $printer->text("Amount Paid: \x{20A6}" . number_format($order->amount_paid, 2) . "\n");
            if ($order->pending_payment > 0) {
                $printer->text("Balance: \x{20A6}" . number_format($order->pending_payment, 2) . "\n");
            }
            $printer->text(str_repeat("-", 32) . "\n");

            // Footer
            $printer->setJustification(Printer::JUSTIFY_CENTER);
            $printer->text("Thank you for your business!\n\n");
            $printer->text("For Enquiries:\n");
            $printer->text($companyPhone . "\n");
            if ($branch && $branch->email) {
                $printer->text("Email: " . $branch->email . "\n");
            } elseif ($settings && $settings->company_email) {
                $printer->text("Email: " . $settings->company_email . "\n");
            }
            $printer->text("Job Tracking: ***********\n\n");
            $printer->text("Developed By: Kuronicz Tech\n");
            $printer->text("\n\n\n"); // Feed paper to ensure complete cut

            // Cut receipt
            $printer->cut();
            $printer->close();

            return back()->with('success', 'Receipt printed successfully');
        } catch (\Exception $e) {
            Log::error('Thermal printing error: ' . $e->getMessage());
            return back()->with('error', 'Failed to print receipt: ' . $e->getMessage());
        }
    }

    public function generateReceipt($orderIds, $format = 'pdf')
    {
        try {
            // Convert single order ID to array if needed
            $orderIds = is_array($orderIds) ? $orderIds : [$orderIds];

            // Fetch all orders
            $orders = Order::whereIn('id', $orderIds)
                         ->orderBy('created_at')
                         ->get();

            if ($orders->isEmpty()) {
                throw new \Exception('No orders found');
            }

            // Verify all orders belong to the same customer
            $customerName = $orders->first()->customer_name;
            if ($orders->where('customer_name', '!=', $customerName)->isNotEmpty()) {
                throw new \Exception('All orders must belong to the same customer');
            }

            if ($format === 'pdf') {
                $pdf = PDF::loadView('orders.receipt', ['orders' => $orders]);

                // Configure PDF for thermal receipt style with proper encoding
                $pdf->setPaper([0, 0, 226.77, 841.89]); // 80mm × 297mm (A4 height)
                $pdf->setOption('margin-top', 2);
                $pdf->setOption('margin-right', 2);
                $pdf->setOption('margin-bottom', 2);
                $pdf->setOption('margin-left', 2);
                $pdf->setOption('dpi', 203);
                $pdf->setOption('enable-local-file-access', true);
                $pdf->setOption('encoding', 'UTF-8');
                $pdf->setOption('default-font', 'DejaVu Sans');

                return $pdf->download('receipt-' . $customerName . '-' . now()->format('Y-m-d') . '.pdf');
            }

            throw new \Exception('Unsupported format');

        } catch (\Exception $e) {
            Log::error('Receipt generation error: ' . $e->getMessage());
            return back()->with('error', 'Failed to generate receipt: ' . $e->getMessage());
        }
    }

    public function generateReceiptForOrders(Request $request)
    {
        try {
            $orderIds = $request->input('order_ids');
            if (is_string($orderIds)) {
                $orderIds = explode(',', $orderIds);
            }

            if (empty($orderIds)) {
                throw new \Exception('No orders selected');
            }

            $orders = Order::whereIn('id', $orderIds)
                         ->orderBy('created_at')
                         ->get();

            if ($orders->isEmpty()) {
                throw new \Exception('No orders found');
            }

            $customerName = $orders->first()->customer_name;
            if ($orders->where('customer_name', '!=', $customerName)->isNotEmpty()) {
                throw new \Exception('All orders must belong to the same customer');
            }

            $pdf = PDF::loadView('orders.receipt', ['orders' => $orders]);

            // Configure PDF for thermal receipt style
            $pdf->setPaper([0, 0, 226.77, 841.89]); // 80mm × 297mm
            $pdf->setOption('margin-top', 2);
            $pdf->setOption('margin-right', 2);
            $pdf->setOption('margin-bottom', 2);
            $pdf->setOption('margin-left', 2);
            $pdf->setOption('dpi', 203);
            $pdf->setOption('enable-local-file-access', true);
            $pdf->setOption('encoding', 'UTF-8');
            $pdf->setOption('default-font', 'DejaVu Sans');

            return $pdf->download('receipt-' . $customerName . '-' . now()->format('Y-m-d') . '.pdf');

        } catch (\Exception $e) {
            Log::error('Receipt generation error: ' . $e->getMessage(), [
                'order_ids' => $orderIds ?? null
            ]);
            return back()->with('error', 'Failed to generate receipt: ' . $e->getMessage());
        }
    }
}
