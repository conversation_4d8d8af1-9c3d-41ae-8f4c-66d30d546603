@extends('layouts.app')

@section('title', 'Users')

@section('styles')
<link href="/SalesManagementSystem/public/css/sweetalert2.min.css" rel="stylesheet">
<script src="/SalesManagementSystem/public/js/sweetalert2.min.js" defer></script>
<script src="/SalesManagementSystem/public/js/user-management.js?v={{ time() }}" defer></script>
<style>
    .user-stats {
        @apply grid grid-cols-1 md:grid-cols-3 gap-4 mb-6;
    }
    .stat-card {
        @apply bg-white p-4 rounded-lg shadow-sm border border-gray-200;
        transition: all 0.3s ease;
    }
    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    .icon-container {
        transition: all 0.3s ease;
    }
    .stat-card:hover .icon-container {
        transform: scale(1.1);
    }
    .stat-value {
        @apply text-2xl font-bold text-primary;
        background: linear-gradient(120deg, #4F46E5 0%, #7C3AED 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        transition: all 0.3s ease;
    }
    .stat-card:hover .stat-value {
        transform: scale(1.05);
    }
    .stat-label {
        @apply text-sm text-gray-600;
    }
    .filter-section {
        @apply flex flex-wrap items-center gap-4 mb-6;
    }
    .add-user-btn {
        transition: all 0.2s ease;
    }
    .add-user-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
    .add-user-btn:active {
        transform: translateY(0);
    }
    .search-input, .filter-select {
        transition: border-color 0.2s ease;
    }
    .search-input:focus, .filter-select:focus {
        border-color: #4F46E5;
        box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
    }
    .filter-select:focus {
        border-color: #4F46E5;
        box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
    }
    .stat-card {
        transition: transform 0.3s ease;
    }
    .stat-card:hover {
        transform: translateY(-5px);
    }
    .icon-container {
        transition: transform 0.3s ease;
    }
    .stat-card:hover .icon-container {
        transform: scale(1.1);
    }
</style>
@endsection

@section('content')
<div class="py-12" x-data="{
    activeTab: localStorage.getItem('userActiveTab') || 'active',
    setActiveTab(tab) {
        this.activeTab = tab;
        localStorage.setItem('userActiveTab', tab);
    }
}">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- User Statistics Cards -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
            <!-- Active Users Card -->
            <div class="bg-white rounded-lg shadow-sm p-6 border-l-4 border-green-500 stat-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Active Users</p>
                        <p class="text-3xl font-bold text-gray-900 stat-value" data-value="{{ $activeCount }}">0</p>
                    </div>
                    <div class="bg-green-100 rounded-full p-3 icon-container">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Inactive Users Card -->
            <div class="bg-white rounded-lg shadow-sm p-6 border-l-4 border-red-500 stat-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Inactive Users</p>
                        <p class="text-3xl font-bold text-gray-900 stat-value" data-value="{{ $inactiveCount }}">0</p>
                    </div>
                    <div class="bg-red-100 rounded-full p-3 icon-container">
                        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m0 0v2m0-2h2m-2 0H10m5-6a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Total Users Card -->
            <div class="bg-white rounded-lg shadow-sm p-6 border-l-4 border-blue-500 stat-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Total Users</p>
                        <p class="text-3xl font-bold text-gray-900 stat-value" data-value="{{ $totalUsers }}">0</p>
                    </div>
                    <div class="bg-blue-100 rounded-full p-3 icon-container">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- Header with Add User Button -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div class="flex justify-between items-center">
                <h2 class="text-xl font-semibold text-gray-800">User Management</h2>
                <a href="{{ route('users.create') }}"
                    class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg inline-flex items-center transition duration-150 ease-in-out add-user-btn">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                    </svg>
                    Add New User
                </a>
            </div>
        </div>

        <!-- Tab Navigation -->
        <div class="mb-6">
            <div class="border-b border-gray-200">
                <nav class="flex -mb-px">
                    <button @click="setActiveTab('active')" :class="{ 'border-primary text-primary': activeTab === 'active', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'active' }"
                            class="whitespace-nowrap py-4 px-4 border-b-2 font-medium text-sm">
                        Active Users ({{ $activeCount }})
                    </button>
                    <button @click="setActiveTab('inactive')" :class="{ 'border-yellow-500 text-yellow-500': activeTab === 'inactive', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'inactive' }"
                            class="whitespace-nowrap py-4 px-4 border-b-2 font-medium text-sm">
                        Inactive Users ({{ $inactiveCount }})
                    </button>
                    <button @click="setActiveTab('archived')" :class="{ 'border-gray-500 text-gray-500': activeTab === 'archived', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'archived' }"
                            class="whitespace-nowrap py-4 px-4 border-b-2 font-medium text-sm">
                        Archived Users ({{ $archivedCount }})
                    </button>
                </nav>
            </div>
        </div>

        <!-- Search and Filters -->
        <div class="mb-4">
            <div class="flex flex-col sm:flex-row gap-4">
                <div class="flex-1">
                    <input type="text"
                           id="userSearch"
                           placeholder="Search users..."
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 search-input">
                </div>
                <div class="flex flex-col sm:flex-row gap-4 sm:items-center">
                    <select id="roleFilter"
                            class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 filter-select">
                        <option value="">All Roles</option>
                        @foreach($roles as $role)
                            <option value="{{ $role->id }}">{{ $role->name }}</option>
                        @endforeach
                    </select>
                    <select id="statusFilter"
                            class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 filter-select">
                        <option value="">All Status</option>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                    </select>
                    <button onclick="clearFilters()"
                            class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors duration-200">
                        Clear Filters
                    </button>
                </div>
            </div>
        </div>

        <!-- Users Table -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6 text-gray-900">
                @if(session('success'))
                    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4 alert-dismissible" role="alert">
                        <span class="block sm:inline">{{ session('success') }}</span>
                        <button type="button" class="absolute top-0 right-0 px-4 py-3" onclick="this.parentElement.remove()">
                            <span class="sr-only">Close</span>
                            <svg class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z"/>
                            </svg>
                        </button>
                    </div>
                @endif

                <div class="overflow-x-auto">
                    <!-- Active Users Table -->
                    <div x-show="activeTab === 'active'">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Roles</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Branch</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                                @foreach($activeUsers as $user)
                                <tr data-user-id="{{ $user->id }}" class="bg-white hover:bg-gray-50">
                                        @include('users.partials.user-row', ['user' => $user])
                                </tr>
                            @endforeach
                        </tbody>
                    </table>

                        @if($activeUsers->isEmpty())
                    <div class="text-center py-12 empty-state">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"/>
                        </svg>
                                <h3 class="mt-2 text-sm font-medium text-gray-900">No active users found</h3>
                        <p class="mt-1 text-sm text-gray-500">Get started by creating a new user.</p>
                        <div class="mt-6">
                            <a href="{{ route('users.create') }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                                </svg>
                                Create New User
                            </a>
                        </div>
                    </div>
                @endif
                    </div>

                    <!-- Inactive Users Table -->
                    <div x-show="activeTab === 'inactive'" x-cloak>
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Roles</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Branch</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($inactiveUsers as $user)
                                    <tr data-user-id="{{ $user->id }}" class="bg-white hover:bg-gray-50">
                                        @include('users.partials.user-row', ['user' => $user])
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>

                        @if($inactiveUsers->isEmpty())
                            <div class="text-center py-12 empty-state">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4c-4.4 0-8 3.6-8 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8zm0 14c-3.3 0-6-2.7-6-6s2.7-6 6-6 6 2.7 6 6-2.7 6-6 6zm0-9v3m0 3h.01" />
                        </svg>
                                <h3 class="mt-2 text-sm font-medium text-gray-900">No inactive users found</h3>
                                <p class="mt-1 text-sm text-gray-500">All users are currently active.</p>
                            </div>
                        @endif
                    </div>

                    <!-- Archived Users Table -->
                    <div x-show="activeTab === 'archived'" x-cloak>
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Roles</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Branch</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($archivedUsers as $user)
                                    <tr data-user-id="{{ $user->id }}" class="bg-white hover:bg-gray-50">
                                        @include('users.partials.user-row', ['user' => $user])
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>

                        @if($archivedUsers->isEmpty())
                            <div class="text-center py-12 empty-state">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-gray-900">No archived users found</h3>
                                <p class="mt-1 text-sm text-gray-500">You have not archived any users yet.</p>
                        </div>
                        @endif
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('alpine:init', () => {
    Alpine.store('tabState', {
        activeTab: localStorage.getItem('userActiveTab') || 'active',
        setActiveTab(tab) {
            this.activeTab = tab;
            localStorage.setItem('userActiveTab', tab);
        }
    });
});

(function() {
    // Check for requestAnimationFrame support and provide fallback
    const requestAF = window.requestAnimationFrame ||
        window.webkitRequestAnimationFrame ||
        window.mozRequestAnimationFrame ||
        (callback => setTimeout(callback, 1000 / 60));

    document.addEventListener('DOMContentLoaded', function() {
        // Debounce animation start to prevent performance issues
        let timeout;
        clearTimeout(timeout);
        timeout = setTimeout(() => {
            const stats = document.querySelectorAll('.stat-value');
            stats.forEach(stat => {
                try {
                    const finalValue = Math.max(0, parseInt(stat.dataset.value) || 0);
                    stat.textContent = '0';
                    // Add small delay between animations to prevent CPU spikes
                    setTimeout(() => {
                        animateValue(stat, finalValue);
                    }, 100 * Math.random()); // Stagger animations slightly
                } catch (error) {
                    console.error('Animation error:', error);
                    stat.textContent = stat.dataset.value || '0';
                }
            });
        }, 100);
    });

    function easeOutQuad(t) {
        return t * (2 - t);
    }

    function animateValue(element, endValue) {
        if (!element || typeof endValue !== 'number') return;
        if (endValue === 0) {
            element.textContent = '0';
            return;
        }

        const duration = 1500;
        const startTime = performance.now();
        let lastTimestamp = 0;

        function updateValue(currentTime) {
            // Throttle updates to prevent excessive redraws
            if (currentTime - lastTimestamp < 16) { // ~60fps
                requestAF(() => updateValue(currentTime));
                return;
            }

            lastTimestamp = currentTime;
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            const easedProgress = easeOutQuad(progress);
            const current = Math.round(easedProgress * endValue);

            if (element.textContent !== current.toString()) {
                element.textContent = current;
            }

            if (progress < 1) {
                requestAF(updateValue);
            } else {
                element.textContent = endValue;
            }
        }

        requestAF(updateValue);
    }
})();
</script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animate numbers in statistics
    const stats = document.querySelectorAll('.stat-value');
    stats.forEach(stat => {
        try {
            const finalValue = Math.max(0, parseInt(stat.dataset.value) || 0); // Ensure non-negative
            stat.textContent = '0'; // Set initial value
            requestAnimationFrame(() => {
                animateValue(stat, finalValue);
            });
        } catch (error) {
            console.error('Animation error:', error);
            stat.textContent = stat.dataset.value || '0';
        }
    });
});

function easeOutQuad(t) {
    return t * (2 - t);
}

function animateValue(element, endValue) {
    if (!element || typeof endValue !== 'number') return;
    if (endValue === 0) {
        element.textContent = '0';
        return;
    }

    const duration = 1500; // slightly longer duration for smoother animation
    const startTime = performance.now();

    function updateValue(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // Apply easing function for smoother animation
        const easedProgress = easeOutQuad(progress);
        const current = Math.round(easedProgress * endValue);

        element.textContent = current;

        if (progress < 1) {
            requestAnimationFrame(updateValue);
        } else {
            element.textContent = endValue;
        }
    }

    requestAnimationFrame(updateValue);
}

// Existing search and filter functionality
const userSearch = document.getElementById('userSearch');
const roleFilter = document.getElementById('roleFilter');
const statusFilter = document.getElementById('statusFilter');

function filterUsers() {
    const searchTerm = userSearch.value.toLowerCase();
    const selectedRole = roleFilter.value;
    const selectedStatus = statusFilter.value;
    const userRows = document.querySelectorAll('tbody tr');

    userRows.forEach(row => {
        const name = row.querySelector('td:nth-child(1)').textContent.toLowerCase();
        const email = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
        const roles = Array.from(row.querySelectorAll('.role-badge')).map(badge => badge.dataset.roleId);
        const status = row.querySelector('.status-badge').dataset.status;

        const matchesSearch = name.includes(searchTerm) || email.includes(searchTerm);
        const matchesRole = !selectedRole || roles.includes(selectedRole);
        const matchesStatus = !selectedStatus || status === selectedStatus;

        row.style.display = matchesSearch && matchesRole && matchesStatus ? '' : 'none';
    });

    updateEmptyState();
}

function updateEmptyState() {
    const visibleRows = Array.from(document.querySelectorAll('tbody tr')).filter(row => row.style.display !== 'none');
    const emptyState = document.querySelector('.empty-state');

    if (visibleRows.length === 0 && emptyState) {
        emptyState.style.display = '';
    } else if (emptyState) {
        emptyState.style.display = 'none';
    }
}

// Add event listeners
userSearch.addEventListener('input', filterUsers);
roleFilter.addEventListener('change', filterUsers);
statusFilter.addEventListener('change', filterUsers);

// Initialize auto-hide for alerts
setTimeout(() => {
    document.querySelectorAll('.alert-dismissible').forEach(alert => {
        alert.classList.add('opacity-0');
        setTimeout(() => alert.remove(), 300);
    });
}, 5000);

function clearFilters() {
    document.getElementById('userSearch').value = '';
    document.getElementById('roleFilter').value = '';
    document.getElementById('statusFilter').value = '';
    filterUsers();
}

// Remove duplicate confirmDelete function since it's handled by UserManagement.js

function toggleUserStatus(userId) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `/users/${userId}/toggle-status`;
    const csrfToken = document.querySelector('meta[name="csrf-token"]').content;

    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = '_token';
    csrfInput.value = csrfToken;

    const methodInput = document.createElement('input');
    methodInput.type = 'hidden';
    methodInput.name = '_method';
    methodInput.value = 'PATCH';

    form.appendChild(csrfInput);
    form.appendChild(methodInput);
    document.body.appendChild(form);
    form.submit();
}

// Add event listeners
document.getElementById('userSearch').addEventListener('input', filterUsers);
document.getElementById('roleFilter').addEventListener('change', filterUsers);
document.getElementById('statusFilter').addEventListener('change', filterUsers);
</script>
@endpush

@endsection
