// ...existing code...

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const orderStatus = '{{ $order->status }}';

    form.addEventListener('submit', function(e) {
        if (orderStatus !== 'Pending') {
            e.preventDefault();
            Swal.fire({
                icon: 'error',
                title: 'Cannot Edit Order',
                text: 'This order cannot be modified because it has already been submitted.'
            });
            return false;
        }
    });
});
</script>
@endpush

// ...existing code...
