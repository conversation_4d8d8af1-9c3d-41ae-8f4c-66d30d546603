<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use App\Models\User;

class LastActiveAdmin implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $user = request()->route('user');
        
        if ($value === 'inactive' && $user && $user->hasRole('Organization Owner')) {
            // Count active Organization Owners
            $activeOwnerCount = User::whereHas('roles', function($query) {
                $query->where('name', 'Organization Owner');
            })->where('status', 'active')->count();

            if ($activeOwnerCount <= 1) {
                $fail('Cannot deactivate the last active Organization Owner.');
            }
        }
    }
}