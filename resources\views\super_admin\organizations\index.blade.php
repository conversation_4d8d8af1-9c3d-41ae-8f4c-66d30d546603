@extends('super_admin.layouts.app')

@section('title', 'Organizations')
@section('page-title', 'Organizations')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Organizations</h1>
            <p class="text-muted">Manage all organizations in the system</p>
        </div>
        <a href="{{ route('super.organizations.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Add Organization
        </a>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('super.organizations.index') }}" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ request('search') }}" placeholder="Search organizations...">
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Statuses</option>
                        <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                        <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="plan" class="form-label">Plan</label>
                    <select class="form-select" id="plan" name="plan">
                        <option value="">All Plans</option>
                        @foreach($plans as $plan)
                            <option value="{{ $plan->id }}" {{ request('plan') == $plan->id ? 'selected' : '' }}>
                                {{ $plan->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-search"></i> Filter
                    </button>
                    <a href="{{ route('super.organizations.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> Clear
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Organizations Table -->
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                Organizations ({{ $organizations->total() }})
            </h6>
        </div>
        <div class="card-body">
            @if($organizations->count() > 0)
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Organization</th>
                                <th>Plan</th>
                                <th>Users</th>
                                <th>Branches</th>
                                <th>Subscriptions</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($organizations as $organization)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if($organization->logo)
                                                <img src="{{ asset('storage/logos/' . $organization->logo) }}" 
                                                     alt="{{ $organization->name }}" 
                                                     class="rounded-circle me-3" width="40" height="40">
                                            @else
                                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" 
                                                     style="width: 40px; height: 40px;">
                                                    <span class="text-white font-weight-bold">
                                                        {{ substr($organization->name, 0, 1) }}
                                                    </span>
                                                </div>
                                            @endif
                                            <div>
                                                <div class="font-weight-bold">{{ $organization->name }}</div>
                                                <small class="text-muted">{{ $organization->email }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        @if($organization->plan)
                                            <span class="badge bg-info">{{ $organization->plan->name }}</span>
                                            <br><small class="text-muted">${{ number_format($organization->plan->price, 2) }}/month</small>
                                        @else
                                            <span class="badge bg-secondary">No Plan</span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ $organization->users_count }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">{{ $organization->branches_count }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning">{{ $organization->subscriptions_count }}</span>
                                    </td>
                                    <td>
                                        @if($organization->is_active)
                                            <span class="badge bg-success">Active</span>
                                        @else
                                            <span class="badge bg-danger">Inactive</span>
                                        @endif
                                    </td>
                                    <td>
                                        <small>{{ $organization->created_at->format('M j, Y') }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('super.organizations.show', $organization) }}" 
                                               class="btn btn-sm btn-outline-primary" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('super.organizations.edit', $organization) }}" 
                                               class="btn btn-sm btn-outline-secondary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            @if($organization->is_active)
                                                <form method="POST" action="{{ route('super.organizations.deactivate', $organization) }}" 
                                                      class="d-inline">
                                                    @csrf
                                                    <button type="submit" class="btn btn-sm btn-outline-warning" 
                                                            title="Deactivate" onclick="return confirm('Are you sure?')">
                                                        <i class="fas fa-pause"></i>
                                                    </button>
                                                </form>
                                            @else
                                                <form method="POST" action="{{ route('super.organizations.activate', $organization) }}" 
                                                      class="d-inline">
                                                    @csrf
                                                    <button type="submit" class="btn btn-sm btn-outline-success" 
                                                            title="Activate">
                                                        <i class="fas fa-play"></i>
                                                    </button>
                                                </form>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div>
                        Showing {{ $organizations->firstItem() }} to {{ $organizations->lastItem() }} 
                        of {{ $organizations->total() }} results
                    </div>
                    {{ $organizations->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-building fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No organizations found</h5>
                    <p class="text-muted">Get started by creating your first organization.</p>
                    <a href="{{ route('super.organizations.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add Organization
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
