@extends('affiliate.layouts.auth')

@section('title', 'Join Affiliate Program')
@section('header-title', 'Join Our Affiliate Program')
@section('header-subtitle', 'Start earning commissions by referring new customers')

@section('content')
@if($referralData)
    <div class="alert alert-info" role="alert">
        <i class="fas fa-info-circle me-2"></i>
        <strong>Referral Detected!</strong> You were referred by an affiliate. Complete your registration to help them earn a commission.
    </div>
@endif

<form method="POST" action="{{ route('affiliate.register') }}">
    @csrf

    <!-- Personal Information -->
    <div class="mb-4">
        <h5 class="text-primary mb-3">
            <i class="fas fa-user me-2"></i>
            Personal Information
        </h5>

        <div class="mb-3">
            <label for="name" class="form-label">Full Name</label>
            <input type="text"
                   class="form-control @error('name') is-invalid @enderror"
                   id="name"
                   name="name"
                   value="{{ old('name') }}"
                   required
                   autofocus
                   placeholder="Enter your full name">
            @error('name')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <div class="mb-3">
            <label for="email" class="form-label">Email Address</label>
            <input type="email"
                   class="form-control @error('email') is-invalid @enderror"
                   id="email"
                   name="email"
                   value="{{ old('email') }}"
                   required
                   placeholder="Enter your email address">
            @error('email')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <div class="mb-3">
            <label for="phone" class="form-label">Phone Number</label>
            <input type="tel"
                   class="form-control @error('phone') is-invalid @enderror"
                   id="phone"
                   name="phone"
                   value="{{ old('phone') }}"
                   required
                   placeholder="Enter your phone number">
            <input type="hidden" id="phone_full" name="phone_full" value="{{ old('phone_full') }}">
            @error('phone')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
            @error('phone_full')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <div class="row">
            <div class="col-md-6 mb-3">
                <label for="password" class="form-label">Password</label>
                <input type="password"
                       class="form-control @error('password') is-invalid @enderror"
                       id="password"
                       name="password"
                       required
                       placeholder="Enter password">
                @error('password')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="col-md-6 mb-3">
                <label for="password_confirmation" class="form-label">Confirm Password</label>
                <input type="password"
                       class="form-control @error('password_confirmation') is-invalid @enderror"
                       id="password_confirmation"
                       name="password_confirmation"
                       required
                       placeholder="Confirm password">
                @error('password_confirmation')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
        </div>
    </div>

    <!-- Additional Information -->
    <div class="mb-4">
        <h5 class="text-primary mb-3">
            <i class="fas fa-info-circle me-2"></i>
            Additional Information (Optional)
        </h5>

        <div class="mb-3">
            <label for="website" class="form-label">Website URL</label>
            <input type="url"
                   class="form-control @error('website') is-invalid @enderror"
                   id="website"
                   name="website"
                   value="{{ old('website') }}"
                   placeholder="https://example.com">
            @error('website')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <div class="mb-3">
            <label for="social_media" class="form-label">Social Media Profile</label>
            <input type="text"
                   class="form-control @error('social_media') is-invalid @enderror"
                   id="social_media"
                   name="social_media"
                   value="{{ old('social_media') }}"
                   placeholder="e.g., @username or profile URL">
            @error('social_media')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <div class="mb-3">
            <label for="bio" class="form-label">Brief Bio</label>
            <textarea id="bio"
                      name="bio"
                      rows="3"
                      class="form-control @error('bio') is-invalid @enderror"
                      placeholder="Tell us about yourself and how you plan to promote our services">{{ old('bio') }}</textarea>
            @error('bio')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
    </div>

    <!-- Payment Information -->
    <div class="mb-4">
        <h5 class="text-primary mb-3">
            <i class="fas fa-credit-card me-2"></i>
            Payment Information
        </h5>

        <div class="mb-3">
            <label for="payment_method" class="form-label">Payment Method</label>
            <input type="hidden" name="payment_method" value="bank_transfer">
            <div class="alert alert-info">
                <i class="fas fa-university me-2"></i>
                <strong>Bank Transfer</strong> - All affiliate payments are processed via bank transfer for security and compliance.
            </div>
        </div>

        <!-- Bank Transfer Fields -->
        <div id="bank_fields" class="d-none">
            <div class="mb-3">
                <label for="bank_name" class="form-label">Bank Name</label>
                <input type="text"
                       class="form-control @error('bank_name') is-invalid @enderror"
                       id="bank_name"
                       name="bank_name"
                       value="{{ old('bank_name') }}"
                       placeholder="Enter bank name">
                @error('bank_name')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="mb-3">
                <label for="account_name" class="form-label">Account Holder Name</label>
                <input type="text"
                       class="form-control @error('account_name') is-invalid @enderror"
                       id="account_name"
                       name="account_name"
                       value="{{ old('account_name') }}"
                       placeholder="Enter account holder name">
                @error('account_name')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="mb-3">
                <label for="account_number" class="form-label">Account Number</label>
                <input type="text"
                       class="form-control @error('account_number') is-invalid @enderror"
                       id="account_number"
                       name="account_number"
                       value="{{ old('account_number') }}"
                       placeholder="Enter account number">
                @error('account_number')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="mb-3">
                <label for="routing_number" class="form-label">Routing Number (Optional)</label>
                <input type="text"
                       class="form-control @error('routing_number') is-invalid @enderror"
                       id="routing_number"
                       name="routing_number"
                       value="{{ old('routing_number') }}"
                       placeholder="Enter routing number">
                @error('routing_number')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
        </div>


    </div>

    <!-- Terms and Conditions -->
    <div class="mb-4">
        <div class="form-check">
            <input type="checkbox"
                   class="form-check-input @error('terms_accepted') is-invalid @enderror"
                   id="terms_accepted"
                   name="terms_accepted"
                   value="1"
                   {{ old('terms_accepted') ? 'checked' : '' }}
                   required>
            <label for="terms_accepted" class="form-check-label">
                I agree to the <a href="#" class="text-primary">Affiliate Program Terms and Conditions</a>
            </label>
            @error('terms_accepted')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
    </div>

    <!-- Commission Information -->
    <div class="alert alert-success mb-4">
        <h6 class="alert-heading">
            <i class="fas fa-percentage me-2"></i>Commission Structure
        </h6>
        <ul class="mb-0">
            <li>Earn {{ number_format($settings->default_commission_rate, 1) }}% commission on each successful referral</li>
            <li>Minimum withdrawal amount: ${{ number_format($settings->minimum_withdrawal, 2) }}</li>
            <li>Monthly payouts for approved earnings</li>
            <li>Real-time tracking of your referrals and earnings</li>
        </ul>
    </div>

    <!-- Submit Button -->
    <div class="d-flex justify-content-between align-items-center">
        <a href="{{ route('affiliate.login') }}" class="text-muted">
            Already have an account? Sign in
        </a>
        <button type="submit" class="btn btn-primary btn-lg">
            <i class="fas fa-user-plus me-2"></i>Join Affiliate Program
        </button>
    </div>
</form>

@push('styles')
<style>
    /* Custom styles for intl-tel-input to match Bootstrap form design */
    .iti {
        width: 100%;
    }
    .iti__flag-container {
        display: flex;
    }
    .iti__selected-flag {
        border-radius: 0.375rem 0 0 0.375rem;
        background-color: #fff;
        border: 1px solid #ced4da;
        border-right: none;
        height: calc(1.5em + 0.75rem + 2px);
    }
    .iti--allow-dropdown input {
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
    }
    .iti__country-list {
        z-index: 1050;
    }
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize international telephone input
    const phoneInputField = document.querySelector("#phone");
    const phoneInput = window.intlTelInput(phoneInputField, {
        utilsScript: "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/js/utils.js",
        initialCountry: "auto",
        geoIpLookup: function(callback) {
            fetch("https://ipapi.co/json")
              .then(function(res) { return res.json(); })
              .then(function(data) { callback(data.country_code); })
              .catch(function() { callback("us"); });
        },
        preferredCountries: ["us", "gb", "ca", "ng"],
        separateDialCode: true,
        formatOnDisplay: true,
    });

    // Show bank fields since bank transfer is the only payment method
    const bankFields = document.getElementById('bank_fields');
    if (bankFields) {
        bankFields.classList.remove('d-none');
    }

    // Store the full number with country code when submitting the form
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function() {
            const fullNumber = phoneInput.getNumber();
            document.getElementById('phone_full').value = fullNumber;
        });
    }
});
</script>
@endpush
@endsection
