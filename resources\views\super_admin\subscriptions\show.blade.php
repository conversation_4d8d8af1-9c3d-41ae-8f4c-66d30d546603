@extends('super_admin.layouts.app')

@section('title', 'Subscription Details')
@section('page-title', 'Subscription Details')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Subscription Details</h1>
            <p class="text-muted">{{ $subscription->organization->name }} - {{ $subscription->plan->name }}</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('super.subscriptions.edit', $subscription) }}" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i>Edit Subscription
            </a>
            <a href="{{ route('super.subscriptions.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Subscriptions
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Subscription Details -->
        <div class="col-lg-8">
            <!-- Basic Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Subscription Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>{{ $subscription->organization->name }}</h5>
                            <p class="text-muted mb-1">{{ $subscription->organization->email }}</p>
                            @if($subscription->organization->phone)
                                <p class="text-muted mb-1">{{ $subscription->organization->phone }}</p>
                            @endif
                            @if($subscription->organization->address)
                                <p class="text-muted">{{ $subscription->organization->address }}</p>
                            @endif
                        </div>
                        <div class="col-md-6 text-md-end">
                            <div class="mb-2">
                                <span class="badge bg-info fs-6">{{ $subscription->plan->name }}</span>
                            </div>
                            <div class="h3 text-primary">${{ number_format($subscription->amount_paid ?? $subscription->plan->price, 2) }}</div>
                            <small class="text-muted">Amount Paid</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Subscription Status -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Status & Timeline</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            @switch($subscription->status)
                                @case('active')
                                    <div class="h4 text-success">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <span class="badge bg-success">Active</span>
                                    @break
                                @case('trial')
                                    <div class="h4 text-info">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <span class="badge bg-info">Trial</span>
                                    @break
                                @case('canceled')
                                    <div class="h4 text-warning">
                                        <i class="fas fa-ban"></i>
                                    </div>
                                    <span class="badge bg-warning">Canceled</span>
                                    @break
                                @case('expired')
                                    <div class="h4 text-danger">
                                        <i class="fas fa-times-circle"></i>
                                    </div>
                                    <span class="badge bg-danger">Expired</span>
                                    @break
                                @case('past_due')
                                    <div class="h4 text-warning">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </div>
                                    <span class="badge bg-warning">Past Due</span>
                                    @break
                                @default
                                    <div class="h4 text-secondary">
                                        <i class="fas fa-question-circle"></i>
                                    </div>
                                    <span class="badge bg-secondary">{{ ucfirst($subscription->status) }}</span>
                            @endswitch
                            <div class="mt-2">
                                <small class="text-muted">Current Status</small>
                            </div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="h5 text-info">{{ $subscription->start_date->format('M j, Y') }}</div>
                            <small class="text-muted">Start Date</small>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="h5 {{ $subscription->end_date->isPast() ? 'text-danger' : 'text-success' }}">
                                {{ $subscription->end_date->format('M j, Y') }}
                            </div>
                            <small class="text-muted">End Date</small>
                            @if($subscription->end_date->isPast() && $subscription->status === 'active')
                                <br><small class="text-danger">Overdue</small>
                            @endif
                        </div>
                        <div class="col-md-3 text-center">
                            @if($subscription->trial_ends_at)
                                <div class="h5 {{ $subscription->trial_ends_at->isPast() ? 'text-danger' : 'text-warning' }}">
                                    {{ $subscription->trial_ends_at->format('M j, Y') }}
                                </div>
                                <small class="text-muted">Trial Ends</small>
                            @else
                                <div class="h5 text-muted">-</div>
                                <small class="text-muted">No Trial</small>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Payment Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <strong>Payment Method:</strong>
                            <div class="mt-1">
                                @if($subscription->payment_method)
                                    <span class="badge bg-secondary">{{ ucfirst(str_replace('_', ' ', $subscription->payment_method)) }}</span>
                                @else
                                    <span class="text-muted">Not set</span>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-4">
                            <strong>Auto Renew:</strong>
                            <div class="mt-1">
                                @if($subscription->auto_renew)
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i>Enabled
                                    </span>
                                @else
                                    <span class="badge bg-secondary">
                                        <i class="fas fa-times me-1"></i>Disabled
                                    </span>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-4">
                            <strong>Plan Price:</strong>
                            <div class="mt-1">
                                <span class="h6 text-primary">${{ number_format($subscription->plan->price, 2) }}</span>
                                <small class="text-muted">/month</small>
                            </div>
                        </div>
                    </div>

                    @if($subscription->cancellation_reason)
                        <hr>
                        <div>
                            <strong>Cancellation Reason:</strong>
                            <div class="mt-1">
                                <div class="alert alert-warning">
                                    {{ $subscription->cancellation_reason }}
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Plan Features -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Plan Features</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Limits:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-building text-info me-2"></i>
                                    {{ $subscription->plan->branch_limit == 999 ? 'Unlimited' : $subscription->plan->branch_limit }} Branches
                                </li>
                                <li><i class="fas fa-users text-success me-2"></i>
                                    {{ $subscription->plan->user_limit == 999 ? 'Unlimited' : $subscription->plan->user_limit }} Users
                                </li>
                                <li><i class="fas fa-database text-warning me-2"></i>
                                    {{ $subscription->plan->data_retention_days == 999 ? 'Forever' : $subscription->plan->data_retention_days . ' Days' }} Data Retention
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Features:</h6>
                            <ul class="list-unstyled">
                                <li>
                                    <i class="fas fa-print me-2 {{ $subscription->plan->thermal_printing ? 'text-success' : 'text-muted' }}"></i>
                                    Thermal Printing
                                    @if($subscription->plan->thermal_printing)
                                        <i class="fas fa-check text-success ms-1"></i>
                                    @endif
                                </li>
                                <li>
                                    <i class="fas fa-chart-bar me-2 {{ $subscription->plan->advanced_reporting ? 'text-success' : 'text-muted' }}"></i>
                                    Advanced Reporting
                                    @if($subscription->plan->advanced_reporting)
                                        <i class="fas fa-check text-success ms-1"></i>
                                    @endif
                                </li>
                                <li>
                                    <i class="fas fa-code me-2 {{ $subscription->plan->api_access ? 'text-success' : 'text-muted' }}"></i>
                                    API Access
                                    @if($subscription->plan->api_access)
                                        <i class="fas fa-check text-success ms-1"></i>
                                    @endif
                                </li>
                                <li>
                                    <i class="fas fa-tag me-2 {{ $subscription->plan->white_label ? 'text-success' : 'text-muted' }}"></i>
                                    White Label
                                    @if($subscription->plan->white_label)
                                        <i class="fas fa-check text-success ms-1"></i>
                                    @endif
                                </li>
                                <li>
                                    <i class="fas fa-palette me-2 {{ $subscription->plan->custom_branding ? 'text-success' : 'text-muted' }}"></i>
                                    Custom Branding
                                    @if($subscription->plan->custom_branding)
                                        <i class="fas fa-check text-success ms-1"></i>
                                    @endif
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions Sidebar -->
        <div class="col-lg-4">
            <!-- Quick Actions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        @if($subscription->status === 'active')
                            <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#cancelModal">
                                <i class="fas fa-ban me-2"></i>Cancel Subscription
                            </button>
                        @elseif($subscription->status === 'canceled')
                            <form method="POST" action="{{ route('super.subscriptions.reactivate', $subscription) }}">
                                @csrf
                                <button type="submit" class="btn btn-success w-100" 
                                        onclick="return confirm('Reactivate this subscription?')">
                                    <i class="fas fa-play me-2"></i>Reactivate Subscription
                                </button>
                            </form>
                        @endif
                        
                        <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#extendModal">
                            <i class="fas fa-calendar-plus me-2"></i>Extend Subscription
                        </button>
                        
                        <a href="{{ route('super.subscriptions.edit', $subscription) }}" class="btn btn-outline-primary">
                            <i class="fas fa-edit me-2"></i>Edit Details
                        </a>
                    </div>
                </div>
            </div>

            <!-- Subscription Timeline -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Timeline</h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Subscription Created</h6>
                                <small class="text-muted">{{ $subscription->created_at->format('M j, Y g:i A') }}</small>
                            </div>
                        </div>
                        
                        @if($subscription->trial_ends_at)
                            <div class="timeline-item">
                                <div class="timeline-marker {{ $subscription->trial_ends_at->isPast() ? 'bg-warning' : 'bg-info' }}"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">Trial {{ $subscription->trial_ends_at->isPast() ? 'Ended' : 'Ends' }}</h6>
                                    <small class="text-muted">{{ $subscription->trial_ends_at->format('M j, Y') }}</small>
                                </div>
                            </div>
                        @endif
                        
                        <div class="timeline-item">
                            <div class="timeline-marker {{ $subscription->end_date->isPast() ? 'bg-danger' : 'bg-primary' }}"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Subscription {{ $subscription->end_date->isPast() ? 'Expired' : 'Expires' }}</h6>
                                <small class="text-muted">{{ $subscription->end_date->format('M j, Y') }}</small>
                            </div>
                        </div>
                        
                        @if($subscription->updated_at != $subscription->created_at)
                            <div class="timeline-item">
                                <div class="timeline-marker bg-secondary"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">Last Updated</h6>
                                    <small class="text-muted">{{ $subscription->updated_at->format('M j, Y g:i A') }}</small>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Cancel Modal -->
<div class="modal fade" id="cancelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Cancel Subscription</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ route('super.subscriptions.cancel', $subscription) }}">
                @csrf
                <div class="modal-body">
                    <p>Are you sure you want to cancel this subscription for <strong>{{ $subscription->organization->name }}</strong>?</p>
                    <div class="mb-3">
                        <label for="cancellation_reason" class="form-label">Reason for cancellation *</label>
                        <textarea class="form-control" id="cancellation_reason" 
                                  name="cancellation_reason" rows="3" required 
                                  placeholder="Please provide a reason for cancellation..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">Cancel Subscription</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Extend Modal -->
<div class="modal fade" id="extendModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Extend Subscription</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ route('super.subscriptions.extend', $subscription) }}">
                @csrf
                <div class="modal-body">
                    <p>Extend subscription for <strong>{{ $subscription->organization->name }}</strong></p>
                    <div class="mb-3">
                        <label for="extend_months" class="form-label">Extend by (months) *</label>
                        <select class="form-select" id="extend_months" name="extend_months" required>
                            <option value="1">1 Month</option>
                            <option value="3">3 Months</option>
                            <option value="6">6 Months</option>
                            <option value="12">12 Months</option>
                        </select>
                    </div>
                    <p class="text-muted">Current end date: {{ $subscription->end_date->format('M j, Y') }}</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-info">Extend Subscription</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: #dee2e6;
}
</style>
@endsection
