@php
    $settings = \App\Models\Setting::first() ?? new \App\Models\Setting;
@endphp
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="{{ $settings->theme_mode }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'Affiliate Portal') - {{ $settings->site_title }} - {{ $settings->app_name }}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ $settings->favicon_url }}">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Bootstrap and jQuery -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- SweetAlert2 -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- International Telephone Input -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/css/intlTelInput.css">

    @production
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    @else
    <script src="https://cdn.tailwindcss.com"></script>
    @endproduction

    <style>
        :root {
            --affiliate-primary: #8e44ad;
            --affiliate-secondary: #e74c3c;
            --affiliate-primary-80: #8e44adcc;
            --affiliate-primary-60: #8e44ad99;
        }

        /* Apply affiliate color scheme */
        .bg-primary {
            background: linear-gradient(135deg, var(--affiliate-primary), var(--affiliate-secondary)) !important;
        }

        .text-primary {
            color: var(--affiliate-primary) !important;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--affiliate-primary), var(--affiliate-secondary)) !important;
            border: none !important;
            border-radius: 10px;
            padding: 12px 24px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #7d3c98, #c0392b) !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(142, 68, 173, 0.3);
        }

        .border-primary {
            border-color: var(--affiliate-primary) !important;
        }

        /* Custom styles for auth pages */
        .auth-container {
            min-height: 100vh;
        }

        .auth-brand-side {
            background: linear-gradient(135deg, var(--affiliate-primary), var(--affiliate-secondary));
        }

        .auth-form-side {
            background-color: #f8f9fa;
        }

        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
            padding: 12px 16px;
        }

        .form-control:focus {
            border-color: var(--affiliate-primary);
            box-shadow: 0 0 0 0.2rem rgba(142, 68, 173, 0.25);
        }

        .form-floating > .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .form-floating > .form-control:focus {
            border-color: var(--affiliate-primary);
            box-shadow: 0 0 0 0.2rem rgba(142, 68, 173, 0.25);
        }

        .btn {
            border-radius: 10px;
            padding: 12px 24px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-secondary {
            background-color: #6c757d;
            border-color: #6c757d;
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
        }
    </style>
</head>
<body class="font-sans antialiased">
    <!-- Impersonation Banner -->
    @include('components.impersonation-banner')

    <!-- Main Content -->
    <main>
        @yield('content')
    </main>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/js/intlTelInput.min.js"></script>
    
    <script>
        // Show body after styles are loaded to prevent FOUC
        document.addEventListener('DOMContentLoaded', function() {
            document.body.style.display = 'block';
        });
    </script>

    @stack('scripts')
</body>
</html>
