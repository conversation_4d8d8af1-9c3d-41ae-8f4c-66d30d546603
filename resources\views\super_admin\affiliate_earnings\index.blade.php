@extends('super_admin.layouts.app')

@section('title', 'Affiliate Earnings')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Affiliate Earnings</h1>
            <p class="text-muted mb-0">Manage and approve affiliate commission earnings</p>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Earnings
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                ${{ number_format($stats['total_earnings'], 2) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Pending Earnings
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                ${{ number_format($stats['pending_earnings'], 2) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Approved Earnings
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                ${{ number_format($stats['approved_earnings'], 2) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Pending Count
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $stats['pending_count'] }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-list fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('super.affiliate-earnings.index') }}">
                <div class="row">
                    <div class="col-md-2 mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select id="status" name="status" class="form-select">
                            <option value="">All Statuses</option>
                            <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                            <option value="approved" {{ request('status') == 'approved' ? 'selected' : '' }}>Approved</option>
                            <option value="paid" {{ request('status') == 'paid' ? 'selected' : '' }}>Paid</option>
                            <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>Rejected</option>
                        </select>
                    </div>

                    <div class="col-md-2 mb-3">
                        <label for="type" class="form-label">Type</label>
                        <select id="type" name="type" class="form-select">
                            <option value="">All Types</option>
                            <option value="commission" {{ request('type') == 'commission' ? 'selected' : '' }}>Commission</option>
                            <option value="bonus" {{ request('type') == 'bonus' ? 'selected' : '' }}>Bonus</option>
                            <option value="adjustment" {{ request('type') == 'adjustment' ? 'selected' : '' }}>Adjustment</option>
                            <option value="penalty" {{ request('type') == 'penalty' ? 'selected' : '' }}>Penalty</option>
                        </select>
                    </div>

                    <div class="col-md-2 mb-3">
                        <label for="search" class="form-label">Search Affiliate</label>
                        <input type="text" id="search" name="search" value="{{ request('search') }}" class="form-control" placeholder="Name or email">
                    </div>

                    <div class="col-md-2 mb-3">
                        <label for="date_from" class="form-label">From Date</label>
                        <input type="date" id="date_from" name="date_from" value="{{ request('date_from') }}" class="form-control">
                    </div>

                    <div class="col-md-2 mb-3">
                        <label for="date_to" class="form-label">To Date</label>
                        <input type="date" id="date_to" name="date_to" value="{{ request('date_to') }}" class="form-control">
                    </div>

                    <div class="col-md-2 mb-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-filter me-2"></i>Filter
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Earnings Table -->
    <div class="card shadow">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Earnings List</h6>
            @if($stats['pending_count'] > 0)
                <div>
                    <button type="button" class="btn btn-success btn-sm me-2" onclick="showBulkApproveModal()">
                        <i class="fas fa-check me-1"></i>Bulk Approve
                    </button>
                    <button type="button" class="btn btn-danger btn-sm" onclick="showBulkRejectModal()">
                        <i class="fas fa-times me-1"></i>Bulk Reject
                    </button>
                </div>
            @endif
        </div>
        <div class="card-body">
            @if($earnings->count() > 0)
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th width="30">
                                    <input type="checkbox" id="select-all">
                                </th>
                                <th>Affiliate</th>
                                <th>Organization</th>
                                <th>Amount</th>
                                <th>Type</th>
                                <th>Status</th>
                                <th>Earned Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($earnings as $earning)
                            <tr>
                                <td>
                                    @if($earning->status === 'pending')
                                        <input type="checkbox" class="earning-checkbox" value="{{ $earning->id }}">
                                    @endif
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ $earning->affiliate->user->name }}</strong>
                                        <br><small class="text-muted">{{ $earning->affiliate->user->email }}</small>
                                    </div>
                                </td>
                                <td>
                                    @if($earning->organization)
                                        {{ $earning->organization->name }}
                                    @else
                                        <span class="text-muted">N/A</span>
                                    @endif
                                </td>
                                <td>
                                    <strong>${{ number_format($earning->amount, 2) }}</strong>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ ucfirst($earning->type) }}</span>
                                </td>
                                <td>
                                    @php
                                        $badgeClass = match($earning->status) {
                                            'pending' => 'bg-warning',
                                            'approved' => 'bg-success',
                                            'paid' => 'bg-primary',
                                            'rejected' => 'bg-danger',
                                            default => 'bg-secondary'
                                        };
                                    @endphp
                                    <span class="badge {{ $badgeClass }}">{{ ucfirst($earning->status) }}</span>
                                </td>
                                <td>
                                    {{ $earning->earned_at ? $earning->earned_at->format('M j, Y') : 'N/A' }}
                                    @if($earning->earned_at)
                                        <br><small class="text-muted">{{ $earning->earned_at->format('g:i A') }}</small>
                                    @endif
                                </td>
                                <td>
                                    <a href="{{ route('super.affiliate-earnings.show', $earning) }}" class="btn btn-sm btn-outline-primary me-1">View</a>

                                    @if($earning->status === 'pending')
                                        <form action="{{ route('super.affiliate-earnings.approve', $earning) }}" method="POST" class="d-inline">
                                            @csrf
                                            <button type="submit" class="btn btn-sm btn-success me-1"
                                                    onclick="return confirm('Approve this earning?')">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        </form>
                                        <button type="button" class="btn btn-sm btn-danger"
                                                onclick="showRejectModal({{ $earning->id }})">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    @endif
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-3">
                    {{ $earnings->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-dollar-sign text-muted" style="font-size: 3rem;"></i>
                    <h4 class="mt-3">No earnings found</h4>
                    <p class="text-muted">No affiliate earnings match your current filters.</p>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Bulk Approve Modal -->
<div class="modal fade" id="bulkApproveModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Bulk Approve Earnings</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ route('super.affiliate-earnings.bulk-approve') }}">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="bulk_approve_notes" class="form-label">Notes (Optional)</label>
                        <textarea class="form-control" id="bulk_approve_notes" name="notes" rows="3"
                                  placeholder="Add any notes for this bulk approval..."></textarea>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Selected earnings will be approved and moved to affiliate available balance.</strong>
                    </div>
                    <input type="hidden" name="earning_ids" id="bulk_approve_ids">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Approve Selected</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Bulk Reject Modal -->
<div class="modal fade" id="bulkRejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Bulk Reject Earnings</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ route('super.affiliate-earnings.bulk-reject') }}">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="bulk_reject_notes" class="form-label">Rejection Reason <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="bulk_reject_notes" name="notes" rows="3" required
                                  placeholder="Please provide a reason for rejecting these earnings..."></textarea>
                    </div>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning:</strong> Selected earnings will be rejected and removed from affiliate balances.
                    </div>
                    <input type="hidden" name="earning_ids" id="bulk_reject_ids">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Reject Selected</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Individual Reject Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Reject Earning</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="rejectForm">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="reject_notes" class="form-label">Rejection Reason <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="reject_notes" name="notes" rows="3" required
                                  placeholder="Please provide a reason for rejecting this earning..."></textarea>
                    </div>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning:</strong> This earning will be rejected and removed from affiliate balance.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Reject Earning</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Select all checkbox functionality
    const selectAllCheckbox = document.getElementById('select-all');
    const earningCheckboxes = document.querySelectorAll('.earning-checkbox');

    selectAllCheckbox?.addEventListener('change', function() {
        earningCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });

    // Update select all when individual checkboxes change
    earningCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const checkedCount = document.querySelectorAll('.earning-checkbox:checked').length;
            selectAllCheckbox.checked = checkedCount === earningCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < earningCheckboxes.length;
        });
    });
});

function showBulkApproveModal() {
    const checkedBoxes = document.querySelectorAll('.earning-checkbox:checked');
    if (checkedBoxes.length === 0) {
        alert('Please select at least one earning to approve.');
        return;
    }

    const earningIds = Array.from(checkedBoxes).map(cb => cb.value);
    document.getElementById('bulk_approve_ids').value = JSON.stringify(earningIds);

    new bootstrap.Modal(document.getElementById('bulkApproveModal')).show();
}

function showBulkRejectModal() {
    const checkedBoxes = document.querySelectorAll('.earning-checkbox:checked');
    if (checkedBoxes.length === 0) {
        alert('Please select at least one earning to reject.');
        return;
    }

    const earningIds = Array.from(checkedBoxes).map(cb => cb.value);
    document.getElementById('bulk_reject_ids').value = JSON.stringify(earningIds);

    new bootstrap.Modal(document.getElementById('bulkRejectModal')).show();
}

function showRejectModal(earningId) {
    const form = document.getElementById('rejectForm');
    form.action = `/super-admin/affiliate-earnings/${earningId}/reject`;

    new bootstrap.Modal(document.getElementById('rejectModal')).show();
}
</script>
@endsection
