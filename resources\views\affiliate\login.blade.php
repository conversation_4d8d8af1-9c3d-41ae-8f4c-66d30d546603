@extends('affiliate.layouts.auth')

@section('title', 'Affiliate Login - Sales Management System')

@section('content')
<div class="container-fluid vh-100">
    <div class="row h-100">
        <!-- Left Side - Branding -->
        <div class="col-lg-6 d-none d-lg-flex align-items-center justify-content-center bg-primary text-white">
            <div class="text-center">
                <div class="mb-4">
                    <i class="fas fa-handshake" style="font-size: 4rem;"></i>
                </div>
                <h2 class="display-4 fw-bold mb-3">Welcome Back, Partner!</h2>
                <p class="lead mb-4">
                    Access your affiliate dashboard to track referrals, monitor earnings,
                    and grow your commission income.
                </p>
                <div class="row text-center">
                    <div class="col-4">
                        <div class="mb-2">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                        <small>Track Referrals</small>
                    </div>
                    <div class="col-4">
                        <div class="mb-2">
                            <i class="fas fa-dollar-sign fa-2x"></i>
                        </div>
                        <small>Earn Commissions</small>
                    </div>
                    <div class="col-4">
                        <div class="mb-2">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                        <small>View Analytics</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Side - Login Form -->
        <div class="col-lg-6 d-flex align-items-center justify-content-center auth-form-side">
            <div class="w-100" style="max-width: 400px;">
                <div class="text-center mb-4">
                    <h3 class="fw-bold text-dark">Affiliate Login</h3>
                    <p class="text-muted">Access your affiliate dashboard</p>
                </div>

                @if ($errors->any())
                <div class="alert alert-danger border-0 rounded-3 mb-4">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <div>
                            <strong>Please fix the following errors:</strong>
                            <ul class="mb-0 mt-2">
                                @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                </div>
                @endif

                <form method="POST" action="{{ route('affiliate.login') }}">
                    @csrf

                    <!-- Email Address -->
                    <div class="form-floating mb-4">
                        <input type="email"
                               class="form-control @error('email') is-invalid @enderror"
                               id="email"
                               name="email"
                               value="{{ old('email') }}"
                               required
                               autofocus
                               autocomplete="username"
                               placeholder="Enter your email address">
                        <label for="email">
                            <i class="fas fa-envelope me-2"></i>Email Address
                        </label>
                        @error('email')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Password -->
                    <div class="form-floating mb-4">
                        <input type="password"
                               class="form-control @error('password') is-invalid @enderror"
                               id="password"
                               name="password"
                               required
                               autocomplete="current-password"
                               placeholder="Enter your password">
                        <label for="password">
                            <i class="fas fa-lock me-2"></i>Password
                        </label>
                        @error('password')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Remember Me -->
                    <div class="mb-4 form-check">
                        <input type="checkbox" class="form-check-input" id="remember" name="remember">
                        <label class="form-check-label" for="remember">
                            Remember me
                        </label>
                    </div>

                    <!-- Submit Button -->
                    <div class="d-grid mb-4">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            Login to Dashboard
                        </button>
                    </div>

                </form>

                <!-- Additional Info -->
                <div class="text-center mt-4 pt-4 border-top">
                    <p class="text-muted mb-2">
                        Don't have an account?
                        <a href="{{ route('affiliate.register') }}" class="text-decoration-none">
                            <strong>Join our affiliate program</strong>
                        </a>
                    </p>
                    <p class="text-muted small mb-0">
                        <a href="{{ route('organization.login') }}" class="text-decoration-none">
                            <i class="fas fa-arrow-left me-1"></i>
                            Back to organization login
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
