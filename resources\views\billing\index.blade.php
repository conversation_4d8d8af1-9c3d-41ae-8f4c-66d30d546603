@extends('layouts.app')

@section('title', 'Billing & Payments')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Billing & Payments</h1>
                <div>
                    <a href="{{ route('billing.payment-accounts') }}" class="btn btn-outline-info me-2">
                        <i class="fas fa-university"></i> Payment Accounts
                    </a>
                    @if($billingSummary['amount_due'] > 0)
                        <a href="{{ route('billing.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Submit Payment
                        </a>
                    @else
                        <a href="{{ route('billing.create') }}" class="btn btn-outline-primary">
                            <i class="fas fa-plus"></i> Submit Payment
                        </a>
                    @endif
                </div>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <!-- Billing Summary -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Current Plan</h6>
                                    <h4 class="mb-0">{{ $billingSummary['current_plan'] }}</h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-box fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Monthly Cost</h6>
                                    <h4 class="mb-0">${{ number_format($billingSummary['monthly_cost'], 2) }}</h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-dollar-sign fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-{{ $billingSummary['amount_due'] > 0 ? 'warning' : 'success' }} text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Amount Due</h6>
                                    <h4 class="mb-0">${{ number_format($billingSummary['amount_due'], 2) }}</h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Total Paid</h6>
                                    <h4 class="mb-0">${{ number_format($billingSummary['total_paid'], 2) }}</h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Subscription Status -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Subscription Status</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Status:</strong>
                                <span class="badge bg-{{ $subscription->status === 'active' ? 'success' : ($subscription->status === 'past_due' ? 'warning' : 'danger') }}">
                                    {{ ucfirst($subscription->status) }}
                                </span>
                            </p>
                            <p><strong>Plan:</strong> {{ $subscription->plan->name }}</p>
                            <p><strong>Start Date:</strong> {{ $subscription->start_date->format('M d, Y') }}</p>
                            <p><strong>End Date:</strong> {{ $subscription->end_date->format('M d, Y') }}</p>
                        </div>
                        <div class="col-md-6">
                            @if($billingSummary['next_billing_date'])
                                <p><strong>Next Billing:</strong> {{ $billingSummary['next_billing_date']->format('M d, Y') }}</p>
                            @endif
                            @if($billingSummary['pending_payments'] > 0)
                                <p><strong>Pending Payments:</strong> ${{ number_format($billingSummary['pending_payments'], 2) }}</p>
                            @endif
                            @if($billingSummary['amount_due'] > 0)
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    You have an outstanding balance of ${{ number_format($billingSummary['amount_due'], 2) }}.
                                    <a href="{{ route('billing.create') }}" class="alert-link">Submit payment now</a>
                                </div>
                            @endif

                            <!-- Always show submit payment button -->
                            <div class="mt-3">
                                <a href="{{ route('billing.create') }}" class="btn btn-{{ $billingSummary['amount_due'] > 0 ? 'warning' : 'outline-primary' }} btn-lg">
                                    <i class="fas fa-credit-card me-2"></i>
                                    {{ $billingSummary['amount_due'] > 0 ? 'Submit Payment Now' : 'Submit Payment' }}
                                </a>
                                <a href="{{ route('billing.payment-accounts') }}" class="btn btn-outline-info btn-lg ms-2">
                                    <i class="fas fa-university me-2"></i>
                                    View Payment Accounts
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment History -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Payment History</h5>
                </div>
                <div class="card-body">
                    @if($paymentHistory->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Reference</th>
                                        <th>Amount</th>
                                        <th>Method</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($paymentHistory as $payment)
                                        <tr>
                                            <td>{{ $payment->payment_date->format('M d, Y') }}</td>
                                            <td>{{ $payment->payment_reference ?: 'N/A' }}</td>
                                            <td>${{ number_format($payment->amount, 2) }}</td>
                                            <td>
                                                <span class="badge bg-secondary">{{ ucfirst(str_replace('_', ' ', $payment->payment_method)) }}</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-{{ $payment->status === 'approved' ? 'success' : ($payment->status === 'pending' ? 'warning' : 'danger') }}">
                                                    {{ ucfirst($payment->status) }}
                                                </span>
                                            </td>
                                            <td>
                                                <a href="{{ route('billing.show', $payment) }}" class="btn btn-sm btn-outline-info">
                                                    <i class="fas fa-eye"></i> View
                                                </a>
                                                @if($payment->isApproved() && $payment->invoice_number)
                                                    <a href="{{ route('billing.invoice.download', $payment) }}" class="btn btn-sm btn-outline-success">
                                                        <i class="fas fa-download"></i> Invoice
                                                    </a>
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        {{ $paymentHistory->links() }}
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                            <h5>No Payment History</h5>
                            <p class="text-muted">Your payment history will appear here once you make payments.</p>
                            @if($billingSummary['amount_due'] > 0)
                                <a href="{{ route('billing.create') }}" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Submit Your First Payment
                                </a>
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
