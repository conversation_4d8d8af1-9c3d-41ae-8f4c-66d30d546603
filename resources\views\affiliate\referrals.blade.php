@extends('affiliate.layouts.app')

@section('title', 'My Referrals')
@section('page-title', 'My Referrals')

@section('content')
<div class="container-fluid">
    <!-- Stats Cards -->
    <div class="row mb-4">
        <!-- Total Referrals -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Referrals
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total_referrals'] ?? 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Referrals -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Active Organizations
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['active_referrals'] ?? 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pending Referrals -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Pending Organizations
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['pending_referrals'] ?? 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Conversion Rate -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Conversion Rate
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['conversion_rate'] ?? 0, 1) }}%</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-percentage fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filter Referrals</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('affiliate.referrals') }}">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select id="status" name="status" class="form-select">
                            <option value="">All Statuses</option>
                            <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>Pending</option>
                            <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                            <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" id="search" name="search" value="{{ request('search') }}" 
                               placeholder="Organization name" class="form-control">
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="date_from" class="form-label">From Date</label>
                        <input type="date" id="date_from" name="date_from" value="{{ request('date_from') }}" 
                               class="form-control">
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="date_to" class="form-label">To Date</label>
                        <input type="date" id="date_to" name="date_to" value="{{ request('date_to') }}" 
                               class="form-control">
                    </div>
                    <div class="col-md-2 mb-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>Filter
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Referrals Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Referral Organizations</h6>
        </div>
        <div class="card-body">
            @if($referrals->count() > 0)
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>Organization</th>
                                <th>Contact</th>
                                <th>Status</th>
                                <th>Registration Date</th>
                                <th>Subscription</th>
                                <th>Earnings</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($referrals as $referral)
                                <tr>
                                    <td>
                                        <div>
                                            <strong>{{ $referral->organization->name ?? 'N/A' }}</strong>
                                            @if($referral->organization)
                                                <br>
                                                <small class="text-muted">{{ $referral->organization->industry ?? 'Not specified' }}</small>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        @if($referral->organization && $referral->organization->users->first())
                                            <div>
                                                {{ $referral->organization->users->first()->name }}
                                                <br>
                                                <small class="text-muted">{{ $referral->organization->users->first()->email }}</small>
                                            </div>
                                        @else
                                            <span class="text-muted">No contact info</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($referral->status === 'converted')
                                            <span class="badge bg-success">Active</span>
                                        @elseif($referral->status === 'pending')
                                            <span class="badge bg-warning">Pending</span>
                                        @else
                                            <span class="badge bg-secondary">{{ ucfirst($referral->status) }}</span>
                                        @endif
                                    </td>
                                    <td>
                                        {{ $referral->registration_date ? $referral->registration_date->format('M d, Y') : 'N/A' }}
                                        @if($referral->registration_date)
                                            <br>
                                            <small class="text-muted">{{ $referral->registration_date->format('h:i A') }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        @if($referral->organization && $referral->organization->subscription)
                                            <span class="badge bg-info">{{ $referral->organization->subscription->plan->name ?? 'Unknown Plan' }}</span>
                                            <br>
                                            <small class="text-muted">${{ number_format($referral->organization->subscription->plan->price ?? 0, 2) }}/month</small>
                                        @else
                                            <span class="text-muted">No subscription</span>
                                        @endif
                                    </td>
                                    <td>
                                        @php
                                            $totalEarnings = $referral->earnings->sum('amount') ?? 0;
                                        @endphp
                                        <strong>${{ number_format($totalEarnings, 2) }}</strong>
                                        @if($referral->earnings->count() > 0)
                                            <br>
                                            <small class="text-success">{{ $referral->earnings->count() }} payments</small>
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center">
                    {{ $referrals->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-users fa-4x text-gray-300 mb-3"></i>
                    <h5 class="text-gray-600">No referrals found</h5>
                    <p class="text-muted">Start sharing your referral links to get your first referrals!</p>
                    <a href="{{ route('affiliate.referral-tools') }}" class="btn btn-primary">
                        <i class="fas fa-link me-2"></i>Get Referral Links
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
