<?php

namespace App\Http\Controllers;

use App\Models\PaymentAccount;
use App\Models\SubscriptionPayment;
use App\Models\Subscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class BillingController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:Organization Owner|Manager');
    }

    /**
     * Display billing dashboard.
     */
    public function index()
    {
        $organization = Auth::user()->organization;
        $subscription = $organization->subscriptions()->latest()->first();
        
        if (!$subscription) {
            return redirect()->route('dashboard')
                           ->with('error', 'No subscription found for your organization.');
        }

        // Get payment accounts for display
        $paymentAccounts = PaymentAccount::active()->orderBy('is_primary', 'desc')->get();
        
        // Get payment history
        $paymentHistory = $subscription->payments()
                                     ->latest('payment_date')
                                     ->paginate(10);

        // Ensure subscription has amount_due set
        if (!$subscription->amount_due || $subscription->amount_due <= 0) {
            $subscription->update(['amount_due' => $subscription->plan->price]);
        }

        // Calculate billing summary
        $billingSummary = [
            'current_plan' => $subscription->plan->name,
            'monthly_cost' => $subscription->plan->price,
            'subscription_status' => $subscription->status,
            'next_billing_date' => $subscription->next_payment_date,
            'amount_due' => $subscription->getOutstandingBalance(),
            'total_paid' => $subscription->calculateTotalPaid(),
            'pending_payments' => $subscription->pendingPayments()->sum('amount'),
        ];

        return view('billing.index', compact(
            'organization',
            'subscription', 
            'paymentAccounts', 
            'paymentHistory',
            'billingSummary'
        ));
    }

    /**
     * Show payment submission form.
     */
    public function create()
    {
        $organization = Auth::user()->organization;
        $subscription = $organization->subscriptions()->latest()->first();
        
        if (!$subscription) {
            return redirect()->route('billing.index')
                           ->with('error', 'No active subscription found.');
        }

        $paymentAccounts = PaymentAccount::active()->orderBy('is_primary', 'desc')->get();
        $outstandingBalance = $subscription->getOutstandingBalance();

        return view('billing.create', compact('subscription', 'paymentAccounts', 'outstandingBalance'));
    }

    /**
     * Store payment submission.
     */
    public function store(Request $request)
    {
        $organization = Auth::user()->organization;
        $subscription = $organization->subscriptions()->latest()->first();
        
        if (!$subscription) {
            return redirect()->route('billing.index')
                           ->with('error', 'No active subscription found.');
        }

        $validated = $request->validate([
            'payment_reference' => 'required|string|max:255',
            'amount' => 'required|numeric|min:0.01',
            'payment_method' => 'required|string|in:bank_transfer,cash,check,mobile_money',
            'payment_date' => 'required|date|before_or_equal:today',
            'notes' => 'nullable|string|max:500',
        ]);

        // Validate amount doesn't exceed outstanding balance
        $outstandingBalance = $subscription->getOutstandingBalance();
        if ($validated['amount'] > $outstandingBalance) {
            return back()->withErrors(['amount' => 'Payment amount cannot exceed outstanding balance.'])
                        ->withInput();
        }

        try {
            SubscriptionPayment::create([
                'subscription_id' => $subscription->id,
                'organization_id' => $organization->id,
                'payment_reference' => $validated['payment_reference'],
                'amount' => $validated['amount'],
                'payment_method' => $validated['payment_method'],
                'payment_date' => $validated['payment_date'],
                'notes' => $validated['notes'],
                'status' => 'pending',
            ]);

            return redirect()->route('billing.index')
                           ->with('success', 'Payment submitted successfully. It will be reviewed by our team.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to submit payment: ' . $e->getMessage()])
                        ->withInput();
        }
    }

    /**
     * Show payment details.
     */
    public function show(SubscriptionPayment $payment)
    {
        $organization = Auth::user()->organization;
        
        // Ensure payment belongs to user's organization
        if ($payment->organization_id !== $organization->id) {
            abort(403, 'Unauthorized access to payment record.');
        }

        return view('billing.show', compact('payment'));
    }

    /**
     * Display payment accounts information.
     */
    public function paymentAccounts()
    {
        $paymentAccounts = PaymentAccount::active()->orderBy('is_primary', 'desc')->get();
        return view('billing.payment_accounts', compact('paymentAccounts'));
    }

    /**
     * Generate invoice for a payment.
     */
    public function generateInvoice(SubscriptionPayment $payment)
    {
        $organization = Auth::user()->organization;
        
        // Ensure payment belongs to user's organization and is approved
        if ($payment->organization_id !== $organization->id) {
            abort(403, 'Unauthorized access to payment record.');
        }

        if (!$payment->isApproved()) {
            return back()->withErrors(['error' => 'Invoice can only be generated for approved payments.']);
        }

        // Generate invoice number if not exists
        if (!$payment->invoice_number) {
            $payment->update([
                'invoice_number' => $payment->generateInvoiceNumber(),
                'invoice_generated_at' => now(),
            ]);
        }

        return view('billing.invoice', compact('payment'));
    }

    /**
     * Download invoice PDF.
     */
    public function downloadInvoice(SubscriptionPayment $payment)
    {
        $organization = Auth::user()->organization;
        
        // Ensure payment belongs to user's organization and is approved
        if ($payment->organization_id !== $organization->id) {
            abort(403, 'Unauthorized access to payment record.');
        }

        if (!$payment->isApproved()) {
            return back()->withErrors(['error' => 'Invoice can only be downloaded for approved payments.']);
        }

        // Generate invoice number if not exists
        if (!$payment->invoice_number) {
            $payment->update([
                'invoice_number' => $payment->generateInvoiceNumber(),
                'invoice_generated_at' => now(),
            ]);
        }

        $pdf = \PDF::loadView('billing.invoice_pdf', compact('payment'));
        
        return $pdf->download("invoice-{$payment->invoice_number}.pdf");
    }
}
